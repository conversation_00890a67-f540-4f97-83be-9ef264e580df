/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddSectionIcon: typeof import('./src/components/icons/AddSectionIcon.vue')['default']
    AddSubTaskIcon: typeof import('./src/components/icons/AddSubTaskIcon.vue')['default']
    ApproveIcon: typeof import('./src/components/icons/ApproveIcon.vue')['default']
    ArrowRight: typeof import('./src/components/icons/ArrowRight.vue')['default']
    AssociateProjection: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/AssociateProjection.vue')['default']
    Attendance_card: typeof import('./src/components/Dashboard_Components/Attendance_card.vue')['default']
    Attendance_Managerview: typeof import('./src/components/Attendence_Components/Attendance_Managerview.vue')['default']
    Attendance_Userview: typeof import('./src/components/Attendence_Components/Attendance_Userview.vue')['default']
    AttendanceCalendar: typeof import('./src/components/Attendence_Components/Manager_view/AttendanceCalendar.vue')['default']
    AttendenceIcon: typeof import('./src/components/icons/AttendenceIcon.vue')['default']
    AttendenceManagement: typeof import('./src/components/AttendenceManagement.vue')['default']
    BottomSelectionBar: typeof import('./src/components/My_Task_Components/BottomSelectionBar.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb.vue')['default']
    CalendarIcon: typeof import('./src/components/icons/CalendarIcon.vue')['default']
    CandidatePipelineCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/CandidatePipelineCard.vue')['default']
    ChevronLeft: typeof import('./src/components/icons/ChevronLeft.vue')['default']
    ChevronRight: typeof import('./src/components/icons/ChevronRight.vue')['default']
    ChristmasFlag: typeof import('./src/components/icons/ChristmasFlag.vue')['default']
    ClientCard: typeof import('./src/components/Project_Overview/Projectoverview_pages/ClientCard.vue')['default']
    ClientInfoCard: typeof import('./src/components/Contract_Components/ClientInfoCard.vue')['default']
    ClockIcon: typeof import('./src/components/icons/ClockIcon.vue')['default']
    Coming_Soon_template: typeof import('./src/components/Coming_Soon_template.vue')['default']
    Company_updates: typeof import('./src/components/Dashboard_Components/Company_updates.vue')['default']
    CompanyRevenue: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/CompanyRevenue.vue')['default']
    Compliance_card: typeof import('./src/components/Dashboard_Components/Compliance_card.vue')['default']
    ContractDetailsModal: typeof import('./src/components/Contract_Components/ContractDetailsModal.vue')['default']
    ContractInfoCard: typeof import('./src/components/Contract_Components/ContractInfoCard.vue')['default']
    ContractsDashboard: typeof import('./src/components/ContractsDashboard.vue')['default']
    ConvertIcon: typeof import('./src/components/icons/ConvertIcon.vue')['default']
    CopyAllIcon: typeof import('./src/components/icons/CopyAllIcon.vue')['default']
    CRM_Dashboards: typeof import('./src/components/CRM_Dashboards.vue')['default']
    CRMDashboard: typeof import('./src/components/Primary_Dashboards_Components/CRMDashboard.vue')['default']
    CrossIcon: typeof import('./src/components/icons/CrossIcon.vue')['default']
    CustomCalendar: typeof import('./src/components/Attendence_Components/User_view/CustomCalendar.vue')['default']
    CustomDropDown: typeof import('./src/components/CustomDropDown.vue')['default']
    DashboardPage: typeof import('./src/components/DashboardPage.vue')['default']
    DealsFunnelChart: typeof import('./src/components/Primary_Dashboards_Components/CRM_pages/DealsFunnelChart.vue')['default']
    DebtorsAgingSummary: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/DebtorsAgingSummary.vue')['default']
    DebtorsDetails: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/DebtorsDetails.vue')['default']
    DeleteIcon: typeof import('./src/components/icons/DeleteIcon.vue')['default']
    DiwaliIcons: typeof import('./src/components/icons/DiwaliIcons.vue')['default']
    DropDownIcon: typeof import('./src/components/icons/DropDownIcon.vue')['default']
    DuedateIcon: typeof import('./src/components/icons/DuedateIcon.vue')['default']
    DuplicateIcon: typeof import('./src/components/icons/DuplicateIcon.vue')['default']
    DussehraIcon: typeof import('./src/components/icons/DussehraIcon.vue')['default']
    EmployeeTenureCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/EmployeeTenureCard.vue')['default']
    ExitInterviewTrendsCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/ExitInterviewTrendsCard.vue')['default']
    ExpenseChart: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/ExpenseChart.vue')['default']
    FillViewIcon: typeof import('./src/components/icons/fillViewIcon.vue')['default']
    FinanceCard: typeof import('./src/components/Contract_Components/FinanceCard.vue')['default']
    FinancialAnalysis: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/FinancialAnalysis.vue')['default']
    FinancialDashboard: typeof import('./src/components/Primary_Dashboards_Components/FinancialDashboard.vue')['default']
    Footer: typeof import('./src/components/Footer.vue')['default']
    Generic_Icon: typeof import('./src/components/icons/Generic_Icon.vue')['default']
    GreenVector: typeof import('./src/components/icons/GreenVector.vue')['default']
    Header: typeof import('./src/components/Header.vue')['default']
    Header_Info: typeof import('./src/components/Dashboard_Components/Header_Info.vue')['default']
    HoliIcon: typeof import('./src/components/icons/HoliIcon.vue')['default']
    HrAnalysisCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/HrAnalysisCard.vue')['default']
    HRDashboard: typeof import('./src/components/Primary_Dashboards_Components/HRDashboard.vue')['default']
    HrmsViews: typeof import('./src/components/HrmsViews.vue')['default']
    ImagePreviewModal: typeof import('./src/components/SiteUpdates_Components/Site_Comparison/ImagePreviewModal.vue')['default']
    Indianflag: typeof import('./src/components/icons/Indianflag.vue')['default']
    LandingDashboard: typeof import('./src/components/Primary_Dashboards_Components/LandingDashboard.vue')['default']
    LandingHeader: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/LandingHeader.vue')['default']
    LeadsDealsChart: typeof import('./src/components/Primary_Dashboards_Components/CRM_pages/LeadsDealsChart.vue')['default']
    Leave_cardinfo: typeof import('./src/components/Dashboard_Components/Leave_cardinfo.vue')['default']
    LeaveBalances: typeof import('./src/components/Leave_Components/User_view/LeaveBalances.vue')['default']
    LeaveCalendar: typeof import('./src/components/Leave_Components/Manager_view/LeaveCalendar.vue')['default']
    LeaveHistory: typeof import('./src/components/Leave_Components/User_view/LeaveHistory.vue')['default']
    LeaveIcons: typeof import('./src/components/icons/LeaveIcons.vue')['default']
    LeaveManagement: typeof import('./src/components/LeaveManagement.vue')['default']
    LeaveRequestForm: typeof import('./src/components/Leave_Components/User_view/LeaveRequestForm.vue')['default']
    LeaveTrendsCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/LeaveTrendsCard.vue')['default']
    Link: typeof import('./src/components/Link.vue')['default']
    LinkageIcon: typeof import('./src/components/icons/LinkageIcon.vue')['default']
    LinkIcon: typeof import('./src/components/icons/LinkIcon.vue')['default']
    Loader: typeof import('./src/components/Loader.vue')['default']
    ManagerLeaveView: typeof import('./src/components/Leave_Components/ManagerLeaveView.vue')['default']
    MetricBox: typeof import('./src/components/Primary_Dashboards_Components/CRM_pages/MetricBox.vue')['default']
    MetricCard: typeof import('./src/components/Primary_Dashboards_Components/CRM_pages/MetricCard.vue')['default']
    MetricsCard: typeof import('./src/components/Project_Overview/Projectoverview_pages/MetricsCard.vue')['default']
    MileStones: typeof import('./src/components/MileStones.vue')['default']
    MilestonesCard: typeof import('./src/components/Project_Overview/Projectoverview_pages/MilestonesCard.vue')['default']
    MilestonesTable: typeof import('./src/components/Contract_Components/MilestonesTable.vue')['default']
    MoveIcon: typeof import('./src/components/icons/MoveIcon.vue')['default']
    My_Task_Views: typeof import('./src/components/My_Task_Views.vue')['default']
    MyTimesheet: typeof import('./src/components/MyTimesheet.vue')['default']
    NationalHoliday: typeof import('./src/components/icons/NationalHoliday.vue')['default']
    NineDotsIcon: typeof import('./src/components/icons/NineDotsIcon.vue')['default']
    OutdoorDutyForm: typeof import('./src/components/Attendence_Components/User_view/OutdoorDutyForm.vue')['default']
    OverviewForm: typeof import('./src/components/Attendence_Components/User_view/OverviewForm.vue')['default']
    OwnerIcon: typeof import('./src/components/icons/OwnerIcon.vue')['default']
    PayrollCostsCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/PayrollCostsCard.vue')['default']
    PencilIcon: typeof import('./src/components/icons/PencilIcon.vue')['default']
    PendingLeaveRequests: typeof import('./src/components/Leave_Components/Manager_view/PendingLeaveRequests.vue')['default']
    PendingRequests: typeof import('./src/components/Leave_Components/User_view/PendingRequests.vue')['default']
    PendingTasks: typeof import('./src/components/Primary_Dashboards_Components/Projects_pages/PendingTasks.vue')['default']
    PLhistoryTable: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/PLhistoryTable.vue')['default']
    PlusButtonIcon: typeof import('./src/components/icons/PlusButtonIcon.vue')['default']
    Primary_Dashboards: typeof import('./src/components/Primary_Dashboards.vue')['default']
    ProfitLossTable: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/ProfitLossTable.vue')['default']
    ProjectDashboardIcon: typeof import('./src/components/icons/ProjectDashboardIcon.vue')['default']
    ProjectLanding: typeof import('./src/components/ProjectLanding.vue')['default']
    ProjectOverviewDashboard: typeof import('./src/components/Project_Overview/ProjectOverviewDashboard.vue')['default']
    ProjectProgress: typeof import('./src/components/Primary_Dashboards_Components/Projects_pages/ProjectProgress.vue')['default']
    Projects_grid: typeof import('./src/components/Dashboard_Components/Projects_grid.vue')['default']
    Projects_table: typeof import('./src/components/Dashboard_Components/Projects_table.vue')['default']
    ProjectsAnalysis: typeof import('./src/components/Primary_Dashboards_Components/Projects_pages/ProjectsAnalysis.vue')['default']
    ProjectsDashboard: typeof import('./src/components/Primary_Dashboards_Components/ProjectsDashboard.vue')['default']
    ProjectViews: typeof import('./src/components/ProjectViews.vue')['default']
    QuarterwiseProjections: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/QuarterwiseProjections.vue')['default']
    RecentActivitySection: typeof import('./src/components/Project_Overview/Projectoverview_pages/RecentActivitySection.vue')['default']
    RecruitmentAnalysisCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/RecruitmentAnalysisCard.vue')['default']
    RedVector: typeof import('./src/components/icons/RedVector.vue')['default']
    RegularizationForm: typeof import('./src/components/Attendence_Components/User_view/RegularizationForm.vue')['default']
    RejectIcon: typeof import('./src/components/icons/RejectIcon.vue')['default']
    RemoveIcons: typeof import('./src/components/icons/RemoveIcons.vue')['default']
    RequestDetailModal: typeof import('./src/components/Attendence_Components/Manager_view/RequestDetailModal.vue')['default']
    RequestsTable: typeof import('./src/components/Attendence_Components/User_view/RequestsTable.vue')['default']
    ResizableHeader: typeof import('./src/components/ResizableHeader.vue')['default']
    ResourceManagementTable: typeof import('./src/components/Contract_Components/ResourceManagementTable.vue')['default']
    RevenueChart: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/RevenueChart.vue')['default']
    RevenueExpensesTrend: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/RevenueExpensesTrend.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SalesAnalysiscard: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/SalesAnalysiscard.vue')['default']
    SemiHumburgerIcon: typeof import('./src/components/icons/SemiHumburgerIcon.vue')['default']
    ServiceBreakupcard: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/ServiceBreakupcard.vue')['default']
    SickLeave: typeof import('./src/components/icons/SickLeave.vue')['default']
    Sidebar: typeof import('./src/components/Sidebar.vue')['default']
    SiteComparisonPanel: typeof import('./src/components/SiteUpdates_Components/Site_Comparison/SiteComparisonPanel.vue')['default']
    SiteUpdates: typeof import('./src/components/SiteUpdates_Components/SiteUpdates.vue')['default']
    SiteUpdatesList: typeof import('./src/components/SiteUpdates_Components/List_View/SiteUpdatesList.vue')['default']
    SiteVisitForm: typeof import('./src/components/Attendence_Components/User_view/SiteVisitForm.vue')['default']
    SmallDropdownIcon: typeof import('./src/components/icons/SmallDropdownIcon.vue')['default']
    SourceOfHiresCard: typeof import('./src/components/Primary_Dashboards_Components/HRMS_pages/SourceOfHiresCard.vue')['default']
    TaskOverlay: typeof import('./src/components/My_Task_Components/TaskOverlay.vue')['default']
    Tasks_sections: typeof import('./src/components/Dashboard_Components/Tasks_sections.vue')['default']
    TasksCard: typeof import('./src/components/Project_Overview/Projectoverview_pages/TasksCard.vue')['default']
    TaskSection: typeof import('./src/components/My_Task_Components/TaskSection.vue')['default']
    TeamChargeability: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/TeamChargeability.vue')['default']
    TeamProjectDistribution: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/TeamProjectDistribution.vue')['default']
    TeamSection: typeof import('./src/components/Project_Overview/Projectoverview_pages/TeamSection.vue')['default']
    TeamTaskView: typeof import('./src/components/My_Task_Components/TeamTaskView.vue')['default']
    TeamTimesheet: typeof import('./src/components/TeamTimesheet.vue')['default']
    ThreeDots: typeof import('./src/components/icons/ThreeDots.vue')['default']
    TimelineSection: typeof import('./src/components/Project_Overview/Projectoverview_pages/TimelineSection.vue')['default']
    Timesheet_card: typeof import('./src/components/Dashboard_Components/Timesheet_card.vue')['default']
    TimesheetDetailView: typeof import('./src/components/TimesheetDetailView.vue')['default']
    TopNavigation: typeof import('./src/components/Leave_Components/User_view/TopNavigation.vue')['default']
    TrailingDropdown: typeof import('./src/components/icons/TrailingDropdown.vue')['default']
    UpcomingHolidays: typeof import('./src/components/Leave_Components/User_view/UpcomingHolidays.vue')['default']
    UpcomingLeaves: typeof import('./src/components/Leave_Components/Manager_view/UpcomingLeaves.vue')['default']
    UpcomingPayrollcard: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/UpcomingPayrollcard.vue')['default']
    UpDownIcon: typeof import('./src/components/icons/UpDownIcon.vue')['default']
    UserIcon: typeof import('./src/components/icons/UserIcon.vue')['default']
    UserLeaveView: typeof import('./src/components/Leave_Components/UserLeaveView.vue')['default']
    UserTaskView: typeof import('./src/components/My_Task_Components/UserTaskView.vue')['default']
    VacationIcons: typeof import('./src/components/icons/VacationIcons.vue')['default']
    VariableManagementTable: typeof import('./src/components/Contract_Components/VariableManagementTable.vue')['default']
    ViewMoreIcon: typeof import('./src/components/icons/ViewMoreIcon.vue')['default']
    WeekRangePicker: typeof import('./src/components/WeekRangePicker.vue')['default']
    WinRateChart: typeof import('./src/components/Primary_Dashboards_Components/CRM_pages/WinRateChart.vue')['default']
    Work_ProgressIcon: typeof import('./src/components/icons/Work_ProgressIcon.vue')['default']
    WorkFromHomeForm: typeof import('./src/components/Attendence_Components/User_view/WorkFromHomeForm.vue')['default']
    YoyCollectionTrend: typeof import('./src/components/Primary_Dashboards_Components/Finance_pages/YoyCollectionTrend.vue')['default']
    YtdCollectioncard: typeof import('./src/components/Primary_Dashboards_Components/Landing_pages/YtdCollectioncard.vue')['default']
  }
}
