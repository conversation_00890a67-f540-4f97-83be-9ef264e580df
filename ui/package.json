{"name": "frappe-ui-frontend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build --base=/assets/inspira/ui/ && yarn copy-html-entry", "preview": "vite preview", "copy-html-entry": "cp ../inspira/public/ui/index.html ../inspira/www/ui.html"}, "dependencies": {"apexcharts": "^4.5.0", "echarts": "^5.6.0", "feather-icons": "^4.29.2", "frappe-ui": "^0.1.109", "vue": "^3.5.12", "vue-router": "^4.4.5", "vue3-apexcharts": "^1.8.0", "date-fns": "^4.1.0", "lucide-vue-next": "^0.483.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.1.4", "autoprefixer": "^10.4.2", "postcss": "^8.4.5", "tailwindcss": "^3.4.14", "typescript": "^5.7.2", "vite": "^5.4.10"}}