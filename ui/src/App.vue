<template>
  <div>
    <GlobalLoader ref="globalLoader" />
    <router-view />
    <Toasts />
  </div>
</template>
<script setup>
import { Toasts } from "frappe-ui"
import { ref, onMounted } from 'vue'
import GlobalLoader from '@/components/GlobalLoader.vue'

const globalLoader = ref(null)
onMounted(() => {
  window.$loader = globalLoader.value
})
// onMounted(() => {
//   window.$loader = globalLoader.value
//   window.$loader.setLoading(true)
//   setTimeout(() => window.$loader.setLoading(false), 3000) 
// })
</script>
