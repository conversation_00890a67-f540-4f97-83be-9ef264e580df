import { ref } from 'vue'
import { useLoader } from '@/utils/useLoader'
const { start, stop } = useLoader()

import { createResource, toast } from 'frappe-ui'

function showToast(title, text, icon, iconClasses) {
  toast({
    title,
    text,
    icon,
    position: 'bottom-right',
    iconClasses,
  })
}

/**
 * Retrieves a document from Frappe using the provided parameters.
 *
 * @param {Object} getDocParams - The parameters to be passed to the Frappe API for getting the document.
 * @param {string} [successMsg] - Optional. The success message to display on successful retrieval.
 * @param {Function} [successFunc] - Optional. The function to call on successful retrieval.
 * @param {string} [errorMsg] - Optional. The error message to display on retrieval error.
 * @param {Function} [errorFunc] - Optional. The function to call on retrieval error.
 * Sample getDocParams: {
    "doctype": "IDP Timesheet",
    "filters": {
      "start_date": "2025-03-17",
      "end_date": "2025-03-23"
    }
  }
 */
export function frappeGetDoc({
  getDocParams,
  successMsg = null,
  successFunc = null,
  errorMsg = null,
  errorFunc = null,
}) {
  const errorShown = ref(false)
  start()
  const handleSuccess = (data) => {
    stop()
    successFunc?.(data)
    if (successMsg) {
      showToast('Success', successMsg, 'check-circle', 'text-green-500')
    }
  }

  const handleError = (error) => {
    stop()
    errorFunc?.(error)
    if (!errorShown.value && errorMsg) {
      errorShown.value = true
      showToast(
        'Error',
        error.messages?.[0] || errorMsg,
        'alert-circle',
        'text-red-500'
      )
    }
  }

  const frappeGetValue = createResource({
    url: 'frappe.client.get',
    onSuccess: handleSuccess,
    onError: handleError,
  })

  frappeGetValue.submit(getDocParams)
}

/**
 * Sets a value in Frappe using the provided parameters.
 *
 * @param {Object} getValueParams - The parameters to be passed to the Frappe API.
 * @param {string} [successMsg] - Optional. The success message to display on successful update.
 * @param {Function} [successFunc] - Optional. The function to call on successful update.
 * @param {string} [errorMsg] - Optional. The error message to display on update error.
 * @param {Function} [errorFunc] - Optional. The function to call on update error.
 * Sample getValueParams: {
        doctype: 'IDP Task',
        name: section.id,
        fieldname: 'subject',
        value: section.name,
    }
 */
export function frappeGetValue({
  getValueParams,
  successMsg = null,
  successFunc = null,
  errorMsg = null,
  errorFunc = null,
}) {
  const errorShown = ref(false)
  start()
  const frappeGetValue = createResource({
    url: 'frappe.client.get_value',
    onSuccess: (data) => {
      stop()
      successFunc?.(data)
      if (successMsg) {
        showToast('Success', successMsg, 'check-circle', 'text-green-500')
      }
    },
    onError: (error) => {
      stop()
      errorFunc?.(error)
      if (!errorShown.value && errorMsg) {
        errorShown.value = true
        showToast(
          'Error',
          error.messages?.[0] || errorMsg,
          'alert-circle',
          'text-red-500'
        )
      }
    },
  })
  frappeGetValue.submit(getValueParams)
}

export function frappeInsertDoc({
  insertDocParams,
  successMsg = null,
  successFunc = null,
  errorMsg = null,
  errorFunc = null,
}) {
  const errorShown = ref(false)
  start()
  const frappeInsertDoc = createResource({
    url: 'frappe.client.insert',
    onSuccess: (data) => {
      stop()
      successFunc?.(data)
      if (successMsg) {
        showToast('Success', successMsg, 'check-circle', 'text-green-500')
      }
    },
    onError: (error) => {
      stop()
      errorFunc?.(error)
      if (!errorShown.value && errorMsg) {
        errorShown.value = true
        showToast(
          'Error',
          error.messages?.[0] || errorMsg,
          'alert-circle',
          'text-red-500'
        )
      }
    },
  })
  frappeInsertDoc.submit(insertDocParams)
}

/**
 * Sets a value in Frappe using the provided parameters.
 *
 * @param {Object} setValueParams - The parameters to be passed to the Frappe API.
 * @param {string} [successMsg] - Optional. The success message to display on successful update.
 * @param {Function} [successFunc] - Optional. The function to call on successful update.
 * @param {string} [errorMsg] - Optional. The error message to display on update error.
 * @param {Function} [errorFunc] - Optional. The function to call on update error.
 * Sample setValueParams: {
        doctype: 'IDP Task',
        name: section.id,
        fieldname: 'subject',
        value: section.name,
    }
 */
export function frappeSetValue({
  setValueParams,
  successMsg = null,
  successFunc = null,
  errorMsg = null,
  errorFunc = null,
}) {
  const errorShown = ref(false)
  start()
  const frappeSetValue = createResource({
    url: 'frappe.client.set_value',
    onSuccess: (data) => {
      stop()
      successFunc?.(data)
      if (successMsg) {
        showToast('Success', successMsg, 'check-circle', 'text-green-500')
      }
    },
    onError: (error) => {
      stop()
      errorFunc?.(error)
      if (!errorShown.value && errorMsg) {
        errorShown.value = true
        showToast(
          'Error',
          error.messages?.[0] || errorMsg,
          'alert-circle',
          'text-red-500'
        )
      }
    },
  })
  frappeSetValue.submit(setValueParams)
}

/**
 * Performs a bulk update operation.
 *
 * @param {Object} bulkUpdateParams - The parameters for the bulk update operation.
 * @param {Function} [successFunc] - Optional. The function to call on successful update.
 * @param {Function} [errorFunc] - Optional. The function to call on update error.
 * @param {string} [successMsg] - Optional. The success message to display on successful update.
 * @param {string} [errorMsg] - Optional. The error message to display on update error.
 * Sample bulkUpdateParams: {
      doctype: 'IDP Task',
      docname: task.id,
      fieldName1: value1,
      fieldName2: value2,
    }
 */
export function frappeBulkUpdate({
  bulkUpdateParams,
  successMsg = null,
  successFunc = null,
  errorMsg = null,
  errorFunc = null,
}) {
  const errorShown = ref(false)
  start()
  const resBulkUpdate = createResource({
    url: 'frappe.client.bulk_update',
    onSuccess(doc) {
      stop()
      successFunc?.(doc)
      if (doc.failed_docs.length) {
        showToast('Error', errorMsg, 'alert-circle', 'text-red-500')
      } else if (successMsg) {
        showToast('Success', successMsg, 'check-circle', 'text-green-500')
      }
    },
    onError(err) {
      stop()
      errorFunc?.(err)
      if (!errorShown.value && errorMsg) {
        errorShown.value = true
        showToast(
          'Error',
          err.messages?.[0] || errorMsg,
          'alert-circle',
          'text-red-500'
        )
      }
    },
  })

  resBulkUpdate.submit({
    docs: JSON.stringify(bulkUpdateParams),
  })
}

export function frappeApplyWorkflow({
  applyWorkflowParams,
  successMsg = null,
  successFunc = null,
  errorMsg = null,
  errorFunc = null,
}) {
  const errorShown = ref(false)
  start()
  const resApplyWorkflow = createResource({
    url: 'inspira.inspira.api.utils.apply_workflow',
    onSuccess(doc) {
      stop()
      successFunc?.(doc)
      if (successMsg) {
        showToast('Success', successMsg, 'check-circle', 'text-green-500')
      }
    },
    onError(err) {
      stop()
      errorFunc?.(err)
      if (!errorShown.value && errorMsg) {
        errorShown.value = true
        showToast(
          'Error',
          err.messages?.[0] || errorMsg,
          'alert-circle',
          'text-red-500'
        )
      }
    },
  })

  resApplyWorkflow.submit(applyWorkflowParams)
}
