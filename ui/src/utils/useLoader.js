export function useLoader() {
  function start() {
    window.$loader?.setLoading(true)
  }
  
  function stop() {
    window.$loader?.setLoading(false)
  }
  
  return { start, stop }
}





// how to use guide by <PERSON> wasim

// Best Practices

// For simple cases: Use directly with window.$loader
// window.$loader?.setLoading(true)
// window.$loader?.setLoading(false)

// For cleaner code: Create a composable useLoader.js:

// import { useLoader } from '@/utils/useLoader'
// const { start, stop } = useLoader()
// async function getData() {
//   start()
//   try {
//   } finally {
//     stop()
//   }
// }