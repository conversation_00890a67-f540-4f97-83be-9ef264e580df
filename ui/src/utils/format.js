import { useTimeAgo } from '@vueuse/core'
import { dayjsLocal, dayjs } from 'frappe-ui'

export function getFormat(
  date,
  format,
  onlyDate = false,
  onlyTime = false,
  withDate = true
) {
  if (!date) return ''
  let dateFormat =
    window.sysdefaults.date_format
      .replace('mm', 'MM')
      .replace('yyyy', 'YYYY')
      .replace('dd', 'DD') || 'YYYY-MM-DD'
  let timeFormat = window.sysdefaults.time_format || 'HH:mm:ss'
  format = format || 'ddd, MMM D, YYYY h:mm a'

  if (onlyDate) format = dateFormat
  if (onlyTime) format = timeFormat
  if (onlyTime && onlyDate) format = `${dateFormat} ${timeFormat}`

  if (withDate) {
    return dayjs(date).format(format)
  }
  return format
}

export function timeAgo(date) {
  return useTimeAgo(date).value
}

export function formatDate(date, format, onlyDate = false, onlyTime = false) {
  if (!date) return ''
  format = getFormat(date, format, onlyDate, onlyTime, false)
  return dayjsLocal(date).format(format)
}
