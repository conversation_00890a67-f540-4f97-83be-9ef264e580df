import './index.css'

import { createApp } from 'vue'
import router from './router'
import App from './App.vue'
import VueApexCharts from 'vue3-apexcharts';

import {
  Button,
  Card,
  Input,
  setConfig,
  frappeRequest,
  resourcesPlugin,
} from 'frappe-ui'

let app = createApp(App)

setConfig('resourceFetcher', frappeRequest)

app.use(router)
app.use(resourcesPlugin)
app.use(VueApexCharts)

app.component('Button', Button)
app.component('Card', Card)
app.component('Input', Input)

if (import.meta.env.DEV) {
  frappeRequest({ url: '/api/method/inspira.www.ui.get_context_for_dev' }).then(
    (values) => {
      for (let key in values) {
        window[key] = values[key]
      }
      app.mount('#app')
    }
  )
} else {
  app.mount('#app')
}
