<template>
  <div class="w-full flex h-screen bg-white">
    <!-- Left side - Login Form (80%) -->
    <div class="w-[80%] flex flex-col p-4">
      <!-- Logo (top left) -->
      <div class="mb-auto">
        <Logo />
      </div>

      <!-- Form Container (centered vertically) -->
      <div class="flex-grow flex items-center justify-center">
        <div class="w-full max-w-md">
          <!-- Login Form -->
          <div v-if="!showForgotPassword">
            <h1 class="text-3xl font-normal mb-12">Welcome to Inspira </h1>

            <form @submit.prevent="submit" class="space-y-6">
              <!-- Email Field -->
              <div class="space-y-1">
                <label for="email" class="text-xs uppercase tracking-wide text-gray-500">Email</label>
                <input id="email" v-model="email" type="text" placeholder="<EMAIL>"
                  class="w-full border-b border-gray-300 py-2 focus:border-purple-500 focus:outline-none transition-colors"
                  required />
                <p v-if="errors.email" class="text-red-500 text-xs mt-1">{{ errors.email }}</p>
              </div>

              <!-- Password Field -->
              <div class="space-y-1">
                <label for="password" class="text-xs uppercase tracking-wide text-gray-500">Password</label>
                <div class="relative">
                  <input id="password" v-model="password" :type="passwordVisible ? 'text' : 'password'"
                    placeholder="••••••"
                    class="w-full border-b border-gray-300 py-2 focus:border-purple-500 focus:outline-none transition-colors pr-10"
                    required />
                  <button type="button" @click="passwordVisible = !passwordVisible"
                    class="absolute inset-y-0 right-0 flex items-center pr-2 text-gray-500 hover:text-purple-500 focus:outline-none"
                    tabindex="-1">
                    <svg v-if="passwordVisible" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                      viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                      stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    <span class="sr-only">
                      {{ passwordVisible ? 'Hide password' : 'Show password' }}
                    </span>
                  </button>
                </div>
                <p v-if="errors.password" class="text-red-500 text-xs mt-1">{{ errors.password }}</p>
              </div>

              <!-- Forgot Password Link -->
              <div class="text-xs text-gray-600">
                <a href="#" @click.prevent="showForgotPassword = true" class="hover:text-purple-500 transition-colors">
                  Have you forgotten your Password?
                </a>
              </div>

              <!-- Login Button -->
              <button type="submit"
                class="w-32 bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 uppercase text-sm tracking-wider transition-colors"
                :disabled="loading">
                <span v-if="!loading">Log in</span>
                <span v-else class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                  Loading
                </span>
              </button>
            </form>
          </div>

          <!-- Forgot Password Form -->
          <div v-else>
            <h1 class="text-3xl font-normal mb-4">Inspira</h1>
            <h2 class="text-sm uppercase tracking-wide font-medium mb-2">Reset Password</h2>
            <p class="text-xs text-gray-600 mb-8">We will send you an e-mail with instructions on how to recover it</p>

            <form @submit.prevent="submitResetPassword" class="space-y-6">
              <!-- Email Field -->
              <div class="space-y-1">
                <label for="reset-email" class="text-xs uppercase tracking-wide text-gray-500">Email</label>
                <input id="reset-email" v-model="resetEmail" type="email"
                  class="w-full border-b border-gray-300 py-2 focus:border-purple-500 focus:outline-none transition-colors"
                  required />
                <p v-if="errors.resetEmail" class="text-red-500 text-xs mt-1">{{ errors.resetEmail }}</p>
              </div>

              <!-- Action Buttons -->
              <div class="flex items-center space-x-4">
                <a href="#" @click.prevent="showForgotPassword = false"
                  class="text-xs uppercase tracking-wide text-gray-500 hover:text-purple-500 transition-colors">
                  Back
                </a>
                <button type="submit"
                  class="bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 uppercase text-xs tracking-wider transition-colors"
                  :disabled="resetLoading">
                  <span v-if="!resetLoading">Submit</span>
                  <span v-else class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg"
                      fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                    Loading
                  </span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Right side - Image (20%) -->
    <!-- <div class="w-[20%] bg-gray-100">
      <img src="../assets/images/Frame_1.png" alt="Wall_Image" class="w-full h-full object-cover">
    </div> -->
    <div class="w-[20%] bg-gray-100 relative overflow-hidden">
      <div class="relative w-full h-full">
        <transition-group name="slide" tag="div" class="w-full h-full relative">
          <img v-for="(image, index) in images" :key="index" :src="image" alt="Slideshow Image"
            class="absolute inset-0 w-full h-full object-cover"
            :class="{ 'opacity-0 z-0': index !== currentImageIndex, 'opacity-100 z-10': index === currentImageIndex }" />
        </transition-group>
      </div>
    </div>


    <!-- Notification -->
    <div v-if="notification.show"
      class="fixed top-16 left-1/4 bg-white shadow-lg rounded p-4 max-w-sm transition-all duration-300" :class="{
        'bg-green-50 border-l-4 border-green-500': notification.type === 'success',
        'bg-red-50 border-l-4 border-red-500': notification.type === 'error'
      }">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg v-if="notification.type === 'success'" class="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          <svg v-else class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium"
            :class="{ 'text-green-800': notification.type === 'success', 'text-red-800': notification.type === 'error' }">
            {{ notification.message }}
          </p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button @click="notification.show = false" class="inline-flex rounded-md p-1.5" :class="{
              'text-green-500 hover:bg-green-100': notification.type === 'success',
              'text-red-500 hover:bg-red-100': notification.type === 'error'
            }">
              <span class="sr-only">Dismiss</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { session } from '../data/session'
import Frame1 from '@/assets/images/Frame_1.png'
import Frame2 from '@/assets/images/Image_3.png'
import Logo from '../assets/images/InspiraLogo.vue'

// Form data
const email = ref('')
const password = ref('')
const resetEmail = ref('')
const showForgotPassword = ref(false)
const loading = ref(false)
const resetLoading = ref(false)
const errors = reactive({
  email: '',
  password: '',
  resetEmail: ''
})

const passwordVisible = ref(false)

// Notification system
const notification = reactive({
  show: false,
  message: '',
  type: 'success',
  timeout: null as number | null
})

function showNotification(message: string, type: 'success' | 'error') {
  if (notification.timeout) {
    clearTimeout(notification.timeout)
  }

  notification.show = true
  notification.message = message
  notification.type = type

  notification.timeout = setTimeout(() => {
    notification.show = false
  }, 5000) as unknown as number
}

function validateEmail(email: string): boolean {
  const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return re.test(email)
}

async function submit() {
  errors.email = ''
  errors.password = ''
  let isValid = true

  if (!email.value) {
    errors.email = 'Email is required'
    isValid = false
  } else if (!validateEmail(email.value)) {
    errors.email = 'Please enter a valid email address'
    isValid = false
  }

  if (!password.value) {
    errors.password = 'Password is required'
    isValid = false
  } else if (password.value.length < 6) {
    errors.password = 'Password must be at least 6 characters'
    isValid = false
  }

  if (!isValid) return

  try {
    loading.value = true

    await session.login.submit({
      email: email.value,
      password: password.value,
    })

    showNotification('Login successful! Redirecting...', 'success')
  } catch (error) {
    showNotification(
      error?.message || 'Invalid email or password. Please try again.',
      'error'
    )
  } finally {
    loading.value = false
  }
}

async function submitResetPassword() {
  errors.resetEmail = ''
  if (!resetEmail.value) {
    errors.resetEmail = 'Email is required'
    return
  } else if (!validateEmail(resetEmail.value)) {
    errors.resetEmail = 'Please enter a valid email address'
    return
  }

  try {
    resetLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 1500))

    showNotification(
      'Password reset instructions have been sent to your email.',
      'success'
    )
    resetEmail.value = ''
    showForgotPassword.value = false

  } catch (error) {
    showNotification(
      error?.message || 'Failed to send reset instructions. Please try again.',
      'error'
    )
  } finally {
    resetLoading.value = false
  }
}
// Extra Feature By Dev "Wasim Ahmed"
const images = [
  Frame1,
  Frame2,
]

const currentImageIndex = ref(0)
let slideshowInterval: number | undefined

const startSlideshow = () => {
  slideshowInterval = setInterval(() => {
    currentImageIndex.value = (currentImageIndex.value + 1) % images.length
  }, 3000)
}

onMounted(() => {
  startSlideshow()
})
onUnmounted(() => {
  if (slideshowInterval) {
    clearInterval(slideshowInterval)
  }
})
</script>
<style scoped>
.slide-enter-active,
.slide-leave-active {
  transition: all 1s cubic-bezier(0.65, 0, 0.35, 1);
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-enter-to {
  transform: translateX(0);
}

.slide-leave-active {
  z-index: 0;
}

.slide-leave-to {
  transform: translateX(-100%);
}

img {
  transition: opacity 1s ease-in-out;
  pointer-events: none;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}
</style>