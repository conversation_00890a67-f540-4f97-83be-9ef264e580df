<template>
  <div>
    <div class="flex flex-col w-full h-full">
      <Header @toggleSidebar="toggleSidebar" class="sticky top-0 z-50 shadow" />
      <div class="flex flex-1">
        <Sidebar :isOpen="isSidebarOpen" style="height: auto !important; min-height: 100vh;" />
        <main :class="['flex-1 p-4 transition-all duration-300',
            isSidebarOpen ? 'w-[calc(100%-11rem)]' : 'w-[calc(100%-3.5rem)]']"
        
        >
          <router-view />
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Header from '../components/Header.vue'
import Sidebar from '../components/Sidebar.vue'

const isSidebarOpen = ref(true)

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value
}
</script>

<style>
html, body, #app {
  height: 100%;
  margin: 0;
  scrollbar-width:none;
  scrollbar-color: rgba(111, 111, 111, 0.2) transparent;
}
</style>