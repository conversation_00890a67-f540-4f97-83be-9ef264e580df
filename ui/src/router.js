import { createRouter, createWebHistory } from 'vue-router'
import { session } from './data/session'
import { userResource } from '@/data/user'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/pages/Home.vue'),
    redirect: '/Dashboard',
    children: [
      { path: '/Dashboard', name: 'Dashboard', component: () => import('@/components/DashboardPage.vue') },
      { path: '/ProjectView/:name', name: 'ProjectView/:name', component: () => import('@/components/ProjectViews.vue') },
      { path: '/Projects', name: 'Projects', component: () => import('@/components/ProjectLanding.vue') },
      { path: '/hrms', name: 'hrms', component: () => import('@/components/HrmsViews.vue') },
      { path: '/Primary_Dashboards', name: 'Primary_Dashboards', component: () => import('@/components/Primary_Dashboards.vue') },
      // {path: '/CRM',name:'CRM', component: () => import('@/components/CRM_Dashboards.vue')},
      { path: '/My_Tasks', name: 'My_Tasks', component: () => import('@/components/My_Task_Views.vue') },
    ]
  },
  {
    name: 'Login',
    path: '/account/login',
    component: () => import('@/pages/Login.vue'),
  },
]

let router = createRouter({
  history: createWebHistory(window.location.pathname.startsWith('/ui') ? '/ui' : '/'),
  routes,
  scrollBehavior(to, from, savedPosition) {
    return { top: 0 }
  }

})

router.beforeEach(async (to, from, next) => {
  let isLoggedIn = session.isLoggedIn
  try {
    await userResource.promise
  } catch (error) {
    isLoggedIn = false
  }

  if (to.name === 'Login' && isLoggedIn) {
    next({ name: 'Home' })
  } else if (to.name !== 'Login' && !isLoggedIn) {
    next({ name: 'Login' })
  } else {
    next()
  }
})

export default router
