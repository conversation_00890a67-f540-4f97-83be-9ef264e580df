<template>
  <div class="flex gap-4 p-2 bg-[#F8F7F9]">
    <div class="flex-1 flex flex-col gap-4">
      <RequisitionStats :stats="statsData" />
      <RequisitionsList :requisitions="requisitionsData" />
    </div>
    <div class="w-96">
      <RequisitionForm :form-data="formData" @submit="handleFormSubmit" @clear="handleFormClear" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import RequisitionStats from './Requisition_Components/RequisitionStats.vue'
import RequisitionsList from './Requisition_Components/RequisitionsList.vue'
import RequisitionForm from './Requisition_Components/RequisitionForm.vue'

// Stats data
const statsData = reactive({
  teamMembers: {
    department: 'Interior Design',
    count: 20
  },
  jobOpenings: {
    title: 'Department Job Openings Fulfilled',
    subtitle: 'Last 6 months',
    percentage: '98.7%'
  },
  currentRequisitions: {
    title: 'Current Requisitions',
    count: '3 filled'
  }
})

// Form data
const formData = reactive({
  designation: 'Junior Designer',
  openings: '5',
  compensation: '₹ 3,00,000',
  department: 'Interior',
  postingDate: '27 July, 2024',
  jobDescription: '',
  reason: '',
  attachedFile: null
})

// Requisitions data
const requisitionsData = ref([
  {
    id: 1,
    title: 'Senior Architect',
    vacancy: 2,
    candidatesInPipeline: 2,
    remaining: 2,
    candidatesRejected: 2,
    candidatesAccepted: 2,
    budgetPerVacancy: '12 LPA',
    nextInterview: '25th March',
    location: 'Mumbai',
    candidates: [
      {
        id: 1,
        name: 'Nidhi Chouhan',
        position: 'Junior Designer',
        email: '<EMAIL>',
        phone: '**********',
        interviewStatus: 'Pending',
        status: 'pipeline'
      },
      {
        id: 2,
        name: 'Rajesh Kumar',
        position: 'Senior Developer',
        email: '<EMAIL>',
        phone: '**********',
        interviewStatus: 'Scheduled',
        status: 'pipeline'
      }
    ]
  },
  {
    id: 2,
    title: 'HR Manager',
    vacancy: 2,
    candidatesInPipeline: 2,
    remaining: 2,
    candidatesRejected: 2,
    candidatesAccepted: 2,
    budgetPerVacancy: '12 LPA',
    nextInterview: '25th March',
    location: 'Mumbai',
    candidates: [
      {
        id: 3,
        name: 'Aisha Patel',
        position: 'Product Manager',
        email: '<EMAIL>',
        phone: '**********',
        interviewStatus: 'Completed',
        status: 'accepted'
      },
      {
        id: 4,
        name: 'Vikram Mehta',
        position: 'UX Researcher',
        email: '<EMAIL>',
        phone: '**********',
        interviewStatus: 'Pending',
        status: 'rejected'
      }
    ]
  },
  {
    id: 3,
    title: 'Jr Designer',
    vacancy: 5,
    candidatesInPipeline: 10,
    remaining: 3,
    candidatesRejected: 1,
    candidatesAccepted: 4,
    budgetPerVacancy: '15 LPA',
    nextInterview: '25th March',
    location: 'Bangalore'
  },
  {
    id: 4,
    title: 'Senior Designer',
    vacancy: 3,
    candidatesInPipeline: 5,
    remaining: 1,
    candidatesRejected: 2,
    candidatesAccepted: 1,
    budgetPerVacancy: '10 LPA',
    nextInterview: '25th March',
    location: 'Hyderabad'
  },
  {
    id: 5,
    title: 'Data Analyst',
    vacancy: 4,
    candidatesInPipeline: 6,
    remaining: 2,
    candidatesRejected: 3,
    candidatesAccepted: 1,
    budgetPerVacancy: '8 LPA',
    nextInterview: '25th March',
    location: 'Pune'
  },
  {
    id: 6,
    title: 'Associate',
    vacancy: 2,
    candidatesInPipeline: 4,
    remaining: 0,
    candidatesRejected: 1,
    candidatesAccepted: 1,
    budgetPerVacancy: '7 LPA',
    nextInterview: '25th March',
    location: 'Delhi'
  },
  {
    id: 7,
    title: 'Junior Architect',
    vacancy: 1,
    candidatesInPipeline: 3,
    remaining: 1,
    candidatesRejected: 0,
    candidatesAccepted: 2,
    budgetPerVacancy: '11 LPA',
    nextInterview: '25th March',
    location: 'Chennai'
  }
])

// Form handlers
const handleFormSubmit = (data) => {
  console.log('Form submitted:', data)
  // Handle form submission logic here
}

const handleFormClear = () => {
  formData.designation = 'Junior Designer'
  formData.openings = '5'
  formData.compensation = '₹ 3,00,000'
  formData.department = 'Interior'
  formData.postingDate = '27 July, 2024'
  formData.jobDescription = ''
  formData.reason = ''
  formData.attachedFile = null
}
</script>