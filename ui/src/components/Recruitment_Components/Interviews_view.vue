<template>
  <div class="flex gap-4 p-2 bg-[#F8F7F9]">
    <div class="flex-1 flex flex-col gap-4">
      <InterviewStats :stats="statsData" />
      <InterviewsList :interviews="interviewsData" @click="handleInterviewClick" />
    </div>
    <div class="w-96">
      <ScheduleInterviewForm v-if="!showFeedback" :form-data="formData" @submit="handleFormSubmit"
        @clear="handleFormClear" />

      <FeedbackForm v-else :is-visible="showFeedback" :interview="selectedInterview || {}" @close="closeFeedback"
        @submit="handleFeedbackSubmit" />
    </div>

  </div>
</template>


<script setup>
import { ref, reactive } from 'vue'
import InterviewStats from './Interview_Components/InterviewStats.vue'
import InterviewsList from './Interview_Components/InterviewsList.vue'
import ScheduleInterviewForm from './Interview_Components/ScheduleInterviewForm.vue'
import FeedbackForm from './Interview_Components/FeedbackForm.vue'

// Stats data
const statsData = reactive({
  hirings: {
    title: 'Hirings fulfilled',
    subtitle: 'Jan 4th - 8th 2025',
    count: '16 of 60 hired'
  },
  offerAcceptance: {
    title: 'Offer acceptance rate',
    subtitle: 'Last 6 months',
    percentage: '98.7%'
  },
  timeToHire: {
    title: 'Time to hire',
    subtitle: 'Last 6 months',
    days: '44 days'
  }
})

// Form data
const formData = reactive({
  interviewers: '',
  applicant: 'Shraddha Kumar',
  scheduledOn: '27 July, 2024',
  fromTime: '09:00 AM',
  toTime: '09:30 AM',
  note: '',
  resume: null
})

// Interviews data
const interviewsData = ref([
  {
    id: 1,
    name: 'Shraddha Kumar',
    position: 'Junior Developer',
    experience: '4 years experience',
    salary: '15 LPA',
    type: 'In person',
    time: '09:00 AM',
    status: 'upcoming',
    feedback: null
  },
  {
    id: 2,
    name: 'Arjun Verma',
    position: 'Senior Developer',
    experience: '8 years experience',
    salary: '22 LPA',
    type: 'Remote',
    time: '10:00 AM',
    status: 'upcoming',
    feedback: null
  },
  {
    id: 3,
    name: 'Meera Singh',
    position: 'Product Designer',
    experience: '5 years experience',
    salary: '12 LPA',
    type: 'Hybrid',
    time: '11:00 AM',
    status: 'upcoming',
    feedback: null
  },
  {
    id: 4,
    name: 'Rohan Patel',
    position: 'Data Analyst',
    experience: '3 years experience',
    salary: '10 LPA',
    type: 'In person',
    time: '09:30 AM',
    status: 'completed',
    feedback: null
  },
  {
    id: 5,
    name: 'Nisha Agarwal',
    position: 'UX Researcher',
    experience: '6 years experience',
    salary: '18 LPA',
    type: 'Remote',
    time: '01:00 PM',
    status: 'completed',
    feedback: null
  },
  {
    id: 6,
    name: 'Devansh Rao',
    position: 'Software Tester',
    experience: '2 years experience',
    salary: '8 LPA',
    type: 'Hybrid',
    time: '02:00 PM',
    status: 'rejected',
    feedback: null
  },
  {
    id: 7,
    name: 'Priya Nair',
    position: 'System Analyst',
    experience: '7 years experience',
    salary: '20 LPA',
    type: 'In person',
    time: '03:00 PM',
    status: 'upcoming',
    feedback: null
  }
])

// Form handlers
const handleFormSubmit = (data) => {
  console.log('Interview scheduled:', data)
  // Handle form submission logic here
}

const handleFormClear = () => {
  formData.interviewers = ''
  formData.applicant = 'Shraddha Kumar'
  formData.scheduledOn = '27 July, 2024'
  formData.fromTime = '09:00 AM'
  formData.toTime = '09:30 AM'
  formData.note = ''
  formData.resume = null
}


const showFeedback = ref(false)
const selectedInterview = ref(null)

const handleInterviewClick = (interview) => {
  selectedInterview.value = interview
  showFeedback.value = true
}

const closeFeedback = () => {
  showFeedback.value = false
  selectedInterview.value = null
}

const handleFeedbackSubmit = (feedbackData) => {
  console.log('Feedback submitted:', feedbackData)
}
</script>