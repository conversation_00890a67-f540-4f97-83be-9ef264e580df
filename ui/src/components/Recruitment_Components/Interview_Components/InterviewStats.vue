<template>
  <div class="flex justify-center gap-4 items-stretch">
    <!-- Hirings Fulfilled -->
    <div class="flex justify-between items-center bg-white w-full rounded-sm p-2 shadow-md">
      <div>
        <div class="text-sm text-gray-600 mb-1">{{ stats.hirings.title }}</div>
        <div class="text-purple-500 text-sm mb-2">{{ stats.hirings.subtitle }}</div>
      </div>
      <div class="text-xl text-gray-600">{{ stats.hirings.count }}</div>
    </div>

    <!-- Offer Acceptance Rate -->
    <div class="flex justify-between items-center bg-white w-full rounded-sm p-2 shadow-md">
      <div>
        <div class="text-sm text-gray-600 mb-1">{{ stats.offerAcceptance.title }}</div>
        <div class="text-purple-500 text-sm mb-2">{{ stats.offerAcceptance.subtitle }}</div>
      </div>
      <div class="text-xl text-gray-600">{{ stats.offerAcceptance.percentage }}</div>
    </div>

    <!-- Time to Hire -->
    <div class="flex flex-col justify-between items-start bg-white w-full rounded-sm p-2 shadow-md">
      <div class="text-sm text-gray-600 mb-1">{{ stats.timeToHire.title }}</div>
      <div class="text-purple-500 text-sm">{{ stats.timeToHire.days }}</div>
    </div>
  </div>
</template>


<script setup>
defineProps({
  stats: {
    type: Object,
    required: true
  }
})
</script>