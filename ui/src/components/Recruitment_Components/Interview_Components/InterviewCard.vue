<template>
  <div class="shadow-md rounded-sm p-2" @click="$emit('click')">
    <div class="flex gap-4 items-center">

      <!-- Avatar -->
      <div class="w-10 h-10">
        <div class="bg-purple-100 rounded-full flex items-center justify-center w-full h-full">
          <UserIcon class="w-5 h-5 text-purple-600" />
        </div>
      </div>

      <!-- Interview Info -->
      <div class="flex-1 flex justify-between items-center gap-4">
        <div class="flex gap-6 items-center">
          <div>
            <h3 class="text-blue-600 font-medium text-sm hover:underline cursor-pointer">
              {{ interview.name }}
            </h3>
            <p class="text-xs text-gray-500 mt-1">{{ interview.position }}</p>
          </div>
          <div class="flex gap-6 text-xs text-gray-600 mt-2">
            <span>{{ interview.experience }}</span>
            <span>{{ interview.salary }}</span>
            <span>{{ interview.type }}</span>
            <span class="font-medium">{{ interview.time }}</span>
          </div>
        </div>

        <!-- Resume Link and Arrow -->
        <div class="flex items-center gap-2">
          <button class="flex items-center gap-1 text-gray-600 hover:text-gray-800 text-xs">
            <span>Resume link</span>
          </button>
          <ChevronRightIcon class="w-4 h-4 text-gray-400" />
        </div>
      </div>

    </div>
  </div>
</template>


<script setup>
import { User as UserIcon, ChevronRight as ChevronRightIcon } from 'lucide-vue-next'

defineProps({
  interview: {
    type: Object,
    required: true
  }
})
</script>