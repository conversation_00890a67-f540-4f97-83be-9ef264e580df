<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Interviews scheduled</h2>

      <!-- Tabs -->
      <div class="flex gap-6">
        <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key"
          class="pb-2 text-sm font-medium relative" :class="[
            activeTab === tab.key
              ? 'text-gray-900 border-b-2 border-gray-900'
              : 'text-gray-500 hover:text-gray-700'
          ]">
          {{ tab.label }}
        </button>
      </div>
    </div>

    <div class="p-4 space-y-4">
      <InterviewCard v-for="interview in filteredInterviews" :key="interview.id" :interview="interview" @click="handleInterviewClick(interview)" />

      <!-- Empty State -->
      <div v-if="filteredInterviews.length === 0" class="text-center py-8">
        <p class="text-gray-500">No interviews found</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import InterviewCard from './InterviewCard.vue'

const props = defineProps({
  interviews: {
    type: Array,
    required: true
  }
})

const activeTab = ref('upcoming')

const tabs = [
  { key: 'upcoming', label: 'Upcoming' },
  { key: 'completed', label: 'Completed' },
  { key: 'rejected', label: 'Rejected' }
]

const filteredInterviews = computed(() => {
  return props.interviews.filter(interview => interview.status === activeTab.value)
})
</script>