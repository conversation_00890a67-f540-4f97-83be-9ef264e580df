<template>
  <div v-if="isVisible" class="w-full bg-white border border-gray-200 h-fit">
    <div class="p-2 border-b bg-[#E6E0E9]">
      <h3 class="text-md text-gray-900">Interview Feedback</h3>
    </div>

    <div class="p-4 space-y-4">
      <div>
        <h4 class="block text-sm text-gray-700 mb-2">Skill Assessment</h4>
        <div class="space-y-4">
          <div v-for="skill in skills" :key="skill.name" class="flex items-center justify-between">
            <div class="flex-1">
              <span class="text-sm text-gray-700">{{ skill.name }}</span>
            </div>
            <div class="flex items-center gap-2">
              <span class="text-xs text-gray-500">Rating</span>
              <div class="flex gap-1">
                <button
                  v-for="star in 5"
                  :key="star"
                  @click="setRating(skill.name, star)"
                  class="focus:outline-none"
                >
                  <StarIcon 
                    class="w-4 h-4 transition-colors"
                    :class="star <= skill.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'"
                  />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <label class="block text-sm text-gray-700 mb-2">Result</label>
        <select 
          v-model="feedbackData.result"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
        >
          <option value="">Select Result</option>
          <option value="selected">Selected</option>
          <option value="rejected">Rejected</option>
        </select>
      </div>

      <div>
        <label class="block text-sm text-gray-700 mb-2">Feedback</label>
        <textarea 
          v-model="feedbackData.feedback"
          placeholder="Type Here"
          rows="6"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent resize-none"
        ></textarea>
      </div>

      <div class="flex gap-3 pt-4 justify-end border-t border-gray-200">
        <button 
          @click="handleClear"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-600 transition-colors"
        >
          Clear
        </button>
        <button 
          @click="handleSubmit"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-700 transition-colors"
        >
          Submit
        </button>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ref, reactive, watch } from 'vue'
import { X as XIcon, Star as StarIcon } from 'lucide-vue-next'

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  interview: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close', 'submit'])

const skills = ref([
  { name: 'Data Structures', rating: 3 },
  { name: 'Problem Solving', rating: 3 },
  { name: 'Database Knowledge', rating: 3 },
  { name: 'Adaptability', rating: 3 }
])

const feedbackData = reactive({
  result: '',
  feedback: '',
  skillRatings: {}
})

// Initialize skill ratings
watch(() => props.isVisible, (newVal) => {
  if (newVal) {
    skills.value.forEach(skill => {
      feedbackData.skillRatings[skill.name] = skill.rating
    })
  }
})

const setRating = (skillName, rating) => {
  const skill = skills.value.find(s => s.name === skillName)
  if (skill) {
    skill.rating = rating
    feedbackData.skillRatings[skillName] = rating
  }
}

const handleClear = () => {
  feedbackData.result = ''
  feedbackData.feedback = ''
  skills.value.forEach(skill => {
    skill.rating = 0
    feedbackData.skillRatings[skill.name] = 0
  })
}

const handleSubmit = () => {
  const submissionData = {
    interviewId: props.interview.id,
    result: feedbackData.result,
    feedback: feedbackData.feedback,
    skillRatings: { ...feedbackData.skillRatings }
  }
  emit('submit', submissionData)
  closeModal()
}

const closeModal = () => {
  emit('close')
}
</script>