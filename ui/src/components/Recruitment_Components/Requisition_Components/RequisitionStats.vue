<template>
  <div class="flex justify-center gap-4 items-stretch">
    <!-- Team Members -->
    <div class="flex justify-between items-center bg-white w-full rounded-sm p-2 shadow-md">
      <div>
        <div class="text-sm text-gray-600 mb-1">Team members</div>
        <div class="text-purple-500 text-sm font-medium mb-2">{{ stats.teamMembers.department }}</div>
      </div>
      <div class="text-xl text-gray-600">{{ stats.teamMembers.count }}</div>
    </div>

    <!-- Job Openings Fulfilled -->
    <div class="flex justify-between items-center bg-white w-full rounded-sm p-2 shadow-md">
      <div>
        <div class="text-sm text-gray-600 mb-1">{{ stats.jobOpenings.title }}</div>
        <div class="text-sm text-purple-500 mb-2">{{ stats.jobOpenings.subtitle }}</div>
      </div>
      <div class="text-xl text-gray-600">{{ stats.jobOpenings.percentage }}</div>
    </div>

    <!-- Current Requisitions -->
    <div class="flex flex-col justify-between items-start bg-white w-full rounded-sm p-2 shadow-md">
      <div class="text-sm text-gray-600 mb-1">{{ stats.currentRequisitions.title }}</div>
      <div class="text-sm  text-purple-500">{{ stats.currentRequisitions.count }}</div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  stats: {
    type: Object,
    required: true
  }
})
</script>