<template>
  <div class="w-full bg-white border border-gray-200 h-fit">
    <!-- Header -->
    <div class="p-2 border-b bg-[#E6E0E9]">
      <h3 class="text-md text-gray-900">Requisition Request</h3>
    </div>

    <!-- Form -->
    <div class="p-4 space-y-4">
      <!-- Designation -->
      <div>
        <label class="block text-sm text-gray-700 mb-2">Designation</label>
        <select 
          v-model="localFormData.designation"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
        >
          <option>Junior Designer</option>
          <option>Senior Designer</option>
          <option>HR Manager</option>
          <option>Data Analyst</option>
          <option>Architect</option>
        </select>
      </div>

      <!-- No of Openings and Expected Compensation -->
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">No of Openings</label>
          <input 
            v-model="localFormData.openings"
            type="text"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Expected Compensation</label>
          <input 
            v-model="localFormData.compensation"
            type="text"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
          />
        </div>
      </div>

      <!-- Department and Posting Date -->
      <div class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Department</label>
          <input 
            v-model="localFormData.department"
            type="text"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Posting Date</label>
          <input 
            v-model="localFormData.postingDate"
            type="text"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
          />
        </div>
      </div>

      <!-- Job Description -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Job Description</label>
        <textarea 
          v-model="localFormData.jobDescription"
          placeholder="Type Here"
          rows="4"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none resize-none"
        ></textarea>
      </div>

      <!-- Reason -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Reason</label>
        <textarea 
          v-model="localFormData.reason"
          placeholder="Type Here"
          rows="3"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none resize-none"
        ></textarea>
      </div>

      <!-- Attach File -->
      <div>
        <div>
          <input 
            type="file" 
            @change="handleFileUpload"
            class="hidden" 
            ref="fileInput"
          />
          <button 
            @click="$refs.fileInput.click()"
            class="bg-[#E6E0E9] p-2 rounded-sm text-sm"
          >
            Attach File
          </button>
          <p class="text-xs text-gray-500 mt-1" v-if="localFormData.attachedFile">
            {{ localFormData.attachedFile.name }}
          </p>
        </div>
      </div>

      <!-- Buttons -->
      <div class="flex gap-3 pt-4 justify-end border-t border-gray-200">
        <button 
          @click="handleClear"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-600 transition-colors"
        >
          Clear
        </button>
        <button 
          @click="handleSubmit"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-700 transition-colors"
        >
          Request
        </button>
      </div>
    </div>
  </div>
</template>


<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['submit', 'clear'])

const localFormData = reactive({ ...props.formData })

// Watch for changes in props and update local data
watch(() => props.formData, (newData) => {
  Object.assign(localFormData, newData)
}, { deep: true })

const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    localFormData.attachedFile = file
  }
}

const handleSubmit = () => {
  emit('submit', { ...localFormData })
}

const handleClear = () => {
  emit('clear')
}
</script>