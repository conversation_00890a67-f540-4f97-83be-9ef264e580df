<template>
  <Transition name="slide">
    <!-- Modal Overlay -->
    <div v-if="isVisible" class="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50"
      @click="closeModal">
      <!-- Modal Content -->
      <div class="bg-white rounded-lg shadow-xl w-[800px] h-[80vh] overflow-hidden" @click.stop>
        <!-- Header with Tabs -->
        <div class="p-6 border-b border-gray-200">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-xl font-semibold text-gray-900">{{ requisition.title }} Candidates</h2>
            <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
              <XIcon class="w-6 h-6" />
            </button>
          </div>

          <!-- Tabs -->
          <div class="flex gap-8">
            <button v-for="tab in tabs" :key="tab.key" @click="activeTab = tab.key"
              class="pb-2 text-sm font-medium relative" :class="[
                activeTab === tab.key
                  ? 'text-gray-900 border-b-2 border-gray-900'
                  : 'text-gray-500 hover:text-gray-700'
              ]">
              {{ tab.label }}
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="p-6 max-h-[60vh] overflow-y-auto">
          <div class="space-y-4">
            <div v-for="candidate in filteredCandidates" :key="candidate.id" class="shadow-md rounded-sm p-2">
              <div class="flex gap-4 items-center">

                <!-- Icon (copied style) -->
                <div class="flex gap-3 items-center w-40">
                  <div class="w-10 h-10">
                    <Requisitions_avatar />
                  </div>
                  <div>
                    <h3 class="text-blue-600 font-medium text-sm hover:underline cursor-pointer">
                      {{ candidate.name }}
                    </h3>
                    <p class="text-xs text-gray-500 mt-1">{{ candidate.position }}</p>
                  </div>
                </div>

                <!-- Email & Phone -->
                <div class="text-sm text-gray-500">
                  <div>Email: {{ candidate.email }}</div>
                  <div class="text-xs mt-1">Telephone: {{ candidate.phone }}</div>
                </div>

                <!-- Interview -->
                <div class="text-sm text-gray-500">
                  <div>Interview Status</div>
                  <div class="mt-1">{{ candidate.interviewStatus }}</div>
                </div>

                <!-- Resume -->
                <div class="flex items-center gap-1 text-sm">
                  <ExternalLinkIcon class="w-3 h-3 text-gray-400" />
                  <span class="text-gray-600">Resume</span>
                </div>

                <!-- Chevron for possible detail expansion -->
                <div class="flex items-center">
                  <ChevronRightIcon class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div v-if="filteredCandidates.length === 0" class="text-center py-8">
            <p class="text-gray-500">No candidates found</p>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed } from 'vue'
import { X as XIcon, ExternalLink as ExternalLinkIcon, ChevronRight as ChevronRightIcon } from 'lucide-vue-next'
import Requisitions_avatar from '../../icons/Requisitions_avatar.vue'

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  requisition: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['close'])

const activeTab = ref('pipeline')

const tabs = [
  { key: 'pipeline', label: 'In Pipeline' },
  { key: 'accepted', label: 'Accepted' },
  { key: 'rejected', label: 'Rejected' }
]

const filteredCandidates = computed(() => {
  if (!props.requisition.candidates) return []

  return props.requisition.candidates.filter(candidate => candidate.status === activeTab.value)
})

const closeModal = () => {
  emit('close')
}
</script>

<style scoped>
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: scale(0.9);
}

.slide-leave-to {
  opacity: 0;
  transform: scale(0.9);
}
</style>

