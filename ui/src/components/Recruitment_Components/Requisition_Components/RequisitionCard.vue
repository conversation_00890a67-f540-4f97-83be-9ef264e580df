<template>
  <div class="shadow-md rounded-sm p-2" @click="$emit('click')">
    <div class="flex gap-4 items-center">


      <div class="flex gap-3 items-center w-40">
        <!-- Icon -->
        <div class="w-10 h-10">
          <Requisitions_avatar />
        </div>

        <div>
          <h3 class="text-blue-600 font-medium text-sm hover:underline cursor-pointer">
            {{ requisition.title }}
          </h3>
          <p class="text-xs text-gray-500 mt-1">Vacancy: {{ requisition.vacancy }}</p>
        </div>
      </div>


      <div>
        <div class="text-sm text-gray-500">
          Candidates in pipeline: {{ requisition.candidatesInPipeline }}
        </div>
        <div class="text-xs text-gray-500 mt-1">
          {{ requisition.remaining }} remaining
        </div>
      </div>

      <div class="text-sm text-gray-500">
        <div class="text-gray-500">Candidates Rejected: {{ requisition.candidatesRejected }}</div>
        <div class="text-gray-500 mt-1">Candidates Accepted: {{ requisition.candidatesAccepted }}</div>
      </div>

      <div class="text-sm text-gray-500">
        <div class="text-gray-500">Budget per Vacancy</div>
        <div class="text-gray-500 mt-1">{{ requisition.budgetPerVacancy }}</div>
      </div>

      <div class="text-sm text-gray-500">
        <div class="text-gray-500">Next Interview</div>
        <div class="text-gray-500 mt-1">{{ requisition.nextInterview }}</div>
      </div>

      <div class="flex items-center gap-1 text-sm">
        <MapPinIcon class="w-3 h-3 text-gray-400" />
        <span class="text-gray-600">{{ requisition.location }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { MapPin as MapPinIcon } from 'lucide-vue-next'
import Requisitions_avatar from '../../icons/Requisitions_avatar.vue'

defineEmits(['click'])

defineProps({
  requisition: {
    type: Object,
    required: true
  }
})
</script>