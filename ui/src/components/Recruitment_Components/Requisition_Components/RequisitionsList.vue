<template>
  <div class="bg-white rounded-sm shadow-sm">
    <div class="p-4 border-b border-gray-200">
      <h2 class="text-lg font-medium text-gray-900">Requisitions</h2>
    </div>
    <div class="p-4 space-y-3">
      <RequisitionCard v-for="requisition in requisitions" :key="requisition.id" :requisition="requisition"
        @click="handleRequisitionClick(requisition)" />
    </div>
    <RequisitionDetails :is-visible="showDetails" :requisition="selectedRequisition || {}" @close="closeDetails" />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import RequisitionCard from './RequisitionCard.vue'
import RequisitionDetails from './RequisitionDetails.vue'

defineProps({
  requisitions: {
    type: Array,
    required: true
  }
})

const showDetails = ref(false)
const selectedRequisition = ref(null)

const handleRequisitionClick = (requisition) => {
  selectedRequisition.value = requisition
  showDetails.value = true
}

const closeDetails = () => {
  showDetails.value = false
  selectedRequisition.value = null
}
</script>