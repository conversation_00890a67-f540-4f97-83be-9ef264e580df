<template>
    <div class="bg-white rounded-lg shadow p-4">
      <div class="text-sm text-gray-600">Win Rate Percentage</div>
      <div class="h-28">
        <apexchart
          type="donut"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return props.data.map(item => item.percentage);
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'donut',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      labels: ['Won', 'Lost'],
      colors: ['#9d94c0', '#e8e7f2'],
      legend: {
        position: 'right',
        fontSize: '12px',
        markers: {
          width: 12,
          height: 12,
          radius: 0
        },
        itemMargin: {
          horizontal: 8,
          vertical: 5
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '70%',
            labels: {
              show: true,
              name: {
                show: true
              },
              value: {
                show: true,
                formatter: function(val) {
                  return val + '%';
                }
              },
              total: {
                show: false,
                label: 'Total',
                formatter: function() {
                  return props.data[0].value + ' Deals Won';
                }
              }
            }
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        width: 2,
        colors: ['#fff']
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + '%';
          }
        }
      },
      annotations: {
        texts: [
          {
            x: '85%',
            y: '15%',
            text: props.data[0].value + ' Deals Won',
            textAnchor: 'middle',
            fontSize: '12px',
            foreColor: '#333'
          },
          {
            x: '60%',
            y: '55%',
            text: props.data[0].percentage + '%',
            textAnchor: 'middle',
            fontSize: '12px',
            fontWeight: 'bold',
            foreColor: '#333'
          }
        ]
      }
    };
  });
  </script>