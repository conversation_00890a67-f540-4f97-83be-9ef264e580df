<template>
  <div ref="chartContainer" style="width: 100%; height: 350px;"></div>
</template>

<script setup>
import { onMounted, ref, watch, defineProps } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Array,
    required: true,
    default: () => []
  }
})

const chartContainer = ref(null)
let chart = null

const colors = ['#483d8b', '#5d4ea6', '#6b5ca5', '#9d94c0', '#dcdbe2']

const initChart = () => {
  if (!chartContainer.value) return
  
  chart = echarts.init(chartContainer.value)
  updateChart()
  
  window.addEventListener('resize', () => {
    chart && chart.resize()
  })
}

const updateChart = () => {
  if (!chart) return
  
  chart.setOption({
    title: {
      text: '',
      left: 'left',
      top: 5,
      textStyle: {
        fontSize: 16
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      show: true,
      orient: 'vertical',
      left: 0,
      top: 'middle',
      data: props.data.map(d => d.name),
      icon: 'box',
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: '#333'
      }
    },
    series: [
      {
        name: 'Deals Pipeline',
        type: 'funnel',
        left: '20%',
        top: 20,
        bottom: 20,
        width: '75%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          formatter: '{c} %',
          color: '#fff',
          fontSize: 10
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        data: props.data.map((d, i) => ({
          name: d.name,
          value: d.percentage,
          itemStyle: {
            color: colors[i % colors.length]
          }
        }))
      }
    ]
  })
}

// Initialize chart when component is mounted
onMounted(() => {
  initChart()
})

// Watch for data changes and update chart
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// Clean up event listeners when component is unmounted
const onUnmounted = () => {
  window.removeEventListener('resize', () => {
    chart && chart.resize()
  })
  chart && chart.dispose()
}
</script>