<template>
    <div class="h-full">
      <apexchart
        type="bar"
        height="100%"
        :options="chartOptions"
        :series="chartSeries"
      ></apexchart>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [
      {
        name: 'Leads',
        data: props.data.leads
      },
      {
        name: 'Deals',
        data: props.data.deals
      }
    ];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        },
        stacked: false
      },
      plotOptions: {
        bar: {
          columnWidth: '60%',
          borderRadius: 0
        }
      },
      colors: ['#6b5ca5', '#9d94c0'],
      dataLabels: {
        enabled: false
      },
      grid: {
        borderColor: '#f1f1f1',
        xaxis: {
          lines: {
            show: false
          }
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      },
      xaxis: {
        categories: props.data.months,
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        min: 0,
        max: 50,
        tickAmount: 5,
        labels: {
          formatter: function(val) {
            return val.toFixed(0);
          }
        }
      },
      legend: {
        position: 'top',
        horizontalAlign: 'right',
        fontSize: '12px',
        markers: {
          width: 12,
          height: 12,
          radius: 0
        },
        itemMargin: {
          horizontal: 8,
          vertical: 5
        }
      },
      tooltip: {
        shared: true,
        intersect: false
      }
    };
  });
  </script>