<template>
  <div class="flex flex-col justify-between items-start bg-white rounded-lg shadow p-4">
    <div class="text-sm text-gray-600">{{ title }}</div>
    <div class="text-3xl font-semibold">{{ value }}</div>
    <div class="flex items-center gap-1">
      <div v-if="showTrend" class="flex items-center text-xs">
        <div class="flex items-center" :class="trend === 'up' ? 'text-green-600' : 'text-red-600'">
          <svg v-if="trend === 'up'" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"
            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-trending-up">
            <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
            <polyline points="16 7 22 7 22 13" />
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-trending-down">
            <polyline points="22 17 13.5 8.5 8.5 13.5 2 7" />
            <polyline points="16 17 22 17 22 11" />
          </svg>
          <span class="ml-1">{{ change > 0 ? '+' : '' }}{{ change }}%</span>
        </div>
      </div>
      <div v-if="changeLabel" class="text-xs text-gray-500 ">{{ changeLabel }}</div>

    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  change: {
    type: Number,
    default: 0
  },
  changeLabel: {
    type: String,
    default: ''
  },
  trend: {
    type: String,
    default: 'up',
    validator: (value) => ['up', 'down'].includes(value)
  },
  showTrend: {
    type: Boolean,
    default: true
  }
});
</script>