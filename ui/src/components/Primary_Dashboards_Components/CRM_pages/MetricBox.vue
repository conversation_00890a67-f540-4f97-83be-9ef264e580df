<template>
  <div class="flex flex-col justify-around bg-white border rounded-lg p-4">
    <div class="text-sm text-gray-600">{{ title }}</div>
    <div class="flex items-baseline mt-1">
      <div class="text-2xl font-semibold">{{ value }}</div>
      <div v-if="showTrend && change !== 0" class="ml-2 flex items-center text-xs">
        <div class="flex items-center text-green-600">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
            class="lucide lucide-trending-up">
            <polyline points="22 7 13.5 15.5 8.5 10.5 2 17" />
            <polyline points="16 7 22 7 22 13" />
          </svg>
          <span class="ml-1">+{{ change }}%</span>
        </div>
      </div>
    </div>
    <div v-if="changeLabel" class="text-xs text-gray-500 mt-1">{{ changeLabel }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: [String, Number],
    required: true
  },
  change: {
    type: Number,
    default: 0
  },
  changeLabel: {
    type: String,
    default: ''
  },
  showTrend: {
    type: Boolean,
    default: true
  }
});
</script>