<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-sm text-gray-500 mb-2">{{ title }}</h2>
      <div class="text-3xl font-medium text-start mt-4">
        {{ value }}{{ suffix }}
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    title: {
      type: String,
      required: true
    },
    value: {
      type: [Number, String],
      required: true
    },
    suffix: {
      type: String,
      default: ''
    }
  });
  </script>