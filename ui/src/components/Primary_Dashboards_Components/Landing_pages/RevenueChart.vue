<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-base font-medium text-gray-700 mb-2">Revenue</h2>
      <div class="text-sm text-gray-500 mb-4">Year To Date</div>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Revenue',
      data: props.data.map(item => item.value)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          columnWidth: '60%',
          borderRadius: 0
        }
      },
      colors: ['#6b5ca5'],
      dataLabels: {
        enabled: false
      },
      grid: {
        borderColor: '#e0e0e0',
        strokeDashArray: 2
      },
      xaxis: {
        categories: props.data.map(item => item.month),
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Cr',
          style: {
            fontSize: '12px'
          }
        },
        min: 0,
        max: 8,
        tickAmount: 4
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + ' Cr';
          }
        }
      }
    };
  });
  </script>