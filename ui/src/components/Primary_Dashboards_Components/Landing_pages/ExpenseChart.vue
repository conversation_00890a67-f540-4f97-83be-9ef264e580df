<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-base font-medium text-gray-700 mb-4">Expense Category Breakup</h2>
      <div class="h-64">
        <apexchart
          type="donut"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return props.data.map(item => item.percentage);
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'donut',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      labels: props.data.map(item => item.category),
      colors: ['#1e0e4b', '#3a1e6d', '#6b5ca5', '#9d94c0', '#b7b6db', '#d1d0e6', '#e8e7f2'],
      legend: {
        position: 'right',
        fontSize: '12px',
        markers: {
          width: 12,
          height: 12,
          radius: 0
        },
        itemMargin: {
          horizontal: 8,
          vertical: 5
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '60%',
            labels: {
              show: true,
              name: {
                show: true
              },
              value: {
                show: true,
                formatter: function(val) {
                  return val + '%';
                }
              }
            }
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val + '%';
        },
        style: {
          fontSize: '10px'
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        width: 2,
        colors: ['#fff']
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + '%';
          }
        }
      }
    };
  });
  </script>