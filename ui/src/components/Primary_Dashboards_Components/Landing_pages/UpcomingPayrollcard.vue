<template>
  <!-- Upcoming Payroll Card -->
  <div class="flex justify-between bg-white rounded-lg shadow p-3 border border-gray-200 mb-4">
    <h2 class="text-sm text-gray-500">Upcoming Payroll</h2>
    <p class=" bg-yellow-100 text-yellow-800 text-xs px-2 py-0.5 rounded-full">
      Under Review
    </p>
  </div>

  <!-- Deals Card -->
  <div class="rounded-lg relative col-span-1 shadow-xl">
    <div class="relative w-full h-32 overflow-hidden rounded-lg">
      <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 200 100" preserveAspectRatio="none">
        <polygon points="0,0 200,0 0,100" class="fill-[#6D5C904D]" />
        <polygon points="200,0 200,100 0,100" class="fill-[#B7B7B74D]" />
      </svg>
      <div class="absolute top-2 left-4 text-gray-700 text-sm">Interior Deals</div>
      <div class="absolute top-8 left-4 text-2xl font-medium">{{ data.interiorDeals }}</div>
      <div class="absolute bottom-2 right-4 text-gray-700 text-sm">Architecture Deals</div>
      <div class="absolute bottom-8 right-4 text-2xl font-medium">{{ data.architectureDeals }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  data: {
    type: Object,
    required: true
  }
});
</script>

<style scoped>
.fill-gray-200 {
  fill: #E5E7EB;
}

.fill-gray-100 {
  fill: #F3F4F6;
}
</style>
