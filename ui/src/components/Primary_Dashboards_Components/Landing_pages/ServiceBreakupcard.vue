<template>
    <div class="bg-white rounded-lg shadow p-4 col-span-1">
      <h2 class="text-sm text-gray-500 mb-4">Service Based Sales Breakup</h2>
      <div class="h-32">
        <apexchart
          type="donut"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [
      props.data.Architecture,
      props.data.Interior
    ];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'donut',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      labels: ['Fee for Architecture', 'Fee for Interior'],
      colors: ['#6b5ca5', '#b7b6db'],
      legend: {
        show: false
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val + '%';
        },
        style: {
          fontSize: '10px'
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '65%',
            labels: {
              show: false
            }
          }
        }
      },
      stroke: {
        width: 0
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + '%';
          }
        }
      }
    };
  });
  </script>