<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-sm text-gray-500 mb-6">YTD Collection - FY 2425</h2>
      <div class="space-y-4">
        <div>
          <div class="flex justify-between items-center text-sm">
            <span class="text-sm">Invoiced YTD</span>
            <span>{{ formatCurrency(data.invoiced) }} Lakhs</span>
          </div>
          <div class="flex items-center text-xs text-green-600 mt-1">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-trending-up">
              <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/>
              <polyline points="16 7 22 7 22 13"/>
            </svg>
            <span class="ml-1">+{{ data.growthPercentage }}% Compared to last FY</span>
          </div>
        </div>
        <div>
          <div class="flex justify-between items-center text-sm">
            <span class="text-sm">Collected YTD</span>
            <span>{{ formatCurrency(data.collected) }} Lakhs</span>
          </div>
        </div>
        <div>
          <div class="flex justify-between items-center text-sm">
            <span class="text-sm"># of Active Projects</span>
            <span>{{ data.projects }}</span>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  
  const formatCurrency = (value) => {
    // return value.toLocaleString('en-IN');
    return value
  };
  </script>