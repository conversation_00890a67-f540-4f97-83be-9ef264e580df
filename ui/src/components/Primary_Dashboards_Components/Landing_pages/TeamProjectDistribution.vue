<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-base font-medium text-gray-700 mb-4">Team-wise Project No. Distribution</h2>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Projects',
      data: props.data.map(item => item.count)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          columnWidth: '60%',
          borderRadius: 0,
          dataLabels: {
            position: 'top'
          }
        }
      },
      colors: ['#6b5ca5'],
      dataLabels: {
        enabled: true,
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#304758']
        }
      },
      grid: {
        borderColor: '#e0e0e0',
        strokeDashArray: 2
      },
      xaxis: {
        categories: props.data.map(item => item.name),
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        min: 0,
        max: 50,
        tickAmount: 5
      }
    };
  });
  </script>