  <template>
  <div class="bg-white rounded-lg shadow p-4">
    <div class="flex items-center justify-between mb-4">
      <h2 class="text-base font-medium text-gray-700">
        P&L - Current FY & Historical
      </h2>
      <button
        @click="generateReport"
        class=" text-white px-3 py-1 rounded hover:bg-gray-800 text-sm"
        style="background-color: #6b5ca5;"
      >
        Generate New
      </button>
    </div>


    <div class="overflow-x-auto">
      <table class="min-w-full">
        <thead>
          <tr class="bg-gray-100">
            <th
              v-for="(col, index) in visibleColumns"
              :key="index"
              class="py-2 px-3 text-sm font-medium text-gray-700"
              :class="['py-2 px-3 text-sm font-medium text-gray-700', ['account', 'account_name'].includes(col.fieldname) ? 'text-left' : 'text-right']"
            >
              {{ col.label }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(row, rowIndex) in rows"
            :key="rowIndex"
            class="border-b"
          >
            <td
              v-for="(col, colIndex) in visibleColumns"
              :key="colIndex"
              class="py-2 px-3 text-sm text-gray-700"
              :class="['py-2 px-3 text-sm font-medium text-gray-700', ['account', 'account_name'].includes(col.fieldname) ? 'text-left' : 'text-right']"
            >
              {{ formatValue(row[col.fieldname]) }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { defineProps, computed } from 'vue';
import { createResource ,toast} from 'frappe-ui';
const props = defineProps({
  columns: Array,
  rows: Array
});

// Filter out hidden columns
const visibleColumns = computed(() =>
  (props.columns || []).filter(col => !col.hidden)
);
function formatValue(val) {
  if (typeof val === 'number') {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(val);
  }
  if (typeof val === 'string') {
    // Remove single quotes
    return val.replace(/^'+|'+$/g, '').trim();
  }
  return val ?? '-';
}

function generateReport(){
  prepared_profit_and_loss_statement_report()
}
function prepared_profit_and_loss_statement_report(){
    createResource({
      url:"inspira.inspira.api.dashboards.landing.prepared_profit_and_loss_statement_report",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        toast({
            title: 'Success',
            text: `Report initiated`,
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
      },
      onError:(error)=>{
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Error while initing report',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      }
    })
  }
</script>
