<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-sm text-gray-500 mb-6">Sales Analysis - FY 2526</h2>
      <div class="space-y-4">
        <div>
          <div class="flex justify-between text-sm items-center">
            <span class="text-sm">Customers Added</span>
            <span>{{ data.customersAdded }}</span>
          </div>
        </div>
        <div>
          <div class="flex justify-between text-sm items-center">
            <span class="text-sm">Revenue Generated</span>
            <span>{{ data.revenueGenerated }} Cr</span>
          </div>
        </div>
        <div>
          <div class="flex justify-between text-sm items-center">
            <span class="text-sm">Total Square Feet</span>
            <span>{{ formatNumber(data.totalSquareFeet) }}</span>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  
  const formatNumber = (value) => {
    // return value.toLocaleString('en-IN');
    return value
  };
  </script>