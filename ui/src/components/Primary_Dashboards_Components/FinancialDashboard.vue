<template>
  <div class="w-full h-full p-4">
    <div class="flex items-center gap-2 mb-4">
      <div>
        <ProjectDashboardIcon class="w-4 h-4" />
      </div>
      <h1 class="text-lg font-medium">Finance Dashboard</h1>
    </div>

    <div class="w-full h-full flex gap-4 mb-4">
      <div class="w-[40%]">
        <FinancialAnalysis :ytdData="ytdData" :serviceBreakupData="serviceBreakupData"
          :profitabilityData="profitabilityData" :projectionsData="projectionsData"
          :chargeabilityData="chargeabilityData" :expenseData="expenseData" :debtorsData="debtorsData"
          :expenseCategoryData="expenseCategoryData" />
      </div>

      <div class="w-[60%] flex flex-col gap-4">

        <div class="w-full flex gap-4 h-1/2">
          <div class="w-1/2 h-full">
            <RevenueExpensesTrend :data="revenueExpensesTrendData" class="h-full" />
          </div>
          <div class="w-1/2 h-full">
            <QuarterwiseProjections :data="quarterwiseProjectionsData" class="h-full" />
          </div>
        </div>

        <div class="w-full flex gap-4 h-1/2">
          <div class="w-1/2 h-full">
            <CompanyRevenue :data="companyRevenueData" class="h-full" />
          </div>
          <div class="w-1/2 h-full">
            <ProfitLossTable :data="profitLossData" class="h-full" />
          </div>

        </div>
      </div>

    </div>


    <div class="w-full flex justify-between gap-4 mb-4">
      <DebtorsAgingSummary :data="debtorsAgingData" class="w-[40%]" />
      <DebtorsDetails :data="debtorsDetailsData" class="w-[60%]" />
    </div>


    <div class="flex flex-col flex-wrap gap-4">
      <AssociateProjection :data="associateProjectionData" />
      <YoyCollectionTrend :data="yoyCollectionData" />
      <TeamChargeability :data="teamChargeabilityData" />
    </div>
  </div>
</template>


<script setup>
import { onMounted, ref } from 'vue';
import FinancialAnalysis from './Finance_pages/FinancialAnalysis.vue';
import RevenueExpensesTrend from './Finance_pages/RevenueExpensesTrend.vue';
import CompanyRevenue from './Finance_pages/CompanyRevenue.vue';
import DebtorsDetails from './Finance_pages/DebtorsDetails.vue';
import QuarterwiseProjections from './Finance_pages/QuarterwiseProjections.vue';
import ProfitLossTable from './Finance_pages/ProfitLossTable.vue';
import DebtorsAgingSummary from './Finance_pages/DebtorsAgingSummary.vue';
import AssociateProjection from './Finance_pages/AssociateProjection.vue';
import YoyCollectionTrend from './Finance_pages/YoyCollectionTrend.vue';
import TeamChargeability from './Finance_pages/TeamChargeability.vue';
import ProjectDashboardIcon from '../icons/ProjectDashboardIcon.vue'
import { createResource } from 'frappe-ui';


// Financial Analysis Data
const ytdData = ref({
  collection: {
    title: 'YTD Collection - AM 25',
    value: '1500',
    unit: 'Lakhs',
    trend: '+8%',
    trendLabel: 'Compared to last FY',
    trendPositive: true
  },
  invoiced: {
    title: 'Invoiced YTD',
    value: '1500',
    unit: 'Lakhs'
  },
  collected: {
    title: 'Collected YTD',
    value: '1200',
    unit: 'Lakhs'
  },
  projects: {
    title: '# of Projects',
    value: '130',
    unit: ''
  }
});

const serviceBreakupData = ref([
  // { category: 'Architecture', percentage: 85 },
  // { category: 'Interior', percentage: 15 }
]);

const profitabilityData = ref({
  title: 'Profitability YTD',
  value: '80',
  unit: '%',
  trend: '+8%',
  trendLabel: 'Compared to last FY',
  trendPositive: true
});

const projectionsData = ref({
  forecast: {
    title: 'Projections Forecast',
    value: '300',
    unit: 'Lakhs'
  },
  monthly: {
    title: 'Feb-25 Projections',
    value: '100',
    unit: 'Lakhs'
  }
});

const chargeabilityData = ref({
  title: 'Chargeability YTD',
  value: '1.6',
  unit: '',
  trend: '-0.3%',
  trendLabel: 'Compared to last FY',
  trendPositive: false
});

const expenseData = ref({
  title: 'Expense Incurred',
  value: '100.1',
  unit: 'Lakhs',
  trend: '+9%',
  trendLabel: 'Compared to last FY',
  trendPositive: false
});

const debtorsData = ref({
  title: 'Total Debtors',
  value: '300',
  unit: 'Lakhs',
  trend: '+14%',
  trendLabel: 'Compared to last FY',
  trendPositive: false
});

const expenseCategoryData = ref([
  // { category: 'Professional Fees', percentage: 25 },
  // { category: 'Rent', percentage: 8 },
  // { category: 'Finance Cost / EMI', percentage: 8 },
  // { category: 'Electricity Charges', percentage: 8 },
  // { category: 'Active', percentage: 25 },
  // { category: 'Other Expenses', percentage: 19 },
  // { category: 'IT Cost', percentage: 7 }
]);

// const revenueExpensesTrendData = ref({
//   years: ['2021', '2022', '2023', '2024', '2025'],
//   revenue: [3, 5, 7, 9, 11],
//   expenses: [2, 3, 4, 5, 6],
//   profit: [1, 2, 3, 4, 5]
// });
const revenueExpensesTrendData = ref({
  years: [],
  revenue: [],
  expenses: [],
  profit: []
});

const quarterwiseProjectionsData = ref([
  // { quarter: 'Q1', value: 450 },
  // { quarter: 'Q2', value: 520 },
  // { quarter: 'Q3', value: 600 },
  // { quarter: 'Q4', value: 680 }
]);

const companyRevenueData = ref([
  // { month: 'Apr', value: 4 },
  // { month: 'May', value: 5 },
  // { month: 'Jun', value: 6 },
  // { month: 'Jul', value: 7 },
  // { month: 'Aug', value: 8 },
  // { month: 'Sep', value: 9 },
  // { month: 'Oct', value: 8 },
  // { month: 'Nov', value: 7 },
  // { month: 'Dec', value: 9 },
  // { month: 'Jan', value: 10 },
  // { month: 'Feb', value: 11 },
  // { month: 'Mar', value: 12 }
]);

const profitLossData = ref({
  // headers: ['Particular', '20-21', '21-22', '22-23'],
  // rows: [
  //   { item: 'Sales', values: [400, 500, 600] },
  //   { item: 'Expenses', values: [200, 300, 400] },
  //   { item: 'Profit Before Taxes', values: [200, 200, 200] },
  //   { item: 'Depreciation', values: [8, 9, 10] },
  //   { item: 'Taxes', values: [30, 32, 35] },
  //   { item: 'Profit After Taxes', values: [162, 159, 155] },
  //   { item: 'Profit After Taxes %', values: [40.5, 27, 25.8] }
  // ]
});

const debtorsAgingData = ref([
  //{ range: '0-30', value: 150 },
  // { range: '30-90', value: 250 },
  // { range: '90-180', value: 350 },
  // { range: 'More than 180', value: 250 }
]);

const debtorsDetailsData = ref([
  // { date: '04 Mar 2024', customer: 'Kiran Mandal', associate: 'Kiran Mandal', fee: 6.50, gst: 6.50, total: 6.50 },
  // { date: '08 Mar 2024', customer: 'Sheetal Patil', associate: 'Sheetal Patil', fee: 6.50, gst: 6.50, total: 6.50 },
  // { date: '04 Mar 2024', customer: 'Disha Kumar', associate: 'Disha Kumar', fee: 4.55, gst: 4.55, total: 4.55 },
  // { date: '04 Mar 2024', customer: 'Ruha Luthra', associate: 'Ruha Luthra', fee: 2.50, gst: 2.50, total: 2.50 },
  // { date: '04 Mar 2024', customer: 'Sheetal Patil', associate: 'Sheetal Patil', fee: 2.50, gst: 2.50, total: 2.50 },
  // { date: '08 Mar 2024', customer: 'Diya Grewal', associate: 'Diya Grewal', fee: 4.50, gst: 4.50, total: 4.50 }
]);

const associateProjectionData = ref([
  // { name: 'Arun', value: 3.5 },
  // { name: 'Abhishek', value: 3.2 },
  // { name: 'Deepak', value: 2.7 },
  // { name: 'Pratik', value: 2.0 },
  // { name: 'Shashank', value: 3.5 },
  // { name: 'Rishabh', value: 3.6 },
  // { name: 'Dhruva', value: 3.2 },
  // { name: 'Ishan', value: 3.3 },
  // { name: 'KrishnaMurti', value: 2.6 },
  // { name: 'Ayan', value: 2.0 },
  // { name: 'Riya', value: 2.5 },
  // { name: 'Prajyot', value: 2.6 },
  // { name: 'Ashok', value: 2.2 }
]);

const yoyCollectionData = ref({
  months: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
  fy2223: [90, 90, 85, 90, 80, 85, 75, 85, 90, 90, 85, 55],
  fy2324: [65, 70, 75, 65, 70, 75, 70, 70, 55, 65, 65, 75],
  fy2425: [75, 65, 65, 70, 65, 70, 65, 70, 75, 70, 75, 70]
});
// const yoyCollectionData = ref({
//   months: [],
//   fy2223: [],
//   fy2324: [],
//   fy2425: []
// });

const teamChargeabilityData = ref([
  { name: 'Arun', value: 1.75 },
  { name: 'Abhishek', value: 1.65 },
  { name: 'Deepak', value: 1.55 },
  { name: 'Pratik', value: 1.4 },
  { name: 'Shashank', value: 1.75 },
  { name: 'Rishabh', value: 1.75 },
  { name: 'Dhruva', value: 1.65 },
  { name: 'Ishan', value: 1.65 },
  { name: 'KrishnaMurti', value: 1.55 },
  { name: 'Ayan', value: 1.4 },
  { name: 'Riya', value: 1.55 },
  { name: 'Prajyot', value: 1.55 },
  { name: 'Ashok', value: 1.45 }
]);
function get_landing_data(){
    createResource({
      url:"inspira.inspira.api.dashboards.landing.get_landing_data",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        // console.log("res",res)
        
        const raw = res.serviceBreakup || {};
        const formatted = Object.entries(raw).map(([key, value]) => ({
          category: key,
          percentage: value
        }));
        serviceBreakupData.value = formatted;
        expenseCategoryData.value = res.expenseCategoryChart
        
      },
      onError:(error)=>{
        console.log(error)
      }
    })
  }
  function profit_and_loss_statement(){
    createResource({
      url:"inspira.inspira.api.dashboards.landing.profit_and_loss_statement",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        if (Array.isArray(res) && res.length > 1) {
          // P&L - Current FY & Historical

          const columns = res[0]; // Array of column definitions
          const rawRows = res[1]; // Array of row data

          // 1. Get year columns (excluding 'account', 'currency', etc.)
          const yearFields = columns.filter(col => col.fieldname?.startsWith('mar_'));
          const headers = yearFields.map(col => col.label);  // ['2020-2021', '2021-2022', ...]

          // 2. Transform each row
          const rows = rawRows
            .filter(row => row && row.account) // remove null/empty rows
            .map(row => ({
              item: row.account_name?.replace(/^'+|'+$/g, '') || row.account?.replace(/^'+|'+$/g, ''), // remove extra quotes
              values: yearFields.map(col => row[col.fieldname] || 0)
            }));

          // 3. Set final output
          profitLossData.value.headers = ['Particulars', ...headers];
          profitLossData.value.rows = rows;
          // Revenue, Expenses & Profit 5-Year Trend
          if (Array.isArray(res) && res.length > 1) {
            const columns = res[0];
            const rawRows = res[1];

            const yearFields = columns
              .filter(col => col.fieldname?.startsWith('mar_'));

            const years = yearFields.map(col => col.label); // e.g., ["2020-2021", ...]

            // Find revenue and expense rows
            const revenueRow = rawRows.find(r =>
              r.account_name?.includes('Income') || r.account?.includes('Income')
            );
            const expenseRow = rawRows.find(r =>
              r.account_name?.includes('Expense') || r.account?.includes('Expense')
            );

            // Map revenue, expense, profit
            const revenue = yearFields.map(col => revenueRow?.[col.fieldname] || 0);
            const expenses = yearFields.map(col => expenseRow?.[col.fieldname] || 0);
            const profit = revenue.map((r, i) => r - expenses[i]);

            // Set it in your data ref
            revenueExpensesTrendData.value = {
              years,
              revenue,
              expenses,
              profit
            };
          }

        }
      },

      onError:(error)=>{
        console.log(error)
      }
    })
  }

  function finacial_data(){
    createResource({
      url:"inspira.inspira.api.dashboards.financials.finacial_data",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        // console.log("res",res)
        quarterwiseProjectionsData.value = res.quarterwiseProjectionsData
        associateProjectionData.value = res.associateProjectionData
        companyRevenueData.value = res.companyRevenueData
        yoyCollectionData.value =  res.yoyCollectionData
        debtorsDetailsData.value = res.accounts_receivable.details.map(item => ({
          date: item.date,
          customer: item.customer,
          associate: item.associate,
          fee: parseFloat(item.fee),
          gst: parseFloat(item.gst),
          total: parseFloat(item.total)
        }));
        debtorsAgingData.value = res.accounts_receivable.aging_summary
      },
      onError:(error)=>{
        console.log(error)
      }
    })
  }
onMounted(()=>{
  get_landing_data()
  profit_and_loss_statement()
  finacial_data()
})
</script>