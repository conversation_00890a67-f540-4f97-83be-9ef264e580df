<template>
  <div class="w-full p-4">
    <DashboardHeader title="Dashboard" />

    <!-- Top Row Metrics -->
    <div class="w-full flex gap-2 mb-2">

      <div class="flex justify-between w-1/2 gap-2">
        <YtdCollectionCard :data="dashboardData.ytdCollection" class="w-1/3" />
        <SalesAnalysisCard :data="dashboardData.salesAnalysis" class="w-1/3" />
        <ServiceBreakupCard :data="dashboardData.serviceBreakup" class="w-1/3" />
      </div>

      <div class="flex w-1/2 gap-2">
        <div class="w-1/2">
          <UpcomingPayrollCard :data="dashboardData.upcomingPayroll" />
        </div>

        <div class="w-1/2 flex flex-col gap-2">
          <div class="flex gap-2 ">
            <MetricCard title="New Hires   FY 2526" :value="dashboardData.newHires" class="w-full" />
            <MetricCard title="Total Employees" :value="dashboardData.totalEmployees" class="w-full" />
          </div>
          <div class="flex gap-2">
            <MetricCard title="Overall Attrition" :value="dashboardData.overallAttrition" suffix="%" class="w-full" />
            <MetricCard title="Average Salary" :value="dashboardData.averageSalary" class="w-full" />
          </div>
        </div>
      </div>

    </div>

    <!-- Middle Row Charts -->
    <div class="flex flex-col lg:flex-row gap-4 mb-2">
      <RevenueChart :data="dashboardData.revenueByMonth" class="flex-1" />
      <ExpenseCategoryChart :data="dashboardData.expenseCategories" class="flex-1" />
    </div>

    <!-- Bottom Row -->
    <div class="flex flex-col lg:flex-row gap-4">
      <!-- <PLHistoricalTable :data="dashboardData.plData" class="flex-1" /> -->
       <PLHistoricalTable :columns="plColumns" :rows="plRows" class="flex-1" />
      <TeamProjectDistribution :data="dashboardData.teamProjects" class="flex-1" />
    </div>
  </div>
</template>


<script setup>
import {onMounted, ref } from 'vue';
import DashboardHeader from './Landing_pages/LandingHeader.vue';
import YtdCollectionCard from './Landing_pages/YtdCollectioncard.vue';
import SalesAnalysisCard from './Landing_pages/SalesAnalysiscard.vue';
import ServiceBreakupCard from './Landing_pages/ServiceBreakupcard.vue';
import UpcomingPayrollCard from './Landing_pages/UpcomingPayrollcard.vue';
import MetricCard from './Landing_pages/MetricCard.vue';
import RevenueChart from './Landing_pages/RevenueChart.vue';
import ExpenseCategoryChart from './Landing_pages/ExpenseChart.vue';
import PLHistoricalTable from './Landing_pages/PLhistoryTable.vue';
import TeamProjectDistribution from './Landing_pages/TeamProjectDistribution.vue';
import { createResource } from 'frappe-ui';

const dashboardData = ref({
  ytdCollection: {
    // invoiced: 1500,
    // collected: 1200,
    // projects: 130,
    // growthPercentage: 8
  },
  salesAnalysis: {
    // customersAdded: 19,
    // revenueGenerated: 34.4,
    // totalSquareFeet: 67000
  },
  serviceBreakup: {
    // architecture: 85,
    // interior: 15
  },
  upcomingPayroll: {
    // interiorDeals: 34,
    // architectureDeals: 41
  },
  newHires: 0,
  totalEmployees: 0,
  overallAttrition: 0,
  averageSalary: 0,
  revenueByMonth: [
    // { month: 'Apr', value: 6.5 },
    // { month: 'May', value: 5.8 },
    // { month: 'Jun', value: 4.2 },
    // { month: 'Jul', value: 3.5 },
    // { month: 'Aug', value: 6.2 },
    // { month: 'Sep', value: 6.3 },
    // { month: 'Oct', value: 5.6 },
    // { month: 'Nov', value: 5.7 },
    // { month: 'Dec', value: 4.8 },
    // { month: 'Jan', value: 3.9 },
    // { month: 'Feb', value: 5.2 },
    // { month: 'Mar', value: 5.0 }
  ],
  expenseCategories: [
    // { category: 'Professional Fees', percentage: 25 },
    // { category: 'Active', percentage: 25 },
    // { category: 'Rent', percentage: 8 },
    // { category: 'Finance Cost / EMI', percentage: 15 },
    // { category: 'Electricity Charges', percentage: 8 },
    // { category: 'IT Cost', percentage: 4 },
    // { category: 'Other Expenses', percentage: 15 }
  ],
  plData: [
    // { particular: 'Sales', y2021: 400, y2122: 590, y2223: 600 },
    // { particular: 'Expenses', y2021: 200, y2122: 390, y2223: 400 },
    // { particular: 'Profit Before Taxes', y2021: 200, y2122: 200, y2223: 200 },
    // { particular: 'Depreciation', y2021: 8, y2122: 9, y2223: 10 },
    // { particular: 'Taxes', y2021: 30, y2122: 32, y2223: 35 },
    // { particular: 'Profit After Taxes', y2021: 162, y2122: 159, y2223: 155 },
    // { particular: 'Profit After Taxes %', y2021: '40.5%', y2122: '27%', y2223: '25.9%' }
  ],
  teamProjects: [
    // { name: 'KrishnaMurrti', count: 48 },
    // { name: 'Ayan', count: 38 },
    // { name: 'Riya', count: 49 },
    // { name: 'Prajyot', count: 48 },
    // { name: 'Ashok', count: 38 },
    // { name: 'Arun', count: 48 },
    // { name: 'Abhishek', count: 38 },
    // { name: 'Deepak', count: 44 },
    // { name: 'Pratik', count: 48 },
    // { name: 'Ishan', count: 38 }
  ]
});
function get_project_distribution(){
    createResource({
      url:"inspira.inspira.api.dashboards.projects.get_project_distribution",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        const message = res;
        if (
          message &&
          Array.isArray(message.categories) &&
          message.series?.[0]?.data &&
          Array.isArray(message.series[0].data)
        ) {
          dashboardData.value.teamProjects = message.categories.map((name, i) => ({
            name,
            count: message.series[0].data[i] ?? 0
          }));
        } else {
          dashboardData.value.teamProjects = [];
        }
      },
      onError:(error)=>{
        console.log(error)
      }
    })
  }
const plColumns = ref([]);
const plRows = ref([]);
  function profit_and_loss_statement(){
    createResource({
      url:"inspira.inspira.api.dashboards.landing.profit_and_loss_statement",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        plColumns.value = res[0] || [];
        plRows.value = res[1] || [];
      },
      onError:(error)=>{
        console.log(error)
      }
    })
  }
  function get_landing_data(){
    createResource({
      url:"inspira.inspira.api.dashboards.landing.get_landing_data",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        console.log("res",res)
        dashboardData.value.upcomingPayroll = res.metricCards
        dashboardData.value.ytdCollection = res.ytdCollection
        dashboardData.value.salesAnalysis = res.salesAnalysis
        dashboardData.value.serviceBreakup = res.serviceBreakup
        dashboardData.value.revenueByMonth = res.revenueChart || []
        dashboardData.value.newHires = res.newHires
        dashboardData.value.totalEmployees = res.totalEmployees
        dashboardData.value.overallAttrition = res.overallAttrition
        dashboardData.value.averageSalary = res.averageSalary
        dashboardData.value.expenseCategories = res.expenseCategoryChart
      },
      onError:(error)=>{
        console.log(error)
      }
    })
  }
  onMounted(()=>{
    get_project_distribution()
    profit_and_loss_statement()
    get_landing_data()
  })
</script>