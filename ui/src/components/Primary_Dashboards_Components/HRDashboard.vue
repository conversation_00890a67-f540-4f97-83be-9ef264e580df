<template>
  <div class="w-full h-full p-4">
    <div class="flex items-center gap-2 mb-4">
      <div>
        <ProjectDashboardIcon class="w-4 h-4" />
      </div>
      <h1 class="text-xl font-medium">HR Dashboard</h1>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
      <hr-analysis-card :departments="departments" :attrition-data="attritionData" />
      <payroll-costs-card :payroll-data="payrollData" />
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-4 ">
      <div class="lg:col-span-7">
        <recruitment-analysis-card :recruitment-data="recruitmentData" class="h-full" />
      </div>
      <div class="grid lg:col-span-5 gap-2">
        <candidate-pipeline-card :pipeline-data="pipelineData" />
        <source-of-hires-card :source-data="sourceData" />
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-4">
      <div class="lg:col-span-4">
        <employee-tenure-card :tenure-data="tenureData" class="h-full" />
      </div>
      <div class="lg:col-span-4">
        <exit-interview-trends-card :exit-data="exitData" class="h-full" />
      </div>
      <div class="lg:col-span-4">
        <leave-trends-card :leave-data="leaveData" class="h-full" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import HrAnalysisCard from './HRMS_pages/HrAnalysisCard.vue';
import PayrollCostsCard from './HRMS_pages/PayrollCostsCard.vue';
import RecruitmentAnalysisCard from './HRMS_pages/RecruitmentAnalysisCard.vue';
import CandidatePipelineCard from './HRMS_pages/CandidatePipelineCard.vue';
import SourceOfHiresCard from './HRMS_pages/SourceOfHiresCard.vue';
import EmployeeTenureCard from './HRMS_pages/EmployeeTenureCard.vue';
import ExitInterviewTrendsCard from './HRMS_pages/ExitInterviewTrendsCard.vue';
import LeaveTrendsCard from './HRMS_pages/LeaveTrendsCard.vue';
import ProjectDashboardIcon from '../icons/ProjectDashboardIcon.vue'



const departments = ref([
  { name: 'Departments', type: 'header' },
  { name: 'Arch', employees: 12 },
  { name: 'Int', employees: 15 },
  { name: '3D', employees: 15 },
  { name: 'PM', employees: 15 },
  { name: 'Site', employees: 15 },
  { name: 'Finance', employees: 8 },
  { name: 'IT', employees: 6 },
  { name: 'HR', employees: 4 },
  { name: 'Others', employees: 8 }
]);

const attritionData = ref([
  { year: '2021', rate: '7.19 %' },
  { year: '2022', rate: '6.59 %' },
  { year: '2023', rate: '7.69 %' },
  { year: '2024', rate: '8.54 %' },
  { year: '2025', rate: '6.76 %' }
]);

// Payroll Costs Data
const payrollData = ref([
  { department: 'Interior', cost: 5 },
  { department: 'Architecture', cost: 7 },
  { department: 'Finance', cost: 7 },
  { department: 'HR', cost: 8 },
  { department: 'IT', cost: 4 },
  { department: 'Sales', cost: 6 },
  { department: 'Others', cost: 8 }
]);

// Recruitment Analysis Data
const recruitmentData = ref({
  headers: ['Trends', 'Jan 25', 'Feb 25', 'Mar 25', 'Apr 25', 'May 25', 'Jun 25'],
  rows: [
    { label: 'No of open positions', values: [6, 6, 8, 5, 4, 4], color: 'text-gray-700' },
    { label: 'Number of new positions added', values: [4, 4, 5, 4, 2, 2], color: 'text-red-500' },
    { label: 'On Hold positions', values: [1, 1, 4, 2, 4, 4], color: 'text-green-500' },
    {
      label: 'Number of unique calls made',
      values: [5, 5, 6, 3, 3, 3],
      color: 'text-gray-700',
      expandable: true,
      subRows: [
        { label: 'Screening Calls', values: [2, 2, 3, 1, 1, 1], color: 'text-gray-500' },
        { label: 'Interview Calls', values: [3, 3, 3, 2, 2, 2], color: 'text-gray-500' }
      ]
    },
    {
      label: 'Number of positions closed',
      values: [50, 50, 50, 50, 50, 50],
      color: 'text-gray-700',
      expandable: true,
      subRows: [
        { label: 'Tech Positions Closed', values: [30, 30, 30, 30, 30, 30], color: 'text-gray-500' },
        { label: 'Non-Tech Positions Closed', values: [20, 20, 20, 20, 20, 20], color: 'text-gray-500' }
      ]
    },
    { label: 'Number of candidates in progress', values: [8, 8, 8, 8, 7, 7], color: 'text-gray-700' },
    { label: 'Number of candidates hired', values: [8, 8, 8, 8, 7, 7], color: 'text-gray-700' }
  ]
});


// Candidate Pipeline Data
const pipelineData = ref([
  { name: 'Applications', value: 100, color: '#4E3A77' },
  { name: 'Screening & Shortlisting HR', value: 70, color: '#6D5C90' },
  { name: 'Technical Interviews', value: 50, color: '#8F82AA' },
  { name: 'Background checks', value: 30, color: '#B8B0CA' },
  { name: 'Job Offer', value: 16, color: '#E0DDE8' }
]);

// Source of Hires Data
const sourceData = ref([
  { source: 'Online Platforms', percentage: 35, color: '#2E0D71' },
  { source: 'Reference', percentage: 25, color: '#452489' },
  { source: 'Internal', percentage: 15, color: '#756498' },
  { source: 'Portals', percentage: 10, color: '#756498' },
  { source: 'Placement drives', percentage: 8, color: '#8F82AA' },
  { source: 'Agencies', percentage: 7, color: '#C0C0DA' }
]);

// Employee Tenure Data
const tenureData = ref([
  { years: 2, count: 5 },
  { years: 4, count: 8 },
  { years: 6, count: 12 },
  { years: 8, count: 20 },
  { years: 10, count: 28 },
  { years: 12, count: 25 },
  { years: 14, count: 18 },
  { years: 16, count: 15 },
  { years: 18, count: 20 },
  { years: 20, count: 18 },
  { years: 22, count: 12 },
  { years: 24, count: 8 }
]);

// Exit Interview Trends Data
const exitData = ref([
  { reason: 'Culture', count: 11 },
  { reason: 'Personal', count: 22 },
  { reason: 'Growth', count: 28 },
  { reason: 'Health', count: 28 },
  { reason: 'Pay', count: 17 },
  { reason: 'Discipline', count: 17 }
]);

// Leave Trends Data
const leaveData = ref([
  {
    department: 'Interior',
    casual: 8,
    sick: 4,
    unpaid: 2
  },
  {
    department: 'Architecture',
    casual: 10,
    sick: 6,
    unpaid: 3
  },
  {
    department: 'IT',
    casual: 9,
    sick: 5,
    unpaid: 2
  },
  {
    department: 'HR',
    casual: 15,
    sick: 8,
    unpaid: 5
  },
  {
    department: 'Finance',
    casual: 12,
    sick: 6,
    unpaid: 3
  },
  {
    department: 'Sales',
    casual: 11,
    sick: 7,
    unpaid: 4
  }
]);
</script>