<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-700 mb-4">Revenue, Expenses & Profit 5-Year Trend</h3>
      <div class="h-80">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
      <div class="flex justify-center gap-6 mt-2">
        <div class="flex items-center">
          <div class="w-3 h-3 rounded-full bg-purple-800 mr-2"></div>
          <span class="text-xs text-gray-600">Revenue</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 rounded-full bg-purple-400 mr-2"></div>
          <span class="text-xs text-gray-600">Expenses</span>
        </div>
        <div class="flex items-center">
          <div class="w-3 h-3 rounded-full bg-purple-600 mr-2"></div>
          <span class="text-xs text-gray-600">Profit</span>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [
      {
        name: 'Revenue',
        data: props.data.revenue
      },
      {
        name: 'Expenses',
        data: props.data.expenses
      },
      {
        name: 'Profit',
        data: props.data.profit
      }
    ];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        stacked: true,
        toolbar: {
          show: false
        },
        fontFamily: 'inherit'
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 2
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      // xaxis: {
      //   categories: props.data.years,
      //   axisBorder: {
      //     show: false
      //   },
      //   axisTicks: {
      //     show: false
      //   }
      // },
      xaxis: {
  categories: props.data.years,
  axisBorder: {
    show: false
  },
  axisTicks: {
    show: false
  },
  labels: {
    style: {
      fontSize: '10px',
      colors: '#6b7280', // optional Tailwind gray
      fontWeight: 500
    },
    formatter: function (value) {
      // Example: Split at space or dash to wrap (customize based on your labels)
      return value.toString().replace(/[\s\-]/g, '\n');
    }
  }
},

      yaxis: {
        title: {
          text: 'Cr'
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(0);
          }
        }
      },
      colors: ['#6b5ca5', '#b7b6db', '#9d94c0'],
      legend: {
        show: false
      },
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + ' Cr';
          }
        }
      }
    };
  });
  </script>