<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-700 mb-1">Company Revenue</h3>
      <p class="text-xs text-gray-500 mb-4">Year To Date</p>
      <div class="h-80">
        <apexchart
          type="line"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Revenue',
      data: props.data.map(item => item.value)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'line',
        toolbar: {
          show: false
        },
        fontFamily: 'inherit',
        zoom: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 3
      },
      xaxis: {
        categories: props.data.map(item => item.month),
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: 'Cr'
        },
        min: 0,
        forceNiceScale: true
      },
      colors: ['#9d94c0'],
      markers: {
        size: 5,
        colors: ['#9d94c0'],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 7
        }
      },
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      fill: {
        type: 'gradient',
        gradient: {
          shade: 'light',
          type: 'vertical',
          shadeIntensity: 0.5,
          gradientToColors: ['#6b5ca5'],
          inverseColors: false,
          opacityFrom: 0.7,
          opacityTo: 0.2,
          stops: [0, 100]
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + ' Cr';
          }
        }
      }
    };
  });
  </script>