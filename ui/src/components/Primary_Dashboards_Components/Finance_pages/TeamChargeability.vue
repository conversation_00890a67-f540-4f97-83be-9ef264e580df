<template>
    <div class="bg-white rounded-lg shadow p-4">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h3 class="text-sm font-medium text-gray-700">Chargeability</h3>
          <p class="text-xs text-gray-500">Team wise</p>
        </div>
      </div>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Chargeability',
      data: props.data.map(item => item.value)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        toolbar: {
          show: false
        },
        fontFamily: 'inherit'
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 2
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: props.data.map(item => item.name),
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        labels: {
          style: {
            fontSize: '10px'
          }
        }
      },
      yaxis: {
        title: {
          text: ''
        },
        min: 0,
        max: 2,
        tickAmount: 4
      },
      colors: ['#9d94c0'],
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val.toFixed(2);
          }
        }
      }
    };
  });
  </script>