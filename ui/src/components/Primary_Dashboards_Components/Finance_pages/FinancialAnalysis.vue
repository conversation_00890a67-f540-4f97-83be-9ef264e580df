<template>
  <div class="p-4 shadow-xl rounded-lg">
    <h2 class="text-gray-700 font-medium mb-4">Financial Analysis</h2>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-2">
      <!-- YTD Metrics Card -->
      <div class="bg-white rounded-lg shadow p-4 h-full flex flex-col">
        <div>
          <h3 class="text-sm font-semibold text-gray-700 mb-6">{{ ytdData.collection.title }}</h3>

          <div class="flex justify-between items-center">
            <h3 class="text-md font-medium text-gray-900">{{ ytdData.invoiced.title }}</h3>
            <div class="flex items-baseline">
              <span class="text-lg font-semibold">{{ ytdData.invoiced.value }}</span>
              <span class="text-sm text-gray-500 ml-1">{{ ytdData.invoiced.unit }}</span>
            </div>
          </div>

          <div class="flex mt-2 mb-2">
            <span
              :class="['text-xs font-medium', ytdData.collection.trendPositive ? 'text-green-600' : 'text-red-600']">
              {{ ytdData.collection.trend }}
            </span>
            <span class="text-xs text-gray-500 ml-1">{{ ytdData.collection.trendLabel }}</span>
          </div>
        </div>

        <div class="flex justify-between items-center mb-3">
          <h3 class="text-md font-medium text-gray-900">{{ ytdData.collected.title }}</h3>
          <div class="flex items-baseline">
            <span class="text-lg font-semibold">{{ ytdData.collected.value }}</span>
            <span class="text-sm text-gray-500 ml-1">{{ ytdData.collected.unit }}</span>
          </div>
        </div>

        <div class="flex justify-between items-center">
          <h3 class="text-md font-medium text-gray-900">{{ ytdData.projects.title }}</h3>
          <div class="flex items-baseline">
            <span class="text-lg font-semibold">{{ ytdData.projects.value }}</span>
            <span v-if="ytdData.projects.unit" class="text-sm text-gray-500 ml-1">{{ ytdData.projects.unit }}</span>
          </div>
        </div>
      </div>

      <!-- Service Based Breakup -->
      <div class="bg-white rounded-lg shadow p-2 h-full flex flex-col justify-between">
        <h3 class="text-sm font-semibold text-gray-700 text-center mb-4">Service Based Breakup</h3>

        <div class="flex justify-center items-center h-28">
          <apexchart type="donut" height="100%" :options="serviceChartOptions" :series="serviceChartSeries"></apexchart>
        </div>

        <div class="mt-4 text-xs">
          <div v-for="(item, index) in serviceBreakupData" :key="index" class="flex justify-between mb-1">
            <span class="text-gray-600 font-medium">Fee for {{ item.category }}</span>
            <span class="font-semibold">{{ item.percentage }}%</span>
          </div>
        </div>
      </div>
    </div>


    <!-- Middle row of cards - 3 columns -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Profitability YTD -->
      <div class="bg-white rounded-lg shadow-xl p-4">
        <h3 class="text-sm text-gray-600 mb-1">{{ profitabilityData.title }}</h3>
        <div class="flex items-baseline">
          <span class="text-3xl font-semibold">{{ profitabilityData.value }}{{ profitabilityData.unit }}</span>
        </div>
        <div class="flex items-center mt-1">
          <span :class="['text-xs', profitabilityData.trendPositive ? 'text-green-600' : 'text-red-600']">
            {{ profitabilityData.trend }}
          </span>
          <span class="text-xs text-gray-500 ml-1">{{ profitabilityData.trendLabel }}</span>
        </div>
      </div>

      <!-- Projections Forecast -->
      <div class="bg-white rounded-lg shadow-xl p-4">
        <h3 class="text-sm text-gray-600 mb-1">{{ projectionsData.forecast.title }}</h3>
        <div class="flex items-baseline">
          <span class="text-sm font-medium">₹</span>
          <span class="text-3xl font-semibold ml-1">{{ projectionsData.forecast.value }}</span>
          <span class="text-sm text-gray-500 ml-1">{{ projectionsData.forecast.unit }}</span>
        </div>
      </div>

      <!-- Feb-25 Projections -->
      <div class="bg-white rounded-lg shadow-xl p-4">
        <h3 class="text-sm text-gray-600 mb-1">{{ projectionsData.monthly.title }}</h3>
        <div class="flex items-baseline">
          <span class="text-sm font-medium">₹</span>
          <span class="text-3xl font-semibold ml-1">{{ projectionsData.monthly.value }}</span>
          <span class="text-sm text-gray-500 ml-1">{{ projectionsData.monthly.unit }}</span>
        </div>
      </div>
    </div>

    <!-- Bottom row of cards - 3 columns -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Chargeability YTD -->
      <div class="bg-white rounded-lg shadow-xl p-4">
        <h3 class="text-sm text-gray-600 mb-1">{{ chargeabilityData.title }}</h3>
        <div class="flex items-baseline">
          <span class="text-3xl font-semibold">{{ chargeabilityData.value }}</span>
          <span v-if="chargeabilityData.unit" class="text-sm text-gray-500 ml-1">{{ chargeabilityData.unit }}</span>
        </div>
        <div class="flex items-center mt-1">
          <span :class="['text-xs', chargeabilityData.trendPositive ? 'text-green-600' : 'text-red-600']">
            {{ chargeabilityData.trend }}
          </span>
          <span class="text-xs text-gray-500 ml-1">{{ chargeabilityData.trendLabel }}</span>
        </div>
      </div>

      <!-- Expense Incurred -->
      <div class="bg-white rounded-lg shadow-xl p-4">
        <h3 class="text-sm text-gray-600 mb-1">{{ expenseData.title }}</h3>
        <div class="flex items-baseline">
          <span class="text-sm font-medium">₹</span>
          <span class="text-3xl font-semibold ml-1">{{ expenseData.value }}</span>
          <span class="text-sm text-gray-500 ml-1">{{ expenseData.unit }}</span>
        </div>
        <div class="flex items-center mt-1">
          <span :class="['text-xs', expenseData.trendPositive ? 'text-green-600' : 'text-red-600']">
            {{ expenseData.trend }}
          </span>
          <span class="text-xs text-gray-500 ml-1">{{ expenseData.trendLabel }}</span>
        </div>
      </div>

      <!-- Total Debtors -->
      <div class="bg-white rounded-lg shadow-xl p-4">
        <h3 class="text-sm text-gray-600 mb-1">{{ debtorsData.title }}</h3>
        <div class="flex items-baseline">
          <span class="text-sm font-medium">₹</span>
          <span class="text-3xl font-semibold ml-1">{{ debtorsData.value }}</span>
          <span class="text-sm text-gray-500 ml-1">{{ debtorsData.unit }}</span>
        </div>
        <div class="flex items-center mt-1">
          <span :class="['text-xs', debtorsData.trendPositive ? 'text-green-600' : 'text-red-600']">
            {{ debtorsData.trend }}
          </span>
          <span class="text-xs text-gray-500 ml-1">{{ debtorsData.trendLabel }}</span>
        </div>
      </div>
    </div>

    <!-- Expense Category Breakup - Full width -->
    <div class="bg-white rounded-lg shadow p-4 mb-4">
      <h3 class="text-sm text-gray-600 mb-4">Expense Category Breakup</h3>
      <div class="h-60">
        <apexchart type="donut" height="100%" :options="expenseChartOptions" :series="expenseChartSeries"></apexchart>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  ytdData: {
    type: Object,
    required: true
  },
  serviceBreakupData: {
    type: Array,
    required: true
  },
  profitabilityData: {
    type: Object,
    required: true
  },
  projectionsData: {
    type: Object,
    required: true
  },
  chargeabilityData: {
    type: Object,
    required: true
  },
  expenseData: {
    type: Object,
    required: true
  },
  debtorsData: {
    type: Object,
    required: true
  },
  expenseCategoryData: {
    type: Array,
    required: true
  }
});

// Service Based Breakup Chart
const serviceChartSeries = computed(() =>
  props.serviceBreakupData.map(item => item.percentage)
);

const serviceChartOptions = computed(() => {
  return {
    chart: {
      type: 'donut',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      }
    },
    labels: props.serviceBreakupData.map(item => item.category),
    colors: ['#9d94c0', '#b7b6db'],
    legend: {
      show: false
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: true,
            name: {
              show: false
            },
            value: {
              show: true,
              formatter: function (val) {
                return val + '%';
              }
            },
            total: {
              show: false
            }
          }
        }
      }
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      width: 2,
      colors: ['#fff']
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + '%';
        }
      }
    }
  };
});

// Expense Category Breakup Chart
const expenseChartSeries = computed(() =>
  props.expenseCategoryData.map(item => item.percentage)
);

const expenseChartOptions = computed(() => {
  return {
    chart: {
      type: 'donut',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      }
    },
    labels: props.expenseCategoryData.map(item => item.category),
    colors: ['#1e0e4b', '#b7b6db', '#9d94c0', '#d1d0e6', '#3a1e6d', '#e8e7f2', '#6b5ca5'],
    legend: {
      position: 'right',
      fontSize: '10px',
      markers: {
        width: 10,
        height: 10,
        radius: 0
      },
      itemMargin: {
        horizontal: 6,
        vertical: 3
      }
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: true,
            name: {
              show: true
            },
            value: {
              show: true,
              formatter: function (val) {
                return val + '%';
              }
            }
          }
        }
      }
    },
    dataLabels: {
      enabled: false
    },
    stroke: {
      width: 2,
      colors: ['#fff']
    },
    tooltip: {
      y: {
        formatter: function (val) {
          return val + '%';
        }
      }
    }
  };
});
</script>