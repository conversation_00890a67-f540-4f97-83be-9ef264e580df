<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h3 class="text-sm font-medium text-gray-700 mb-4">Debtors Details</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">Invoice Date</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">Customer Name</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">Associate</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">Fee Amount</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">GST</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">Total Amount</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(item, index) in data" :key="index">
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">{{ item.date }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">{{ item.customer }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">{{ item.associate }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">₹ {{ item.fee.toFixed(2) }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">₹ {{ item.gst.toFixed(2) }}</td>
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">₹ {{ item.total.toFixed(2) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  </script>