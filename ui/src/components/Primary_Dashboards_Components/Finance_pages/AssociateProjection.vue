<template>
    <div class="bg-white rounded-lg shadow p-4">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h3 class="text-sm font-medium text-gray-700">Projection</h3>
          <p class="text-xs text-gray-500">Associate wise</p>
        </div>
        <div class="relative">
          <!-- <button class="flex items-center text-sm text-gray-700 border border-gray-300 rounded-md px-3 py-1">
            <span>Yearly</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="m6 9 6 6 6-6"/></svg>
          </button> -->
        </div>
      </div>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Projection',
      data: props.data.map(item => item.value)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        toolbar: {
          show: false
        },
        fontFamily: 'inherit'
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 2
        }
      },
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: props.data.map(item => item.name),
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        },
        labels: {
          style: {
            fontSize: '10px'
          }
        }
      },
      yaxis: {
        title: {
          text: 'Cr'
        },
        min: 0,
        max: 4,
        tickAmount: 4
      },
      colors: ['#9d94c0'],
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + ' Cr';
          }
        }
      }
    };
  });
  </script>