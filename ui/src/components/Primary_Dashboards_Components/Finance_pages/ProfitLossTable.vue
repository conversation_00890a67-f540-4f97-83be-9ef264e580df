<template>
    <div class="w-full bg-white rounded-lg shadow p-4 h-full">
      <h3 class="text-sm font-medium text-gray-700 mb-4">P&L - Current FY & Historical</h3>
      <div class="overflow-x-auto w-full h-full">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr>
              <th v-for="(header, index) in data.headers" :key="index" 
                  class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-100">
                {{ header }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(row, rowIndex) in data.rows" :key="rowIndex" 
                :class="{'bg-purple-50': row.item.includes('Profit After Taxes')}">
              <td class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">
                {{ row.item }}
              </td>
              <td v-for="(value, valueIndex) in row.values" :key="valueIndex" 
                  class="px-3 py-2 whitespace-nowrap text-xs text-gray-700">
                {{ row.item.includes('%') ? value + '%' : value }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  </script>