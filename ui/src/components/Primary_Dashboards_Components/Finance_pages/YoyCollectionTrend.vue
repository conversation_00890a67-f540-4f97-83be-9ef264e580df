<template>
    <div class="bg-white rounded-lg shadow p-4">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h3 class="text-sm font-medium text-gray-700">YOY Monthly Collection Trend</h3>
          <p class="text-xs text-gray-500">Last 3 Years</p>
        </div>
      </div>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    data: {
      type: Object,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [
      {
        name: 'FY 22-23',
        data: props.data.fy2223
      },
      {
        name: 'FY 23-24',
        data: props.data.fy2324
      },
      {
        name: 'FY 24-25',
        data: props.data.fy2425
      }
    ];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        toolbar: {
          show: false
        },
        fontFamily: 'inherit'
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '70%',
          borderRadius: 0
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: props.data.months,
        axisBorder: {
          show: false
        },
        axisTicks: {
          show: false
        }
      },
      yaxis: {
        title: {
          text: '%'
        },
        min: 0,
        max: 100
      },
      colors: ['#6b5ca5', '#9d94c0', '#d1d0e6'],
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '12px',
        markers: {
          width: 12,
          height: 12,
          radius: 0
        },
        itemMargin: {
          horizontal: 8,
          vertical: 5
        }
      },
      grid: {
        borderColor: '#f1f1f1',
        strokeDashArray: 4,
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + '%';
          }
        }
      }
    };
  });
  </script>