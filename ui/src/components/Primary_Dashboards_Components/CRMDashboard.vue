<template>
    <div class="w-full h-full p-4">
        <div class="flex items-center gap-2 mb-4">
            <div class="flex items-center">
                <ProjectDashboardIcon class="w-4 h-4" />
                <h1 class="text-xl font-medium ml-2">CRM Dashboard</h1>
            </div>
        </div>

        <div class="w-full flex justify-between gap-4">

            <div class="w-[40%] flex flex-col justify-between gap-2">

                <div class="flex flex-col gap-2 bg-white rounded-lg shadow p-4">
                    <h2 class="text-lg font-medium text-gray-700">Lead Analysis</h2>
                    <div class="w-full flex gap-2">
                        <metric-box title="Total Leads YTD" :value="total_leads" :change="leadCompareToLastYear" change-label="Compared to last FY"
                            class="w-1/2 h-28" />
                        <!-- Lead Analysis Card -->
                        <div class="relative w-1/2">
                            <div class="relative w-full h-28 overflow-hidden rounded-lg shadow-md">
                                <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 200 100"
                                    preserveAspectRatio="none">
                                    <polygon points="0,0 200,0 0,100" class="fill-[#6D5C904D]" />
                                    <polygon points="200,0 200,100 0,100" class="fill-[#B7B7B74D]" />
                                </svg>
                                <div class="absolute top-2 left-4 text-gray-700 text-sm">Interior Leads</div>
                                <div class="absolute top-8 left-4 text-2xl font-medium">{{interior_leads}}</div>
                                <div class="absolute bottom-2 right-4 text-gray-700 text-sm">Architecture Leads</div>
                                <div class="absolute bottom-8 right-4 text-2xl font-medium">{{architecture_leads}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex gap-2">
                        <metric-box title="Open Leads" :value="open_leads" :show-trend="false" class="w-1/2 h-28" />
                        <metric-box title="Lead Conversion Rate" :value="converted_leads" :show-trend="false" class="w-1/2 h-28" />
                    </div>
                </div>

                <div class="flex flex-col gap-2 bg-white rounded-lg shadow p-4">
                    <h2 class="text-lg font-medium text-gray-700">Deal Analysis</h2>
                    <div class="w-full flex gap-2">
                        <metric-box title="Total Deals YTD" :value="total_deal" :change="percent_change_deal" change-label="Compared to last FY"
                            class="w-1/2 h-28" />
                        <!-- Deal Analysis Card -->
                        <div class="relative w-1/2">
                            <div class="relative w-full h-28 overflow-hidden rounded-lg shadow-md">
                                <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 200 100"
                                    preserveAspectRatio="none">
                                    <polygon points="0,0 200,0 0,100" class="fill-[#6D5C904D]" />
                                    <polygon points="200,0 200,100 0,100" class="fill-[#B7B7B74D]" />
                                </svg>
                                <div class="absolute top-2 left-4 text-gray-700 text-sm">Interior Deals</div>
                                <div class="absolute top-8 left-4 text-2xl font-medium">{{interior_deals}}</div>
                                <div class="absolute bottom-2 right-4 text-gray-700 text-sm">Architecture Deals</div>
                                <div class="absolute bottom-8 right-4 text-2xl font-medium">{{architecture_deals}}</div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex gap-2">
                        <metric-box title="Open Deals" :value="open_deals" :show-trend="false" class="w-1/3 h-28" />
                        <metric-box title="Deal Value Forecast" :value="deal_frocast" :show-trend="false" class="w-1/3 h-28" />
                        <metric-box title="Average Deal Value" :value="avg_deal_frocast" :show-trend="false" class="w-1/3 h-28" />
                    </div>
                </div>

                <div class="flex-1 bg-white rounded-lg shadow">
                    <div class="p-4">
                        <h2 class="text-lg font-medium text-gray-700">Deals Pipeline</h2>
                        <div class="h-80">
                            <deals-funnel-chart :data="dealsFunnelData" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="w-[60%] flex flex-col justify-between gap-2">

                <!-- Metrics Row -->
                <div class="w-full flex gap-1">
                    <metric-card title="Customers Added" :value="total_customers" :change="customer_purcentage" change-label="Compared to last FY"
                        trend="down" class="w-1/5" />
                    <metric-card title="Revenue Generated" :value="current_revenue" :change="current_revenue_compare"
                        change-label="Compared to last FY" trend="up" class="w-1/5" />
                    <metric-card title="Total Square Feet" :value="total_land_area" :show-trend="false" class="w-1/5" />
                    <win-rate-chart :data="winRateData" class="w-2/5" />
                </div>
                <!-- Right Column -->
                <div class="flex-1 flex flex-col gap-4">
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-4">
                            <h2 class="text-lg font-medium text-gray-700">Leads & Deals</h2>
                            <div class="h-80">
                                <leads-deals-chart :data="leadsDealsData" />
                            </div>
                        </div>
                    </div>

                    <div class="w-full h-full bg-white rounded-lg shadow overflow-auto">
                        <div class="p-4">
                            <h2 class="text-lg font-medium text-gray-700">Upcoming Followups</h2>
                            <div class="overflow-x-auto h-[50vh]">
                                <table class="min-w-full mt-4">
                                    <thead class="bg-[#ECE6F0]">
                                        <tr>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">
                                                Category</th>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Point
                                                of Contact</th>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Next
                                                Followup Date</th>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-[#F7F7FD] text-sm">
                                        <tr v-for="(followup, index) in followups" :key="index">
                                            <td class="py-2 px-4 text-sm text-gray-700">{{ followup.category }}</td>
                                            <td class="py-2 px-4 text-sm text-gray-700">{{ followup.contact }}</td>
                                            <td class="py-2 px-4 text-sm text-gray-700">{{ followup.date }}</td>
                                            <td class="py-2 px-4 text-sm">
                                                <span class="px-3 py-1 rounded-full text-xs"
                                                    :class="followup.status === 'Overdue' ? 'bg-red-200 text-red-800' : 'bg-green-200 text-green-800'">
                                                    {{ followup.status }}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Row: Deals Funnel & Pending Tasks -->
        <div class="flex flex-col lg:flex-row gap-4 mt-4">
            <div class="flex-1 bg-white rounded-lg shadow">
                <div class="p-4">
                    <h2 class="text-lg font-medium text-gray-700">Pending Tasks</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full mt-4">
                            <thead class="bg-[#ECE6F0]">
                                <tr>
                                    <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Category</th>
                                    <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Lead/Deal Name
                                    </th>
                                    <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Task Count</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-[#F7F7FD] text-sm">
                                <tr v-for="(task, index) in pendingTasks" :key="index">
                                    <td class="py-2 px-4 text-sm text-gray-700">{{ task.category }}</td>
                                    <td class="py-2 px-4 text-sm text-gray-700">{{ task.name }}</td>
                                    <td class="py-2 px-4 text-sm text-gray-700">{{ task.count }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup>
import { onMounted, ref } from 'vue';
import MetricCard from './CRM_pages/MetricCard.vue';
import MetricBox from './CRM_pages/MetricBox.vue';
import WinRateChart from './CRM_pages/WinRateChart.vue';
import LeadsDealsChart from './CRM_pages/LeadsDealsChart.vue';
import DealsFunnelChart from './CRM_pages/DealsFunnelChart.vue';
import ProjectDashboardIcon from '../icons/ProjectDashboardIcon.vue'
import { createResource } from 'frappe-ui';

// Sample data - replace with your actual data source
const winRateData = ref([
    { name: 'Won', value: 0, percentage: 0 },
    { name: 'Lost', value: 0, percentage: 0 }
]);

// const leadsDealsData = ref({
//     months: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
//     leads: [45, 42, 48, 45, 40, 45, 42, 45, 48, 42, 45, 40],
//     deals: [35, 32, 35, 38, 32, 35, 32, 38, 28, 32, 35, 32]
// });
const leadsDealsData = ref({
    months: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
    leads: [],
    deals: []
});
const dealsFunnelData = ref([
    // { name: 'New Deal', value: 100, percentage: 100 },
    // { name: 'Qualified', value: 70, percentage: 70 },
    // { name: 'Proposed', value: 50, percentage: 50 },
    // { name: 'Negotiated', value: 30, percentage: 30 },
    // { name: 'Closed', value: 16, percentage: 16 }
]);

const followups = ref([
    // { category: 'Lead', contact: 'Kiran Mandal', date: '04 Mar 2024', status: 'Overdue' },
    // { category: 'Deal', contact: 'Sheetal Patil', date: '08 Mar 2024', status: 'Overdue' },
    // { category: 'Deal', contact: 'Disha Kumar', date: '04 Mar 2024', status: 'Due' },
    // { category: 'Lead', contact: 'Ruha Luthra', date: '04 Mar 2024', status: 'Overdue' },
    // { category: 'Lead', contact: 'Sheetal Patil', date: '04 Mar 2024', status: 'Due' }
]);

const pendingTasks = ref([
    // { category: 'Lead', name: 'Kiran Mandal', count: 15 },
    // { category: 'Deal', name: 'Sheetal Patil', count: 13 },
    // { category: 'Deal', name: 'Disha Kumar', count: 12 },
    // { category: 'Lead', name: 'Ruha Luthra', count: 11 },
    // { category: 'Lead', name: 'Sheetal Patil', count: 10 }
]);
const total_leads = ref(0)
const leadCompareToLastYear = ref([''])
const architecture_leads = ref(0)
const interior_leads = ref(0)
const converted_leads = ref('')
const open_leads = ref(0)
const total_customers = ref(0)
const customer_purcentage = ref(0)
const total_land_area = ref(0)
const total_deal = ref(0)
const percent_change_deal= ref('')
const interior_deals = ref(0)
const architecture_deals = ref(0)
const open_deals = ref(0)
const current_revenue = ref('')
const current_revenue_compare = ref(0)
const deal_frocast = ref('')
const avg_deal_frocast = ref('')

function get_crm_details(){
    createResource({
      url:"inspira.inspira.api.dashboards.crm.get_crm_details",
      makeParams: () => ({ }),
      auto:true,
      onSuccess: (res) => {
        console.log("res",res)
        total_leads.value = res.leadAnalysis.total_leads
        leadCompareToLastYear.value = res.leadAnalysis.percent_change
        architecture_leads.value = res.leadAnalysis.architecture_leads
        interior_leads.value = res.leadAnalysis.interior_leads
        converted_leads.value = res.leadAnalysis.converted_leads
        open_leads.value = res.leadAnalysis.open_leads
        total_customers.value = res.leadAnalysis.total_customers
        customer_purcentage.value = res.leadAnalysis.customer_purcentage
        total_land_area.value = res.leadAnalysis.total_land_area
        winRateData.value = res.winRateData
        total_deal.value = res.dealAnalysis.total_deal
        percent_change_deal.value = res.dealAnalysis.percent_change
        interior_deals.value = res.dealAnalysis.interior_deal
        architecture_deals.value = res.dealAnalysis.architecture_deal
        open_deals.value = res.dealAnalysis.open_deals
        open_deals.leadsDealsData = res.leadsDealsData
        leadsDealsData.value = res.leadsDealsData
        dealsFunnelData.value = res.dealsFunnelData
        pendingTasks.value = res.pendingTasks
        followups.value = res.followups
        current_revenue.value = res.revenue.current_revenue_cr
        current_revenue_compare.value = res.revenue.change_percent
        avg_deal_frocast.value = res.dealAnalysis.avg_total_budget
        deal_frocast.value = res.dealAnalysis.total_budget
      },
      onError:(error)=>{
        console.log(error)
      }
    })
  }
onMounted(()=>{
    get_crm_details()
})
</script>