<template>
    <div class="w-full h-full p-4">
        <div class="flex items-center gap-2 mb-4">
            <div class="flex items-center">
                <ProjectDashboardIcon class="w-4 h-4" />
                <h1 class="text-xl font-medium ml-2">CRM Dashboard</h1>
            </div>
        </div>

        <div class="w-full flex justify-between gap-4">

            <div class="w-[40%] flex flex-col justify-between gap-2">

                <div class="flex flex-col gap-2 bg-white rounded-lg shadow p-4">
                    <h2 class="text-lg font-medium text-gray-700">Lead Analysis</h2>
                    <div class="w-full flex gap-2">
                        <metric-box title="Total Leads YTD" :value="50" :change="8" change-label="Compared to last FY"
                            class="w-1/2 h-28" />
                        <!-- Lead Analysis Card -->
                        <div class="relative w-1/2">
                            <div class="relative w-full h-28 overflow-hidden rounded-lg shadow-md">
                                <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 200 100"
                                    preserveAspectRatio="none">
                                    <polygon points="0,0 200,0 0,100" class="fill-[#6D5C904D]" />
                                    <polygon points="200,0 200,100 0,100" class="fill-[#B7B7B74D]" />
                                </svg>
                                <div class="absolute top-2 left-4 text-gray-700 text-sm">Interior Leads</div>
                                <div class="absolute top-8 left-4 text-2xl font-medium">34</div>
                                <div class="absolute bottom-2 right-4 text-gray-700 text-sm">Architecture Leads</div>
                                <div class="absolute bottom-8 right-4 text-2xl font-medium">41</div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex gap-2">
                        <metric-box title="Open Leads" :value="20" :show-trend="false" class="w-1/2 h-28" />
                        <metric-box title="Lead Conversion Rate" value="80 %" :show-trend="false" class="w-1/2 h-28" />
                    </div>
                </div>

                <div class="flex flex-col gap-2 bg-white rounded-lg shadow p-4">
                    <h2 class="text-lg font-medium text-gray-700">Deal Analysis</h2>
                    <div class="w-full flex gap-2">
                        <metric-box title="Total Deals YTD" :value="50" :change="8" change-label="Compared to last FY"
                            class="w-1/2 h-28" />
                        <!-- Deal Analysis Card -->
                        <div class="relative w-1/2">
                            <div class="relative w-full h-28 overflow-hidden rounded-lg shadow-md">
                                <svg class="absolute top-0 left-0 w-full h-full" viewBox="0 0 200 100"
                                    preserveAspectRatio="none">
                                    <polygon points="0,0 200,0 0,100" class="fill-[#6D5C904D]" />
                                    <polygon points="200,0 200,100 0,100" class="fill-[#B7B7B74D]" />
                                </svg>
                                <div class="absolute top-2 left-4 text-gray-700 text-sm">Interior Deals</div>
                                <div class="absolute top-8 left-4 text-2xl font-medium">34</div>
                                <div class="absolute bottom-2 right-4 text-gray-700 text-sm">Architecture Deals</div>
                                <div class="absolute bottom-8 right-4 text-2xl font-medium">41</div>
                            </div>
                        </div>
                    </div>
                    <div class="w-full flex gap-2">
                        <metric-box title="Open Deals" :value="20" :show-trend="false" class="w-1/3 h-28" />
                        <metric-box title="Deal Value Forecast" value="25 Cr" :show-trend="false" class="w-1/3 h-28" />
                        <metric-box title="Average Deal Value" value="20 Cr" :show-trend="false" class="w-1/3 h-28" />
                    </div>
                </div>

                <div class="flex-1 bg-white rounded-lg shadow">
                    <div class="p-4">
                        <h2 class="text-lg font-medium text-gray-700">Deals Pipeline</h2>
                        <div class="h-80">
                            <deals-funnel-chart :data="dealsFunnelData" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="w-[60%] flex flex-col justify-between gap-2">

                <!-- Metrics Row -->
                <div class="w-full flex gap-1">
                    <metric-card title="Customers Added" :value="19" :change="-11" change-label="Compared to last FY"
                        trend="down" class="w-1/5" />
                    <metric-card title="Revenue Generated" value="34.4 Cr" :change="8"
                        change-label="Compared to last FY" trend="up" class="w-1/5" />
                    <metric-card title="Total Square Feet" value="67,000" :show-trend="false" class="w-1/5" />
                    <win-rate-chart :data="winRateData" class="w-2/5" />
                </div>
                <!-- Right Column -->
                <div class="flex-1 flex flex-col gap-4">
                    <div class="bg-white rounded-lg shadow">
                        <div class="p-4">
                            <h2 class="text-lg font-medium text-gray-700">Leads & Deals</h2>
                            <div class="h-80">
                                <leads-deals-chart :data="leadsDealsData" />
                            </div>
                        </div>
                    </div>

                    <div class="w-full h-full bg-white rounded-lg shadow overflow-auto">
                        <div class="p-4">
                            <h2 class="text-lg font-medium text-gray-700">Upcoming Followups</h2>
                            <div class="overflow-x-auto h-[50vh]">
                                <table class="min-w-full mt-4">
                                    <thead class="bg-[#ECE6F0]">
                                        <tr>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">
                                                Category</th>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Point
                                                of Contact</th>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Next
                                                Followup Date</th>
                                            <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-200 bg-[#F7F7FD] text-sm">
                                        <tr v-for="(followup, index) in followups" :key="index">
                                            <td class="py-2 px-4 text-sm text-gray-700">{{ followup.category }}</td>
                                            <td class="py-2 px-4 text-sm text-gray-700">{{ followup.contact }}</td>
                                            <td class="py-2 px-4 text-sm text-gray-700">{{ followup.date }}</td>
                                            <td class="py-2 px-4 text-sm">
                                                <span class="px-3 py-1 rounded-full text-xs"
                                                    :class="followup.status === 'Overdue' ? 'bg-red-200 text-red-800' : 'bg-green-200 text-green-800'">
                                                    {{ followup.status }}
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Row: Deals Funnel & Pending Tasks -->
        <div class="flex flex-col lg:flex-row gap-4 mt-4">
            <div class="flex-1 bg-white rounded-lg shadow">
                <div class="p-4">
                    <h2 class="text-lg font-medium text-gray-700">Pending Tasks</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full mt-4">
                            <thead class="bg-[#ECE6F0]">
                                <tr>
                                    <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Category</th>
                                    <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Lead/Deal Name
                                    </th>
                                    <th class="py-2 px-4 text-left text-sm font-medium text-gray-700">Task Count</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-[#F7F7FD] text-sm">
                                <tr v-for="(task, index) in pendingTasks" :key="index">
                                    <td class="py-2 px-4 text-sm text-gray-700">{{ task.category }}</td>
                                    <td class="py-2 px-4 text-sm text-gray-700">{{ task.name }}</td>
                                    <td class="py-2 px-4 text-sm text-gray-700">{{ task.count }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup>
import { ref } from 'vue';
import MetricCard from './CRM_pages/MetricCard.vue';
import MetricBox from './CRM_pages/MetricBox.vue';
import WinRateChart from './CRM_pages/WinRateChart.vue';
import LeadsDealsChart from './CRM_pages/LeadsDealsChart.vue';
import DealsFunnelChart from './CRM_pages/DealsFunnelChart.vue';
import ProjectDashboardIcon from '../icons/ProjectDashboardIcon.vue'


// Sample data - replace with your actual data source
const winRateData = ref([
    { name: 'Won', value: 13, percentage: 54 },
    { name: 'Lost', value: 12, percentage: 46 }
]);

const leadsDealsData = ref({
    months: ['Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec', 'Jan', 'Feb', 'Mar'],
    leads: [45, 42, 48, 45, 40, 45, 42, 45, 48, 42, 45, 40],
    deals: [35, 32, 35, 38, 32, 35, 32, 38, 28, 32, 35, 32]
});

const dealsFunnelData = ref([
    { name: 'New Deal', value: 100, percentage: 100 },
    { name: 'Qualified', value: 70, percentage: 70 },
    { name: 'Proposed', value: 50, percentage: 50 },
    { name: 'Negotiated', value: 30, percentage: 30 },
    { name: 'Closed', value: 16, percentage: 16 }
]);

const followups = ref([
    { category: 'Lead', contact: 'Kiran Mandal', date: '04 Mar 2024', status: 'Overdue' },
    { category: 'Deal', contact: 'Sheetal Patil', date: '08 Mar 2024', status: 'Overdue' },
    { category: 'Deal', contact: 'Disha Kumar', date: '04 Mar 2024', status: 'Due' },
    { category: 'Lead', contact: 'Ruha Luthra', date: '04 Mar 2024', status: 'Overdue' },
    { category: 'Lead', contact: 'Sheetal Patil', date: '04 Mar 2024', status: 'Due' }
]);

const pendingTasks = ref([
    { category: 'Lead', name: 'Kiran Mandal', count: 15 },
    { category: 'Deal', name: 'Sheetal Patil', count: 13 },
    { category: 'Deal', name: 'Disha Kumar', count: 12 },
    { category: 'Lead', name: 'Ruha Luthra', count: 11 },
    { category: 'Lead', name: 'Sheetal Patil', count: 10 }
]);
</script>