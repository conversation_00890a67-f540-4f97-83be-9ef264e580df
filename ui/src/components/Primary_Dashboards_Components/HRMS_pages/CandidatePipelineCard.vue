<template>
  <div class="bg-white rounded-lg shadow p-4">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Candidate Pipeline</h2>
    <div ref="chartContainer" style="width: 100%; height: 320px;"></div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue'
import * as echarts from 'echarts'
import { defineProps } from 'vue'

const props = defineProps({
  pipelineData: {
    type: Array,
    required: true
  }
})

const chartContainer = ref(null)
let chartInstance = null

const renderChart = () => {
  if (!chartContainer.value) return

  const colors = props.pipelineData.map(d => d.color || '#888')

  const chartData = props.pipelineData.map((d, i) => ({
    name: d.name,
    value: d.value,
    itemStyle: {
      color: colors[i]
    }
  }))

  chartInstance = echarts.init(chartContainer.value)
  chartInstance.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}%'
    },
    legend: {
      show: true,
      orient: 'vertical',
      left: 0,
      top: 'middle',
      data: props.pipelineData.map(d => d.name),
      icon: 'box',
      itemGap: 20,
      textStyle: {
        fontSize: 12,
        color: '#333'
      }
    },
    series: [
      {
        name: 'Candidates',
        type: 'funnel',
        left: '25%',
        top: 10,
        bottom: 20,
        width: '75%',
        min: 0,
        max: 100,
        minSize: '0%',
        maxSize: '100%',
        sort: 'descending',
        gap: 2,
        label: {
          show: true,
          position: 'inside',
          formatter: '{c} %',
          color: '#fff',
          fontSize: 10
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        data: chartData
      }
    ]
  })
}

onMounted(() => {
  renderChart()
})

watch(
  () => props.pipelineData,
  () => {
    if (chartInstance) {
      chartInstance.dispose()
    }
    renderChart()
  },
  { deep: true }
)
</script>
