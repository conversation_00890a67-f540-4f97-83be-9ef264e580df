<template>
  <div class="bg-white rounded-lg shadow p-4">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Recruitment Analysis</h2>
    <div class="overflow-x-auto">
      <table class="min-w-full">
        <thead>
          <tr class="bg-[#E0DDE8]">
            <th v-for="(header, index) in recruitmentData.headers" :key="index" class="py-2 px-6 text-left text-sm font-medium text-gray-700">
              {{ header }}
            </th>
          </tr>
        </thead>
        <tbody class="bg-[#F7F7FD]">
          <template v-for="(row, rowIndex) in recruitmentData.rows" :key="rowIndex">
            <!-- Main Row -->
            <tr class="border-b">
              <td class="py-2 px-3 text-sm" :class="row.color">
                <div class="relative flex items-center">
                  <span v-if="row.expandable" class="absolute cursor-pointer" @click="toggleExpand(rowIndex)">
                    <svg v-if="!expandedRows[rowIndex]" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-right"><path d="m9 18 6-6-6-6"/></svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-chevron-down"><path d="m6 9 6 6 6-6"/></svg>
                  </span>
                  <span class="ml-4">{{ row.label }}</span>
                </div>
              </td>
              <td v-for="(value, valueIndex) in row.values" :key="valueIndex" class="py-2 px-3 text-sm text-gray-700 text-center">
                {{ value }}
              </td>
            </tr>

            <!-- SubRows if expanded -->
            <tr v-if="expandedRows[rowIndex]" v-for="(subRow, subIndex) in row.subRows || []" :key="`${rowIndex}-${subIndex}`">
              <td class="py-2 px-6 text-sm" :class="subRow.color">
                └ {{ subRow.label }}
              </td>
              <td v-for="(val, idx) in subRow.values" :key="idx" class="py-2 px-3 text-sm text-gray-600 text-center">
                {{ val }}
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

  
  <script setup>
  import { defineProps, ref } from 'vue';
  
  const props = defineProps({
    recruitmentData: {
      type: Object,
      required: true
    }
  });
  
  const expandedRows = ref({});
  
  const toggleExpand = (rowIndex) => {
    expandedRows.value[rowIndex] = !expandedRows.value[rowIndex];
  };
  </script>