<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Source of Hires</h2>
      <div class="h-64">
        <apexchart
          type="pie"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    sourceData: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return props.sourceData.map(item => item.percentage);
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'pie',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      labels: props.sourceData.map(item => item.source),
      colors: props.sourceData.map(item => item.color),
      legend: {
        position: 'bottom',
        fontSize: '12px',
        markers: {
          width: 12,
          height: 12,
          radius: 0
        },
        itemMargin: {
          horizontal: 8,
          vertical: 5
        }
      },
      plotOptions: {
        pie: {
          donut: {
            size: '0%'
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      stroke: {
        width: 2,
        colors: ['#fff']
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + "%";
          }
        }
      }
    };
  });
  </script>