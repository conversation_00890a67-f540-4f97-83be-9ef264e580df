<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Exit Interview Trends</h2>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    exitData: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Exits',
      data: props.exitData.map(item => item.count)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '60%',
          borderRadius: 0,
          dataLabels: {
            position: 'top'
          }
        },
      },
      colors: ['#B8B0CA'],
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val;
        },
        offsetY: -20,
        style: {
          fontSize: '12px',
          colors: ['#666']
        }
      },
      xaxis: {
        categories: props.exitData.map(item => item.reason),
        labels: {
          style: {
            fontSize: '10px'
          }
        }
      },
      yaxis: {
        title: {
          text: ''
        },
        min: 0,
        max: 30,
        tickAmount: 6,
        labels: {
          formatter: function(val) {
            return val.toFixed(0);
          }
        }
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.5
        },
        yaxis: {
          lines: {
            show: true
          }
        }
      }
    };
  });
  </script>