<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Leave Trends</h2>
      <div class="flex items-center justify-end mb-4 space-x-4">
        <div class="flex items-center">
          <span class="w-3 h-3 rounded-full bg-purple-900 mr-2"></span>
          <span class="text-sm text-gray-600">Casual Leaves</span>
        </div>
        <div class="flex items-center">
          <span class="w-3 h-3 rounded-full bg-purple-500 mr-2"></span>
          <span class="text-sm text-gray-600">Sick Leave</span>
        </div>
        <div class="flex items-center">
          <span class="w-3 h-3 rounded-full bg-purple-200 mr-2"></span>
          <span class="text-sm text-gray-600">Unpaid Leave</span>
        </div>
      </div>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    leaveData: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [
      {
        name: 'Casual Leaves',
        data: props.leaveData.map(item => item.casual)
      },
      {
        name: 'Sick Leave',
        data: props.leaveData.map(item => item.sick)
      },
      {
        name: 'Unpaid Leave',
        data: props.leaveData.map(item => item.unpaid)
      }
    ];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        stacked: true,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 0
        },
      },
      colors: ['#564579', '#8F82AA', '#CCC2DC'],
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: props.leaveData.map(item => item.department),
        labels: {
          style: {
            fontSize: '12px'
          }
        }
      },
      yaxis: {
        title: {
          text: ''
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(0);
          }
        }
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.5
        }
      },
      legend: {
        show: false
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val + " days";
          }
        }
      }
    };
  });
  </script>