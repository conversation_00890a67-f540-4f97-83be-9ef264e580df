<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">HR Analysis</h2>
      
      <div class="overflow-x-auto mb-4 shadow-2xl">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E0DDE8]">
              <th v-for="(dept, index) in departments" :key="index" class="py-4 px-3 text-left text-sm font-medium text-gray-700">
                {{ dept.name }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr class="border-b">
              <td class="py-2 px-3 text-sm text-gray-700">Employees</td>
              <td v-for="(dept, index) in departments.slice(1)" :key="index" class="py-4 px-3 text-sm text-gray-700 text-center">
                {{ dept.employees }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
  
      <div class="overflow-x-auto shadow-2xl">
        <table class="min-w-full">
          <thead>
            <tr class="bg-[#E0DDE8]">
              <th class="py-2 px-3 text-left text-sm font-medium text-gray-700">Year</th>
              <th v-for="(item, index) in attritionData" :key="index" class="py-4 px-3 text-center text-sm font-medium text-gray-700">
                {{ item.year }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="py-2 px-3 text-sm text-gray-700">Attrition rate</td>
              <td v-for="(item, index) in attritionData" :key="index" class="py-4 px-3 text-sm text-gray-700 text-center">
                {{ item.rate }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps } from 'vue';
  
  defineProps({
    departments: {
      type: Array,
      required: true
    },
    attritionData: {
      type: Array,
      required: true
    }
  });
  </script>