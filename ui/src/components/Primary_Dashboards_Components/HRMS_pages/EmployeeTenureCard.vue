<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Employee Tenure</h2>
      <div class="h-64">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { defineProps, computed } from 'vue';
  
  const props = defineProps({
    tenureData: {
      type: Array,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return [{
      name: 'Employees',
      data: props.tenureData.map(item => item.count)
    }];
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '70%',
          borderRadius: 0
        },
      },
      colors: ['#A49AB4'],
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: props.tenureData.map(item => item.years),
        title: {
          text: 'Years'
        },
        labels: {
          style: {
            fontSize: '10px'
          }
        }
      },
      yaxis: {
        title: {
          text: 'Employees'
        },
        min: 0,
        max: 40,
        tickAmount: 4,
        labels: {
          formatter: function(val) {
            return val.toFixed(0);
          }
        }
      },
      grid: {
        borderColor: '#e7e7e7',
        row: {
          colors: ['#f3f3f3', 'transparent'],
          opacity: 0.5
        },
        yaxis: {
          lines: {
            show: true
          }
        },
        xaxis: {
          lines: {
            show: false
          }
        }
      },
      annotations: {
        yaxis: [
          {
            y: 10,
            borderColor: '#ddd',
            borderWidth: 1,
            strokeDashArray: 5,
            label: {
              text: '10',
              position: 'left',
              textAnchor: 'start',
              style: {
                color: '#888',
                background: 'transparent'
              }
            }
          },
          {
            y: 20,
            borderColor: '#ddd',
            borderWidth: 1,
            strokeDashArray: 5,
            label: {
              text: '20',
              position: 'left',
              textAnchor: 'start',
              style: {
                color: '#888',
                background: 'transparent'
              }
            }
          },
          {
            y: 30,
            borderColor: '#ddd',
            borderWidth: 1,
            strokeDashArray: 5,
            label: {
              text: '30',
              position: 'left',
              textAnchor: 'start',
              style: {
                color: '#888',
                background: 'transparent'
              }
            }
          },
          {
            y: 40,
            borderColor: '#ddd',
            borderWidth: 1,
            strokeDashArray: 5,
            label: {
              text: '40',
              position: 'left',
              textAnchor: 'start',
              style: {
                color: '#888',
                background: 'transparent'
              }
            }
          }
        ]
      }
    };
  });
  </script>