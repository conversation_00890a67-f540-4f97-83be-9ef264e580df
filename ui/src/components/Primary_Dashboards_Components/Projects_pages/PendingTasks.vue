<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Team Wise Avg Sq/Person</h2>
      
      <div class="h-72 overflow-auto">
        <table class="min-w-full">
          <thead class="bg-[#ECE6F0]">
            <tr>
              <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Team</th>
              <th class="px-4 py-2 text-center text-sm font-medium text-gray-700">Team Size</th>
              <th class="px-4 py-2 text-right text-sm font-medium text-gray-700">Avg Sq/Person</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 bg-[#F7F7FD] text-sm">
            <tr v-for="(task, index) in pendingTasksData" :key="index">
              <td class="px-4 py-3 text-sm text-gray-700">{{ task.team }}</td>
              <td class="px-4 py-3 text-center text-sm text-gray-700">{{ task.size }}</td>
              <td class="px-4 py-3 text-right text-sm text-gray-700">{{ task.avgSqPerPerson.toLocaleString() }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    pendingTasksData: {
      type: Array,
      required: true
    }
  });
  </script>
  
  