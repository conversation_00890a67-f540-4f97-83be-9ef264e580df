<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">Projects Analysis</h2>
      
      <div class="grid grid-cols-2 gap-6 mb-4">
        <div class="bg-white rounded-sm p-6 shadow-xl">
          <div class="text-sm text-gray-500">Active Projects</div>
          <div class="text-2xl font-semibold">{{ projectsData.activeProjects }}</div>
        </div>
        
        <div class="bg-white  rounded-sm p-6 shadow-xl">
          <div class="text-sm text-gray-500">Projects Completed In Current FY</div>
          <div class="text-2xl font-semibold">{{ projectsData.completedProjects }}</div>
        </div>
        
        <div class="bg-red-200   rounded-sm p-6 shadow-xl">
          <div class="flex justify-between">
            <div class="text-sm text-gray-500">Delayed Projects</div>
            <div class="bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {{ projectsData.delayedProjects.count }}
            </div>
          </div>
          <div class="text-2xl font-semibold">{{ projectsData.delayedProjects.percentage }}%</div>
        </div>
        
        <div class="bg-green-200 border rounded-sm p-6 shadow-xl">
          <div class="flex justify-between">
            <div class="text-sm text-gray-500">Projects On Track</div>
            <div class="bg-green-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {{ projectsData.onTrackProjects.count }}
            </div>
          </div>
          <div class="text-2xl font-semibold">{{ projectsData.onTrackProjects.percentage }}%</div>
        </div>
      </div>
      
      <div class="mt-8">
        <h3 class="text-base font-medium text-gray-700 mb-3">Project Status</h3>
        <div class="h-80 shadow-xl p-2">
          <apexchart
            type="pie"
            height="100%"
            :options="chartOptions"
            :series="chartSeries"
          ></apexchart>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  
  const props = defineProps({
    projectsData: {
      type: Object,
      required: true
    }
  });
  
  const chartSeries = computed(() => {
    return props.projectsData.statusDistribution.map(item => item.percentage);
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'pie',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      labels: props.projectsData.statusDistribution.map(item => item.status),
      colors: ['#6b5ca5', '#9d94c0', '#b7b6db', '#d1d0e6'],
      legend: {
        position: 'bottom',
        horizontalAlign: 'center',
        fontSize: '12px'
      },
      plotOptions: {
        pie: {
          donut: {
            size: '0%'
          }
        }
      },
      dataLabels: {
        formatter: function(val) {
          return val.toFixed(0) + '%';
        }
      },
      tooltip: {
        y: {
          formatter: function(value, { seriesIndex }) {
            const item = props.projectsData.statusDistribution[seriesIndex];
            return `${value.toFixed(2)}% (${item.count} projects)`;
          }
        }
      },
      responsive: [{
        breakpoint: 480,
        options: {
          chart: {
            height: 300
          },
          legend: {
            position: 'bottom'
          }
        }
      }]
    };
  });
  </script>
  
  