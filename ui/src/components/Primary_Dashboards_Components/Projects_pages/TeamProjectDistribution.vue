<template>
    <div class="bg-white rounded-lg shadow p-4">
      <h2 class="text-lg font-medium text-gray-700 mb-4">{{ title }}</h2>
      
      <div class="h-72">
        <apexchart
          type="bar"
          height="100%"
          :options="chartOptions"
          :series="chartSeries"
        ></apexchart>
      </div>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  
  const props = defineProps({
    title: {
      type: String,
      required: true
    },
    chartData: {
      type: Object,
      required: true
    },
    yAxisTitle: {
      type: String,
      default: ''
    }
  });
  
  const chartSeries = computed(() => {
    return props.chartData.series;
  });
  
  const chartOptions = computed(() => {
    return {
      chart: {
        type: 'bar',
        fontFamily: 'inherit',
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: false,
          columnWidth: '55%',
          borderRadius: 2
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function(val) {
          return val;
        },
        style: {
          fontSize: '12px',
          colors: ['#fff']
        }
      },
      stroke: {
        show: true,
        width: 2,
        colors: ['transparent']
      },
      xaxis: {
        categories: props.chartData.categories,
        labels: {
          style: {
            fontSize: '12px'
          }
        }
      },
      yaxis: {
        title: {
          text: props.yAxisTitle
        },
        labels: {
          formatter: function(val) {
            return val.toFixed(0);
          }
        }
      },
      colors: ['#6b5ca5'],
      fill: {
        opacity: 1
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val;
          }
        }
      },
      grid: {
        borderColor: '#f1f1f1',
        row: {
          colors: ['#f8f9fa', 'transparent'],
          opacity: 0.5
        }
      }
    };
  });
  </script>
  
  