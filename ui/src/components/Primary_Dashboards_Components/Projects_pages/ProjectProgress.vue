<template>
  <div class="bg-white rounded-lg shadow p-4">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Project Progress</h2>

    <div class="h-56 overflow-x-auto relative">
      <table class="min-w-full">
        <thead class="bg-[#ECE6F0]">
          <tr>
            <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Project Name</th>
            <th class="px-4 py-2 text-center text-sm font-medium text-gray-700">Status</th>
            <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Progress</th>
            <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Current Milestone</th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 bg-[#F7F7FD] text-sm">
          <tr v-for="(project, index) in progressData" :key="index">
            <td class="px-4 py-3 text-sm text-gray-700">{{ project.name }}</td>
            <td class="px-4 py-3 text-center">
              <span v-if="project.status === 'danger'" class="text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" viewBox="0 0 22 20" fill="none">
                  <path d="M0 19.69H22L11 0.689987L0 19.69ZM12 16.69H10V14.69H12V16.69ZM12 12.69H10V8.68999H12V12.69Z"
                    fill="#B00020" />
                </svg>
              </span>
              <span v-else-if="project.status === 'success'" class="text-green-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                  <path
                    d="M6.69999 18.19L1.04999 12.54L2.47499 11.14L6.72499 15.39L8.12499 16.79L6.69999 18.19ZM12.35 18.19L6.69999 12.54L8.09999 11.115L12.35 15.365L21.55 6.16501L22.95 7.59001L12.35 18.19ZM12.35 12.54L10.925 11.14L15.875 6.19001L17.3 7.59001L12.35 12.54Z"
                    fill="#249F43" />
                </svg>
              </span>
              <span v-else-if="project.status === 'warning'" class="text-yellow-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                  <path
                    d="M5 20.19V18.19H6.6L8.575 11.615C8.70833 11.1817 8.95417 10.8358 9.3125 10.5775C9.67083 10.3192 10.0667 10.19 10.5 10.19H13.5C13.9333 10.19 14.3292 10.3192 14.6875 10.5775C15.0458 10.8358 15.2917 11.1817 15.425 11.615L17.4 18.19H19V20.19H5ZM8.7 18.19H15.3L13.5 12.19H10.5L8.7 18.19ZM11 8.19V3.19H13V8.19H11ZM16.95 10.665L15.525 9.24L19.075 5.715L20.475 7.115L16.95 10.665ZM18 15.19V13.19H23V15.19H18ZM7.05 10.665L3.525 7.115L4.925 5.715L8.475 9.24L7.05 10.665ZM1 15.19V13.19H6V15.19H1Z"
                    fill="#FFCF31" />
                </svg>
              </span>
            </td>
            <td class="px-4 py-3">
              <div class="flex items-center">
                <div class="w-44 bg-purple-100 h-2.5">
                  <div class="h-2.5 bg-purple-600" :style="{ width: project.progress + '%' }"></div>
                </div>
                <span class="ml-2 text-sm text-gray-600">{{ project.progress }}%</span>
              </div>
            </td>
            <td class="px-4 py-3 text-sm text-gray-700">{{ project.milestone }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
defineProps({
  progressData: {
    type: Array,
    required: true
  }
});
</script>