<template>
  <div class="p-4">
    <div class="flex items-center gap-2 mb-4">
      <ProjectDashboardIcon class="w-4 h-4" />
      <h1 class="text-xl font-semibold">Projects Dashboard</h1>
    </div>

    <div class="flex justify-between gap-4 mb-4">
      <div class="w-4/12 flex flex-col">
        <ProjectsAnalysis :projectsData="projectsData" class="h-full" />
      </div>
      <div class="flex flex-col gap-4 flex-1 h-full">
        <ProjectProgress :progressData="projectProgressData" class="h-1/2" />
        <TeamProjectDistribution title="Team-wise Project Sq. feet Distribution" :chartData="teamSquareFeetData"
          yAxisTitle="Square Feet" class="h-1/2 min-w-[300px]" />
      </div>
    </div>

    <div class="w-full flex justify-between gap-4">
      <TeamProjectDistribution title="Team-wise Project Count Distribution" :chartData="teamProjectCountData"
        yAxisTitle="Project Count" class="flex-1" />
      <PendingTasks :pendingTasksData="pendingTasksData" class="w-1/3" />
    </div>
  </div>
</template>



<script setup>
import { onMounted, ref } from 'vue';
import ProjectsAnalysis from './Projects_pages/ProjectsAnalysis.vue';
import ProjectProgress from './Projects_pages/ProjectProgress.vue';
import TeamProjectDistribution from './Projects_pages/TeamProjectDistribution.vue';
import PendingTasks from './Projects_pages/PendingTasks.vue';
import ProjectDashboardIcon from '../icons/ProjectDashboardIcon.vue'
import { createResource } from 'frappe-ui';

// Dynamic data for the dashboard
const projectsData = ref({
  activeProjects: 0,
  completedProjects: 0,
  delayedProjects: {
    percentage: 0,
    count: 0
  },
  onTrackProjects: {
    percentage: 0,
    count: 0
  },
  statusDistribution: [
    // { status: 'Active', percentage: 15 },
    // { status: 'Completed', percentage: 55 },
    // { status: 'On hold', percentage: 25 },
    // { status: 'Dropped', percentage: 5 }
  ]
});

const projectProgressData = ref([
  {
    name: 'Breach Candy Square',
    status: 'danger',
    progress: 60,
    milestone: 'Consultation'
  },
  {
    name: 'Urban Oasis: Coastal Luxury A 701',
    status: 'success',
    progress: 50,
    milestone: 'Concept Development'
  },
  {
    name: 'Breach Candy Square',
    status: 'warning',
    progress: 25,
    milestone: 'Design Presentation & Review'
  },
  {
    name: 'Bohemian Bliss: By the Bay B 405',
    status: 'success',
    progress: 75,
    milestone: 'Space Planning & Layout'
  }
]);

const teamMembers = ['KrishnaMurti', 'Ayan', 'Riya', 'Prajyot', 'Ashok', 'Arun', 'Abhishek', 'Deepak', 'Pratik', 'Ishan'];

const teamSquareFeetData = ref({
  categories: [],
  series: [{
    name: 'Square Feet',
    data: []
  }]
});

const teamProjectCountData = ref({
  categories: [],
  series: [{
    name: 'Project Count',
    data: []
  }]
});

const pendingTasksData = ref([
  // { team: 'Kiran Mandal', size: 15, avgSqPerPerson: 12000 },
  // { team: 'Sheetal Patil', size: 13, avgSqPerPerson: 10000 },
  // { team: 'Disha Kumar', size: 12, avgSqPerPerson: 7500 },
  // { team: 'Ruha Luthra', size: 11, avgSqPerPerson: 18000 },
  // { team: 'Sheetal Patil', size: 10, avgSqPerPerson: 3550 },
  // { team: 'Kiran Mandal', size: 15, avgSqPerPerson: 12000 },
  // { team: 'Sheetal Patil', size: 13, avgSqPerPerson: 10000 },
  // { team: 'Disha Kumar', size: 12, avgSqPerPerson: 7500 },
  // { team: 'Ruha Luthra', size: 11, avgSqPerPerson: 18000 },
  // { team: 'Sheetal Patil', size: 10, avgSqPerPerson: 3550 },
  // { team: 'Kiran Mandal', size: 15, avgSqPerPerson: 12000 },
  // { team: 'Sheetal Patil', size: 13, avgSqPerPerson: 10000 },
  // { team: 'Disha Kumar', size: 12, avgSqPerPerson: 7500 },
  // { team: 'Ruha Luthra', size: 11, avgSqPerPerson: 18000 },
  // { team: 'Sheetal Patil', size: 10, avgSqPerPerson: 3550 },
  // { team: 'Arun Kumar', size: 30, avgSqPerPerson: 7556 }
]);
function get_project_details(){
  createResource({
    url:"inspira.inspira.api.dashboards.projects.get_project_details",
    makeParams: () => ({ }),
    auto:true,
    onSuccess: (res) => {
      console.log("get_project_details",res)
       projectsData.value = res;
    },
    onError:(error)=>{
      console.log(error)
    }
  })
}
function get_project_progress(){
  createResource({
    url:"inspira.inspira.api.dashboards.projects.get_project_progress",
    makeParams: () => ({ }),
    auto:true,
    onSuccess: (res) => {
      console.log("get_project_details",res)
       projectProgressData.value = res;
    },
    onError:(error)=>{
      console.log(error)
    }
  })
}
function get_sq_feet_distribution(){
  createResource({
    url:"inspira.inspira.api.dashboards.projects.get_sq_feet_distribution",
    makeParams: () => ({ }),
    auto:true,
    onSuccess: (res) => {
      console.log("get_project_details",res)
       teamSquareFeetData.value = res;
    },
    onError:(error)=>{
      console.log(error)
    }
  })
}
function get_project_distribution(){
  createResource({
    url:"inspira.inspira.api.dashboards.projects.get_project_distribution",
    makeParams: () => ({ }),
    auto:true,
    onSuccess: (res) => {
      console.log("get_project_details",res)
       teamProjectCountData.value = res;
    },
    onError:(error)=>{
      console.log(error)
    }
  })
}
function get_avg_sq_feet_per_person(){
  createResource({
    url:"inspira.inspira.api.dashboards.projects.get_avg_sq_feet_per_person",
    makeParams: () => ({ }),
    auto:true,
    onSuccess: (res) => {
      console.log("get_project_details",res)
       pendingTasksData.value = res;
    },
    onError:(error)=>{
      console.log(error)
    }
  })
}
onMounted(()=>{
  get_project_details()
  get_project_progress()
  get_sq_feet_distribution()
  get_project_distribution()
  get_avg_sq_feet_per_person()
})
</script>