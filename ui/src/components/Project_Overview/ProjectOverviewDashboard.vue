<template>
  <div class="flex gap-6 font-sans">
    <div class="flex-1 flex flex-col gap-6">
      <div class="flex justify-between items-center">
        <h1 class="text-xl font-semibold text-gray-900">Project Overview</h1>
      </div>

      <div class="flex gap-4">
        <MetricsCard title="Budget" :value="projectData.budget.value" :status="projectData.budget.status"
          :statusText="projectData.budget.statusText" />

        <MetricsCard title="Timeline" :value="projectData.timeline.value" :status="projectData.timeline.status"
          :statusText="projectData.timeline.statusText" />

        <TasksCard :completed="projectData.tasks.completed" :total="projectData.tasks.total" />
      </div>

      <TimelineSection :startDate="projectData.timelineDates.start" :endDate="projectData.timelineDates.end" />

      <RecentActivitySection :activities="projectData.recentActivities" />
    </div>

    <div class="w-[360px] flex flex-col gap-6">
      <ClientCard :client="projectData.client" />

      <MilestonesCard :milestones="projectData.upcomingMilestones" />

      <TeamSection :members="projectData.team" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import MetricsCard from './Projectoverview_pages/MetricsCard.vue';
import TasksCard from './Projectoverview_pages/TasksCard.vue';
import TimelineSection from './Projectoverview_pages/TimelineSection.vue';
import RecentActivitySection from './Projectoverview_pages/RecentActivitySection.vue';
import ClientCard from './Projectoverview_pages/ClientCard.vue';
import MilestonesCard from './Projectoverview_pages/MilestonesCard.vue';
import TeamSection from './Projectoverview_pages/TeamSection.vue';

const projectData = ref({
  budget: {
    value: '₹25,00,000',
    status: 'success',
    statusText: 'On track'
  },
  timeline: {
    value: '48%',
    status: 'danger',
    statusText: 'Behind Schedule'
  },
  tasks: {
    completed: 1,
    total: 5
  },
  timelineDates: {
    start: new Date('2023-09-01'),
    end: new Date('2024-03-15')
  },
  client: {
    name: 'Sara Jay',
    company: 'Jay Family Trust',
    initials: 'SJ',
    email: '<EMAIL>',
    phone: '97872 36571'
  },
  upcomingMilestones: [
    {
      title: 'Design Development Approval',
      date: 'Mar 13',
      daysRemaining: 7
    },
    {
      title: 'Finish Selections Finalized',
      date: 'Mar 20',
      daysRemaining: 7
    }
  ],
  recentActivities: [
    {
      user: 'Mithun Chatterjee',
      action: 'Uploaded new bathroom renderings',
      timeAgo: '2 hours ago'
    },
    {
      user: 'Priya Patel',
      action: 'Updated the project timeline',
      timeAgo: '5 hours ago'
    },
    {
      user: 'Amna Shah',
      action: 'Commented on kitchen design proposal',
      timeAgo: '24 hours ago'
    }
  ],
  team: [
    {
      name: 'Mithun Chatterjee',
      role: 'Lead Designer',
      initials: 'MC'
    },
    {
      name: 'Priya Patel',
      role: 'Project Manager',
      initials: 'PP'
    },
    {
      name: 'Dravid Wilson',
      role: 'Procurement Specialist',
      initials: 'DW'
    }
  ]
});
</script>