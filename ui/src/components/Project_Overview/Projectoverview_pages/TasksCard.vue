<template>
    <div class="bg-gray-50 rounded-lg p-4 flex-1">
      <div class="text-sm text-gray-500 mb-2">Tasks</div>
      <div class="text-3xl font-bold text-gray-900 mb-2">{{ completed }}/{{ total }}</div>
      <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
        <div class="h-full bg-indigo-500 rounded-full" :style="{ width: progressWidth }"></div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  
  const props = defineProps({
    completed: {
      type: Number,
      required: true
    },
    total: {
      type: Number,
      required: true
    }
  });
  
  const progressWidth = computed(() => {
    return `${(props.completed / props.total) * 100}%`;
  });
  </script>