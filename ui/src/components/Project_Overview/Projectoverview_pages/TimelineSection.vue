<template>
    <div class="mt-4">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Timeline</h2>
      <div class="flex items-center py-6">
        <div class="text-sm text-gray-500 w-24">{{ formatDate(startDate) }}</div>
        <div class="flex-1 h-px bg-gray-200 relative">
          <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3 h-3 rounded-full bg-gray-500"></div>
        </div>
        <div class="text-sm text-gray-500 w-24 text-right">{{ formatDate(endDate) }}</div>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    startDate: {
      type: Date,
      required: true
    },
    endDate: {
      type: Date,
      required: true
    }
  });
  
  const formatDate = (date) => {
    const options = { month: 'short', day: 'numeric', year: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  };
  </script>