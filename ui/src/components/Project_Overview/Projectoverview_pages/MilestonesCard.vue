<template>
    <div class="bg-white rounded-lg p-4 shadow-sm">
      <h2 class="text-base font-semibold text-gray-900 mb-4">Upcoming Milestones</h2>
      <div class="flex flex-col gap-4">
        <div v-for="(milestone, index) in milestones" :key="index" class="p-3 rounded-lg bg-gray-50">
          <div class="flex justify-between mb-2">
            <div class="font-medium text-gray-900">{{ milestone.title }}</div>
            <div class="text-xs text-gray-900 bg-gray-100 px-2 py-0.5 rounded-full">{{ milestone.date }}</div>
          </div>
          <div class="flex items-center text-sm text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
            {{ milestone.daysRemaining }} days remaining
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    milestones: {
      type: Array,
      required: true
    }
  });
  </script>