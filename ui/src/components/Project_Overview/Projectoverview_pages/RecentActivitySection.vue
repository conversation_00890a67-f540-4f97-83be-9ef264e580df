<template>
    <div class="mt-4">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
      <div class="flex flex-col">
        <div v-for="(activity, index) in activities" :key="index" 
             class="flex justify-between py-4 border-b border-gray-200 last:border-b-0">
          <div class="flex flex-col">
            <div class="font-medium text-gray-900">{{ activity.user }}</div>
            <div class="text-sm text-gray-500">{{ activity.action }}</div>
          </div>
          <div class="text-sm text-gray-500">{{ activity.timeAgo }}</div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    activities: {
      type: Array,
      required: true
    }
  });
  </script>