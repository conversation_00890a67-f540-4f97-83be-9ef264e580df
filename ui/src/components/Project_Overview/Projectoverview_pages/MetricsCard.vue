<template>
  <div class="bg-gray-50 rounded-lg p-4 flex-1">
    <div class="text-sm text-gray-500 mb-2">{{ title }}</div>
    <div class="text-3xl font-bold text-gray-900 mb-2">{{ value }}</div>
    <div :class="[
      'text-sm',
      status === 'success' ? 'text-green-500' : 
      status === 'danger' ? 'text-red-500' : 
      'text-gray-500'
    ]">{{ statusText }}</div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  value: {
    type: String,
    required: true
  },
  status: {
    type: String,
    default: 'neutral'
  },
  statusText: {
    type: String,
    default: ''
  }
});
</script>