<template>
    <div class="bg-white rounded-lg p-4 shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-900">Team</h2>
        <button class="text-sm text-blue-600 hover:text-blue-800">Manage Team</button>
      </div>
      <div class="flex flex-col gap-4">
        <div v-for="(member, index) in members" :key="index" class="flex items-center">
          <div :class="[
            'w-9 h-9 rounded-full flex items-center justify-center font-semibold text-white mr-3',
            getAvatarColorClass(index)
          ]">
            {{ member.initials }}
          </div>
          <div class="flex flex-col">
            <div class="font-medium text-gray-900">{{ member.name }}</div>
            <div class="text-sm text-gray-500">{{ member.role }}</div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    members: {
      type: Array,
      required: true
    }
  });
  
  // Function to get different avatar background colors using Tailwind classes
  const getAvatarColorClass = (index) => {
    const colors = [
      'bg-purple-400', 
      'bg-blue-400', 
      'bg-green-400', 
      'bg-orange-400', 
      'bg-pink-400'
    ];
    return colors[index % colors.length];
  };
  </script>