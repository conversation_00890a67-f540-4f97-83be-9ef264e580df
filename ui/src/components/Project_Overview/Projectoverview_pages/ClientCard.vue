<template>
    <div class="bg-white rounded-lg p-4 shadow-sm">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-base font-semibold text-gray-900">Client</h2>
        <button class="text-sm text-blue-600 hover:text-blue-800">View Details</button>
      </div>
      <div class="flex items-center mb-4">
        <div class="w-10 h-10 rounded-full bg-purple-100 text-purple-600 flex items-center justify-center font-semibold mr-3">
          {{ client.initials }}
        </div>
        <div class="flex flex-col">
          <div class="font-medium text-gray-900">{{ client.name }}</div>
          <div class="text-sm text-gray-500">{{ client.company }}</div>
        </div>
      </div>
      <div class="flex flex-col gap-2">
        <div class="flex justify-between">
          <div class="text-sm text-gray-500">Email</div>
          <div class="text-sm text-gray-900">{{ client.email }}</div>
        </div>
        <div class="flex justify-between">
          <div class="text-sm text-gray-500">Phone</div>
          <div class="text-sm text-gray-900">{{ client.phone }}</div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  defineProps({
    client: {
      type: Object,
      required: true
    }
  });
  </script>