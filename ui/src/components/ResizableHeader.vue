<template>
  <th class="resizable-column py-1 px-3 text-sm cust_border" :style="{ width: `${width}px`, minWidth: `${width}px`, maxWidth: `${width}px` }">
    <slot>{{ label }}</slot>
    <div class="column-resizer" @mousedown="handleMouseDown"></div>
  </th>
</template>

<script setup>
import { ref, onUnmounted } from 'vue'

const props = defineProps({
  columnName: String,
  label: String,
  width: {
    type: Number,
    required: true
  },
  minWidth: {
    type: Number,
    default: 50 // Default minimum width
  },
  maxWidth: {
    type: Number,
    default: Infinity // Default maximum width (no limit)
  },
  isLastColumn: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:width'])

const isResizing = ref(false)
const startX = ref(0)
const startWidth = ref(0)

const handleMouseDown = (e) => {
  e.preventDefault()
  isResizing.value = true
  startX.value = e.pageX
  startWidth.value = props.width

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  document.body.classList.add('resizing')
}

const handleMouseMove = (e) => {
  if (!isResizing.value) return
  const diff = e.pageX - startX.value
  // const newWidth = Math.max(50, startWidth.value + diff)
  const newWidth = Math.max(props.minWidth, Math.min(props.maxWidth, startWidth.value + diff))
  emit('update:width', newWidth)
}

const handleMouseUp = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.classList.remove('resizing')
}

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.body.classList.remove('resizing')
})
</script>

<style scoped>
.resizable-column {
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  background: inherit;
}

.column-resizer {
  position: absolute;
  right: -3px;
  top: 0;
  height: 100%;
  width: 6px;
  cursor: col-resize;
  user-select: none;
  background-color: transparent;
  z-index: 1;
}

.column-resizer:hover,
.column-resizer.resizing {
  background-color: #CAC4D0;
}

.cust_border {
  border-right: 1px solid #CAC4D0;
}

.last-column-header {
  position: sticky;
  right: 0;
  background: #ece6f0;
  text-align: end;
}
</style>