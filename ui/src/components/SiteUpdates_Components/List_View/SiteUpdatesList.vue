<template>
    <div class="w-full">
      <!-- Search and Actions Bar -->
      <div class="flex justify-between items-center mb-4">
        <div class="relative w-72">
          <input 
            type="text" 
            placeholder="Search" 
            v-model="searchQuery"
            class="w-full border border-gray-300 rounded-md py-2 px-3 pr-10 focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
          />
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.3-4.3"/></svg>
          </div>
        </div>
        
        <div class="flex gap-2">
          <button class="flex items-center gap-1 border border-gray-300 rounded-md py-1 px-3 bg-white hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/><path d="M14 2v6h6"/><path d="M16 13H8"/><path d="M16 17H8"/><path d="M10 9H8"/></svg>
            Report
          </button>
          <button class="flex items-center gap-1 border border-gray-300 rounded-md py-1 px-3 bg-white hover:bg-gray-50">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 5v14"/><path d="M5 12h14"/></svg>
            Add Entry
          </button>
        </div>
      </div>
      
      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full border-collapse">
          <thead>
            <tr>
              <th v-for="column in columns" :key="column.key" 
                  class="bg-[#f0f0f7] text-left py-2 px-4 border border-gray-200 font-medium text-gray-700">
                {{ column.label }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in filteredData" :key="index" class="hover:bg-gray-50">
              <td class="py-3 px-4 border border-gray-200">{{ formatDate(item.date) }}</td>
              <td class="py-3 px-4 border border-gray-200">{{ item.teamMember }}</td>
              <td class="py-3 px-4 border border-gray-200">{{ item.area }}</td>
              <td class="py-3 px-4 border border-gray-200">
                <div v-if="item.currentUpdate" class="inline-block bg-gray-100 rounded-full px-3 py-1 text-sm">
                  {{ item.currentUpdate }} Work Updates
                </div>
              </td>
              <td class="py-3 px-4 border border-gray-200">
                <div class="inline-block bg-gray-100 rounded-full px-3 py-1 text-sm">
                  {{ item.futurePlan }} Future Plans
                </div>
              </td>
              <td class="py-3 px-4 border border-gray-200">
                <div class="inline-block bg-gray-100 rounded-full px-3 py-1 text-sm">
                  {{ item.issues }} Issues - Reviewed
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  
  // Table columns
  const columns = [
    { key: 'date', label: 'Date' },
    { key: 'teamMember', label: 'Team Member' },
    { key: 'area', label: 'Area' },
    { key: 'currentUpdate', label: 'Current Update' },
    { key: 'futurePlan', label: 'Future Plan' },
    { key: 'issues', label: 'Issues' }
  ];
  
  // Mock data
  const siteUpdatesData = ref([
    { date: new Date(2024, 2, 2), teamMember: 'Kunal Mishra', area: 'Kitchen', currentUpdate: 5, futurePlan: 6, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Ajit Thakur', area: 'Lobby', currentUpdate: 7, futurePlan: 6, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Nidhi Patil', area: 'Kitchen', currentUpdate: null, futurePlan: 4, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Kunal Mishra', area: 'Veranda', currentUpdate: null, futurePlan: 6, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Rishi Patekar', area: 'Lobby', currentUpdate: null, futurePlan: 4, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Ajit Thakur', area: 'Kitchen', currentUpdate: null, futurePlan: 3, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Kunal Mishra', area: 'Bedroom', currentUpdate: null, futurePlan: 4, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Vidit Narayan', area: 'Kitchen', currentUpdate: null, futurePlan: 4, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Kunal Mishra', area: 'Veranda', currentUpdate: null, futurePlan: 3, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Nidhi Patil', area: 'Lobby', currentUpdate: null, futurePlan: 1, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Ajit Thakur', area: 'Balcony', currentUpdate: null, futurePlan: 3, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Rishi Patekar', area: 'Lobby', currentUpdate: null, futurePlan: 3, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Kunal Mishra', area: 'Bedroom', currentUpdate: null, futurePlan: 1, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Ajit Thakur', area: 'Veranda', currentUpdate: null, futurePlan: 6, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Vidit Narayan', area: 'Balcony', currentUpdate: null, futurePlan: 5, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Nidhi Patil', area: 'Veranda', currentUpdate: null, futurePlan: 1, issues: 6 },
    { date: new Date(2024, 2, 2), teamMember: 'Rishi Patekar', area: 'Bedroom', currentUpdate: null, futurePlan: 1, issues: 6 }
  ]);
  
  // Search functionality
  const searchQuery = ref('');
  
  const filteredData = computed(() => {
    if (!searchQuery.value) {
      return siteUpdatesData.value;
    }
    
    const query = searchQuery.value.toLowerCase();
    return siteUpdatesData.value.filter(item => 
      item.teamMember.toLowerCase().includes(query) ||
      item.area.toLowerCase().includes(query) ||
      formatDate(item.date).toLowerCase().includes(query)
    );
  });
  
  // Format date as "DD MMM YYYY"
  const formatDate = (date) => {
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: 'short',
      year: 'numeric'
    });
  };
  </script>