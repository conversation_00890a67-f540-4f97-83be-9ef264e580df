<template>
  <div class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50" @click="$emit('close')">
    <div class="relative max-w-4xl max-h-screen p-4" @click.stop>
      <!-- Close button -->
      <button 
        class="absolute top-2 right-2 bg-white rounded-full p-2 shadow-lg z-10"
        @click="$emit('close')"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 6 6 18"/><path d="m6 6 12 12"/></svg>
      </button>
      
      <!-- Image -->
      <img :src="image" alt="Site Update Preview" class="max-w-full max-h-[90vh] object-contain">
    </div>
  </div>
</template>

<script setup>
defineProps({
  image: {
    type: String,
    required: true
  }
});

defineEmits(['close']);
</script>