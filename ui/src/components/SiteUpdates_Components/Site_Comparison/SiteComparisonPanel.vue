<template>
  <div class="border border-gray-300 rounded-lg overflow-hidden bg-white">
    <!-- Elevation Navigation -->
    <div class="flex items-center justify-center p-4 gap-4">
      <button @click="$emit('prev-elevation')" class="p-1 hover:bg-gray-100 rounded-full">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m15 18-6-6 6-6" />
        </svg>
      </button>

      <h3 class="text-lg font-medium text-gray-700">{{ elevation }}</h3>

      <button @click="$emit('next-elevation')" class="p-1 hover:bg-gray-100 rounded-full">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="m9 18 6-6-6-6" />
        </svg>
      </button>
    </div>

    <!-- Date Selector -->
    <div class="flex justify-center mb-4 ml-4">
      <DatePicker v-model="activeDatePickerValue" variant="subtle" placeholder="Select date" :disabled="false"
        label="Label" />
    </div>

    <!-- Site Image -->
    <div class="relative">
      <img :src="image" alt="Site Update" class="w-full h-60 object-cover cursor-pointer" @click="$emit('view-image')">
    </div>

    <!-- Work Updates Section -->
    <div class="p-4">
      <div class="bg-[#e6e1f9] text-center py-2 rounded-md mb-4">
        <h4 class="text-gray-700 font-medium">Work Updates</h4>
      </div>

      <!-- Civil Section -->
      <div class="mb-4">
        <div class="bg-gray-100 py-2 px-4 rounded-md mb-2">
          <h5 class="text-gray-700">Civil</h5>
        </div>
        <div class="px-4 py-2 text-sm text-gray-600 border rounded-md">
          {{ workUpdates.civil }}
        </div>
      </div>

      <!-- Decor Section -->
      <div class="mb-4">
        <div class="bg-gray-100 py-2 px-4 rounded-md mb-2">
          <h5 class="text-gray-700">Decor</h5>
        </div>
        <div class="px-4 py-2 text-sm text-gray-600 border rounded-md">
          <span v-if="workUpdates.decor">{{ workUpdates.decor }}</span>
          <span v-else class="text-gray-400">Enter Updates</span>
        </div>
      </div>

      <!-- Electrical Section -->
      <div>
        <div class="bg-gray-100 py-2 px-4 rounded-md mb-2">
          <h5 class="text-gray-700">Electrical</h5>
        </div>
        <div class="px-4 py-2 text-sm text-gray-600 border rounded-md">
          <span v-if="workUpdates.electrical">{{ workUpdates.electrical }}</span>
          <span v-else class="text-gray-400">Enter Updates</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { defineProps, defineEmits } from 'vue';
import { Autocomplete, DatePicker } from 'frappe-ui';

const props = defineProps({
  elevation: {
    type: String,
    required: true
  },
  date: {
    type: String,
    required: true
  },
  image: {
    type: String,
    required: true
  },
  workUpdates: {
    type: Object,
    required: true
  }
});
const activeDatePickerValue = ref(null);

defineEmits(['prev-elevation', 'next-elevation', 'view-image']);
</script>