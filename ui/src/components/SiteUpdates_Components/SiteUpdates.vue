<template>
  <div class="w-full">
    <!-- Navigation Tabs -->
    <div class="border-b border-gray-300">
      <div class="flex gap-[22px]">
        <button 
          v-for="tab in tabs" 
          :key="tab" 
          @click="activeTab = tab"
          :class="['pb-[2px] pl-1 text-[#4d4d4d] border-b border-transparent relative text-[14px]', 
                  activeTab === tab ? 'font-semibold border-b-2' : '']"
        >
          {{ tab }}
        </button>
      </div>
    </div>

    <!-- Compare Tab Content -->
    <div v-if="activeTab === 'Compare'">
      <!-- Filters -->
      <div class="flex space-x-4 my-4">
        <div class="relative">
          <Autocomplete
            v-model="selectedPerson"
            :options="peopleOptions"
            label-key="name"
            value-key="id"
            placeholder="People"
            class="w-32"
          >
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><circle cx="12" cy="8" r="5"/><path d="M20 21a8 8 0 0 0-16 0"/></svg>
            </template>
          </Autocomplete>
        </div>

        <div class="relative">
          <Autocomplete
            v-model="selectedArea"
            :options="areaOptions"
            label-key="name"
            value-key="id"
            placeholder="Area"
            class="w-32"
          >
            <template #prefix>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><line x1="3" x2="21" y1="9" y2="9"/><line x1="9" x2="9" y1="21" y2="9"/></svg>
            </template>
          </Autocomplete>
        </div>
      </div>

      <!-- Comparison Section -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <SiteComparisonPanel 
          :elevation="leftElevation"
          :date="leftDate"
          :image="getImageForElevationAndDate(leftElevation, leftDate)"
          :work-updates="getWorkUpdatesForElevationAndDate(leftElevation, leftDate)"
          @prev-elevation="navigateElevation('left', 'prev')"
          @next-elevation="navigateElevation('left', 'next')"
          @view-image="openImagePreview(getImageForElevationAndDate(leftElevation, leftDate))"
        />
        
        <SiteComparisonPanel 
          :elevation="rightElevation"
          :date="rightDate"
          :image="getImageForElevationAndDate(rightElevation, rightDate)"
          :work-updates="getWorkUpdatesForElevationAndDate(rightElevation, rightDate)"
          @prev-elevation="navigateElevation('right', 'prev')"
          @next-elevation="navigateElevation('right', 'next')"
          @view-image="openImagePreview(getImageForElevationAndDate(rightElevation, rightDate))"
        />
      </div>

      <!-- Image Preview Modal -->
      <ImagePreviewModal 
        v-if="showImagePreview" 
        :image="previewImage" 
        @close="showImagePreview = false" 
      />

      <!-- Date Picker Modal -->
      <div v-if="showDatePickerModal" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50" @click="showDatePickerModal = false">
        <div class="bg-white p-4 rounded-lg shadow-lg" @click.stop>
          <DatePicker 
            v-model="activeDatePickerValue" 
            @change="handleDateChange"
          />
        </div>
      </div>
    </div>

    <!-- List Tab Content -->
    <div v-else-if="activeTab === 'List'">
      <div class="p-4 text-center text-gray-500">
         <SiteUpdatesList />
      </div>
    </div>

    <!-- Report Tab Content -->
    <div v-else-if="activeTab === 'Report'">
      <div class="p-4 text-center text-gray-500">
        Report view content will go here
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { Autocomplete } from 'frappe-ui';
import SiteComparisonPanel from './Site_Comparison/SiteComparisonPanel.vue';
import ImagePreviewModal from './Site_Comparison/ImagePreviewModal.vue';
import SiteUpdatesList from './List_View/SiteUpdatesList.vue';

// Tab navigation
const activeTab = ref('Compare');
const tabs = ['Compare', 'List', 'Report'];

// Filters state
const selectedPerson = ref(null);
const selectedArea = ref(null);

// Comparison state
const leftElevation = ref('Elevation A');
const rightElevation = ref('Elevation A');
const leftDate = ref('07/17/2023');
const rightDate = ref('08/17/2023');
const showImagePreview = ref(false);
const previewImage = ref('');


// Mock data
const peopleOptions = [
  { id: 1, name: 'John Doe' },
  { id: 2, name: 'Jane Smith' },
  { id: 3, name: 'Robert Johnson' }
];

const areaOptions = [
  { id: 1, name: 'Area 1' },
  { id: 2, name: 'Area 2' },
  { id: 3, name: 'Area 3' }
];

const elevations = [
  'Elevation A',
  'Elevation B',
  'Elevation C',
  'Elevation D'
];

const availableDates = {
  'Elevation A': ['07/17/2023', '08/17/2023', '09/17/2023'],
  'Elevation B': ['07/17/2023', '08/17/2023', '09/17/2023'],
  'Elevation C': ['07/17/2023', '08/17/2023', '09/17/2023'],
  'Elevation D': ['07/17/2023', '08/17/2023', '09/17/2023']
};

const siteImages = {
  'Elevation A': {
    '07/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769762-b6291545d818?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '08/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769677-470c0777ac71?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '09/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769888-ef3698e72a6c?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
  },
  'Elevation B': {
    '07/17/2023': 'https://plus.unsplash.com/premium_photo-1678286770016-6306ad61df9b?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '08/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769514-e60a57a33b73?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '09/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769762-b6291545d818?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
  },
  'Elevation C': {
    '07/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769677-470c0777ac71?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '08/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769888-ef3698e72a6c?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '09/17/2023': 'https://plus.unsplash.com/premium_photo-1678286770016-6306ad61df9b?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
  },
  'Elevation D': {
    '07/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769514-e60a57a33b73?q=80&w=1974&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '08/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769762-b6291545d818?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',
    '09/17/2023': 'https://plus.unsplash.com/premium_photo-1678286769677-470c0777ac71?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D'
  }
};

// Mock work updates data
const workUpdates = {
  'Elevation A': {
    '07/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.',
      decor: '',
      electrical: ''
    },
    '08/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry\'s standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.',
      decor: '',
      electrical: ''
    },
    '09/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    }
  },
  'Elevation B': {
    '07/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    },
    '08/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    },
    '09/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    }
  },
  'Elevation C': {
    '07/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    },
    '08/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    },
    '09/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    }
  },
  'Elevation D': {
    '07/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    },
    '08/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    },
    '09/17/2023': {
      civil: 'Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
      decor: '',
      electrical: ''
    }
  }
};

// Methods
const navigateElevation = (side, direction) => {
  const currentIndex = elevations.indexOf(side === 'left' ? leftElevation.value : rightElevation.value);
  let newIndex;
  
  if (direction === 'prev') {
    newIndex = currentIndex > 0 ? currentIndex - 1 : elevations.length - 1;
  } else {
    newIndex = currentIndex < elevations.length - 1 ? currentIndex + 1 : 0;
  }
  
  const newElevation = elevations[newIndex];
  
  if (side === 'left') {
    leftElevation.value = newElevation;
    // Set to first available date for this elevation
    leftDate.value = availableDates[newElevation][0];
  } else {
    rightElevation.value = newElevation;
    // Set to first available date for this elevation
    rightDate.value = availableDates[newElevation][0];
  }
};

const getImageForElevationAndDate = (elevation, date) => {
  return siteImages[elevation]?.[date] || '/placeholder-construction.jpg';
};

const getWorkUpdatesForElevationAndDate = (elevation, date) => {
  return workUpdates[elevation]?.[date] || {
    civil: '',
    decor: '',
    electrical: ''
  };
};

const openImagePreview = (image) => {
  previewImage.value = image;
  showImagePreview.value = true;
};

</script>

<style scoped>
.tab {
  padding-bottom: 5px;
  padding-left: 4px;
  color: #4d4d4d;
  font-family: Inter;
  font-weight: 500;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 0.75px;
  background-color: #747474;
  transition: width 0.3s ease, left 0.3s ease;
}

.tab:hover::after {
  width: 100%;
  left: 0;
}

.tab.active {
  color: #4d4d4d;
  font-weight: 600;
  border-bottom: 1px solid #747474;
}
</style>