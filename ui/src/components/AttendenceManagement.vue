<template>
  <div class="w-full">
    <!-- Attendance header with icon and tabs -->
    <div class="flex gap-5 border-b border-gray-300 mt-3 px-4 items-center">
      <div class="flex gap-2 items-center pb-1">
        <AttendenceIcon />
        <h1 class="text-[#434343] text-[17px] font-normal font-roboto leading-none">Attendance</h1>
      </div>
      <button
        v-for="tab in attendanceTabs"
        :key="tab"
        @click="activeAttendanceTab = tab"
        class="pb-1 px-1 text-[#79747E] text-[14px] font-[600] relative transition-all"
        :class="[
          activeAttendanceTab === tab
            ? 'font-medium border-b border-gray-600'
            : 'hover:text-gray-900'
        ]"
      >
        {{ tab }}
      </button>
    </div>

    <!-- Tab Content for Attendance Management -->
    <div class="pt-2 w-full">
      <Transition name="slide" mode="out-in">
        <!-- User View -->
        <div v-if="activeAttendanceTab === 'User'" key="user-attendance">
          <Attendance_Userview />
        </div>

        <!-- Team/Manager View -->
        <div v-else-if="activeAttendanceTab === 'Team'" key="team-attendance">
          <Attendance_Managerview />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import AttendenceIcon from "./icons/AttendenceIcon.vue";
import Attendance_Userview from './Attendence_Components/Attendance_Userview.vue';
import Attendance_Managerview from './Attendence_Components/Attendance_Managerview.vue';
const is_manager = window.is_manager

// Main tabs for Attendance Management
// const attendanceTabs = ['User', 'Team'];
const attendanceTabs = is_manager ? ['User', 'Team'] : ['User'];
const activeAttendanceTab = ref(localStorage.getItem('activeAttendanceTab') || 'User');

// Save active tab to localStorage
watch(activeAttendanceTab, (newVal) => {
  localStorage.setItem('activeAttendanceTab', newVal);
});
</script>

<style scoped>
.slide-enter-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-100px);
}
</style>
