<template>
  <div class="relative week-range-picker">
    <div class="flex items-center space-x-2">
      <button @click="() => slideWeek('prev')" class="p-1 hover:bg-gray-100 rounded-full">
        <ChevronLeft class="w-6 h-6 text-gray-600" />
      </button>

      <button @click="toggleCalendar"
        class="flex items-center justify-between w-full px-8 py-2 text-left bg-white border rounded-lg shadow-sm hover:bg-gray-50">
        <span class="text-gray-700 w-36 text-center">
          {{ selectedWeekRange?.formatted || 'Select date range' }}
        </span>
        <ChevronDown class="w-4 h-4 text-gray-600 ml-2"/>
      </button>

      <button @click="() => slideWeek('next')" class="p-1 hover:bg-gray-100 rounded-full">
        <ChevronRight  class="w-6 h-6 text-gray-600" />
      </button>
    </div>

    <!-- Calendar Dropdown -->
    <div v-if="isOpen" class="absolute z-10 w-72 mt-1 bg-white border rounded-lg shadow-lg">
      <!-- Calendar Header -->
      <div class="flex items-center justify-between p-3 border-b">
        <button @click="prevMonth" class="p-1 hover:bg-gray-100 rounded-full">
          <!-- Chevron Left Icon -->
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>
        <span class="font-semibold">
          {{ formatMonthYear(currentDate) }}
        </span>
        <button @click="nextMonth" class="p-1 hover:bg-gray-100 rounded-full">
          <!-- Chevron Right Icon -->
          <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
            stroke-linecap="round" stroke-linejoin="round">
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </button>
      </div>

      <!-- Calendar Grid -->
      <div class="p-3">
        <!-- Week day headers -->
        <div class="grid grid-cols-7 mb-2">
          <span v-for="day in ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su']" :key="day"
            class="text-center text-sm text-gray-500">
            {{ day }}
          </span>
        </div>

        <!-- Calendar days -->
        <div class="grid grid-cols-7 gap-1">
          <template v-for="week in calendarDays" :key="week[0]">
            <template v-for="date in week" :key="date">
              <button v-if="date" @click="selectDate(date)" :class="[
                'w-full aspect-square flex items-center justify-center text-sm rounded-full hover:bg-gray-100',
                isDateInSelectedWeek(date) ? 'bg-purple-400 text-white hover:bg-purple-500' : ''
              ]">
                {{ date.getDate() }}
              </button>
              <div v-else class="w-full aspect-square"></div>
            </template>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch , onMounted, onUnmounted} from 'vue'
import { ChevronLeft,ChevronRight,ChevronDown} from 'lucide-vue-next'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ start: null, end: null })
  }
})

const emit = defineEmits(['update:modelValue'])

const currentDate = ref(new Date())
const selectedDate = ref(null)
const isOpen = ref(false)

const formatDate = (date) => {
  if (!date) return ''
  const options = { month: 'short', day: 'numeric' }
  return date.toLocaleDateString('en-US', options)
}

// const getWeekDates = (date) => {
//   const curr = new Date(date);
//   const day = curr.getDay();
//   const diff = curr.getDate() - day + (day === 0 ? -6 : 1);

//   const start = new Date(curr);
//   start.setDate(diff);
//   const end = new Date(start);
//   end.setDate(start.getDate() + 6);

//   return { start, end };
// };

const getWeekDates = (date) => {
  const curr = new Date(date)
  const day = curr.getDay() // 0 (Sunday) to 6 (Saturday)
  // Calculate Monday as start of week
  const diff = curr.getDate() - day + (day === 0 ? -6 : 1)
  
  const start = new Date(curr)
  start.setDate(diff)
  start.setHours(0, 0, 0, 0) // Normalize time
  
  const end = new Date(start)
  end.setDate(start.getDate() + 6)
  end.setHours(23, 59, 59, 999) // End of Sunday
  
  return { start, end }
}


const selectedWeekRange = computed(() => {
  if (!selectedDate.value) return null
  const { start, end } = getWeekDates(selectedDate.value)
  return {
    start,
    end,
    formatted: `${formatDate(start)} - ${formatDate(end)}`
  }
})

watch(selectedWeekRange, (newValue) => {
  if (newValue) {
    emit('update:modelValue', { start: newValue.start, end: newValue.end })
  }
})

const getDaysInMonth = (year, month) => {
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const days = []

  const firstDayOfWeek = firstDay.getDay() || 7 
  for (let i = 1; i < firstDayOfWeek; i++) {
    days.push(null)
  }
  for (let d = 1; d <= lastDay.getDate(); d++) {
    days.push(new Date(year, month, d))
  }
  while (days.length % 7 !== 0) {
    days.push(null)
  }

  return days
}

const calendarDays = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const days = getDaysInMonth(year, month)

  const weeks = []
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7))
  }
  return weeks
})

// const isDateInSelectedWeek = (date) => {
//   if (!selectedDate.value || !date) return false
//   const { start, end } = getWeekDates(selectedDate.value)
//   return date >= start && date <= end
// }

const isDateInSelectedWeek = (date) => {
  if (!selectedDate.value || !date) return false
  const { start, end } = getWeekDates(selectedDate.value)
  
  // Normalize dates to compare just the date parts (ignore time)
  const normalizedDate = new Date(date.setHours(0, 0, 0, 0))
  const normalizedStart = new Date(start.setHours(0, 0, 0, 0))
  const normalizedEnd = new Date(end.setHours(0, 0, 0, 0))
  
  return normalizedDate >= normalizedStart && normalizedDate <= normalizedEnd
}

const nextMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() + 1,
    1
  )
}

const prevMonth = () => {
  currentDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth() - 1,
    1
  )
}

const selectDate = (date) => {
  if (!date) return
  selectedDate.value = date
  isOpen.value = false
}

const toggleCalendar = () => {
  isOpen.value = !isOpen.value
}

const formatMonthYear = (date) => {
  return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
}

const slideWeek = (direction) => {
  if (!selectedDate.value) return
  const newDate = new Date(selectedDate.value)
  newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
  selectDate(newDate)
}

const closeCalendar = (event) => {
  if (isOpen.value && !event.target.closest('.week-range-picker')) {
    isOpen.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', closeCalendar)
})

onUnmounted(() => {
  document.removeEventListener('click', closeCalendar)
})

watch(() => props.modelValue, (newValue) => {
  if (!newValue.start || !newValue.end) return
  const newStart = new Date(newValue.start).toDateString()
  const currentStart = selectedDate.value?.toDateString() || ''
  
  if (newStart !== currentStart) {
    selectedDate.value = new Date(newValue.start)
    currentDate.value = new Date(newValue.start)
  }
}, { immediate: true })
</script>