<template>
  <div class="mt-6 p-4">
    <div class="flex items-center gap-2 mb-4">
      <h2 class="text-lg font-medium">Pending Leave Requests</h2>
      <div class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
        {{ pendingCount }}
      </div>
    </div>

    <div class="border rounded-md overflow-hidden">
      <!-- Table header -->
      <div class="grid grid-cols-6 bg-gray-50 p-3 border-b">
        <div class="text-sm font-medium text-gray-600">UPCOMING LEAVE</div>
        <div class="text-sm font-medium text-gray-600">USER</div>
        <div class="text-sm font-medium text-gray-600">LEAVE TYPE</div>
        <div class="text-sm font-medium text-gray-600">REQUESTED ON</div>
        <div class="text-sm font-medium text-gray-600">LEAVE NOTE</div>
        <div class="text-sm font-medium text-gray-600">STATUS</div>
      </div>

      <!-- Table rows -->
      <div v-if="!requests.length" class="flex justify-center items-center p-3 border-b">
        <p class="text-center text-gray-500 text-sm">No pending requests available </p>
      </div>
      <div v-for="(request, index) in requests" :key="index" class="grid grid-cols-6 p-3 border-b items-center">
        <div class="text-sm">{{ request.upcomingLeave }}</div>
        <div class="text-sm">
          {{ request.user.name }}
          <div class="text-xs text-gray-500">EID : {{ request.user.id }}</div>
        </div>
        <div class="text-sm">{{ request.leaveType }}</div>
        <div class="text-sm">{{ request.requestedOn }}</div>
        <div class="text-sm">{{ request.leaveNote }}</div>
        <div class="flex items-center gap-2">
          <div class="px-3 py-1 rounded-md text-sm" :class="{
            'bg-red-100 text-red-600': request.status === 'Pending',
            'bg-green-100 text-green-600': request.status === 'Approved',
            'bg-gray-100 text-gray-600': request.status === 'Rejected'
          }">
            {{ request.status }}
          </div>
          <button v-if="request.status === 'Pending'" @click="approveRequest(request)" class="text-green-500 p-1">
            <Check class="w-5 h-5" />
          </button>
          <button v-if="request.status === 'Pending'" @click="rejectRequest(request)" class="text-red-500 p-1">
            <X class="w-5 h-5" />
          </button>
          <button @click="viewDetails(request)" class="text-gray-500 p-1">
            <fillViewIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { Check, X, Maximize2 } from 'lucide-vue-next';
import fillViewIcon from '../../icons/fillViewIcon.vue'

const props = defineProps({
  requests: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['approveRequest', 'rejectRequest', 'viewDetails']);

const pendingCount = computed(() => {
  return props.requests.filter(req => req.status === 'Pending').length;
});

const approveRequest = (request) => {
  emit('approveRequest', request);
};

const rejectRequest = (request) => {
  emit('rejectRequest', request);
};

const viewDetails = (request) => {
  emit('viewDetails', request);
};
</script>