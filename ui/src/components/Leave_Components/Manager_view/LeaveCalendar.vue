<template>
  <div class="border rounded-md overflow-hidden">
    <!-- Calendar header -->
    <div class="grid grid-cols-7 text-center border-b">
      <div v-for="day in weekDays" :key="day" class="py-2 text-sm font-medium text-gray-600">
        {{ day }}
      </div>
    </div>

    <!-- Calendar grid -->
    <div class="grid grid-cols-7">
      <div v-for="(day, index) in calendarDays" :key="index" class="min-h-[100px] border-r border-b p-2 relative"
        :class="day.isCurrentMonth ? '' : 'bg-gray-50'">

        <!-- Day number -->
        <div class="text-sm text-gray-700">{{ day.dayNumber }}</div>

        <!-- Holiday indicator -->
        <div v-if="day.isHoliday" class="text-xs text-blue-500 mt-1">
          {{ day.holidayName }}
        </div>

        <!-- Leave indicator -->
        <div v-if="selectedEmployee" v-for="(leave, leaveIndex) in day.leaves" :key="leaveIndex"
          class="mt-2 rounded absolute bottom-4"
          :class="getLeaveColorClass(leave.leaveType, leave.status)">
          <div class="truncate text-[10px]">{{ leave.employeeName }}</div>
          <div class="truncate text-[10px]">{{ leave.leaveType }}</div>
        </div>
        <div v-else>
          <div v-if="day.leaves.length > 0"
            class="absolute bottom-4 text-xs bg-purple-100 text-gray-900 rounded-full w-5 h-5 flex items-center justify-center cursor-pointer hover:bg-gray-200"
            :title="getTooltipContent(day.leaves)">
            {{ day.leaves.length }}
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  currentMonth: {
    type: String,
    required: true
  },
  currentYear: {
    type: Number,
    required: true
  },
  calendarData: {
    type: Array,
    required: true
  },
  selectedEmployee: {
    type: String,
    default: ''
  }
});

// Week days
const weekDays = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];

// Get month index
const monthIndex = computed(() => {
  return ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'].indexOf(props.currentMonth);
});

// Generate calendar days including previous and next month days to fill the grid
const calendarDays = computed(() => {
  const year = props.currentYear;
  const month = monthIndex.value;

  // First day of the month
  const firstDay = new Date(year, month, 1);
  // Last day of the month
  const lastDay = new Date(year, month + 1, 0);

  // Day of the week for the first day (0 = Sunday, 6 = Saturday)
  const firstDayOfWeek = firstDay.getDay();
  // Total days in the month
  const daysInMonth = lastDay.getDate();

  // Previous month's last day
  const prevMonthLastDay = new Date(year, month, 0).getDate();

  const days = [];

  // Add previous month's days
  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    days.push({
      dayNumber: prevMonthLastDay - i,
      isCurrentMonth: false,
      isHoliday: false,
      leaves: []
    });
  }

  // Add current month's days
  for (let i = 1; i <= daysInMonth; i++) {
    // Check if day is a holiday
    const holidayData = props.calendarData.find(item => item.day === i && item.isHoliday);

    // Get all leaves for this day from upcomingLeaves
    let leavesForDay = [];

    // First check from calendarData (for existing entries)
    const calendarLeaves = props.calendarData
      .filter(item => {
        // For single day leaves
        if (item.day === i && item.employeeName) {
          return true;
        }

        // For multi-day leaves
        if (item.startDay && item.endDay) {
          return i >= item.startDay && i <= item.endDay;
        }

        return false;
      })
      .map(item => ({
        employeeName: item.employeeName,
        leaveType: item.leaveType,
        status: item.status || 'Approved'
      }));

    leavesForDay = [...calendarLeaves];

    // Apply employee filter if selected
    if (props.selectedEmployee) {
      leavesForDay = leavesForDay.filter(leave =>
        leave.employeeName === props.selectedEmployee
      );
    }

    days.push({
      dayNumber: i,
      isCurrentMonth: true,
      isHoliday: !!holidayData,
      holidayName: holidayData ? holidayData.holidayName : '',
      leaves: leavesForDay
    });
  }

  // Add next month's days to fill the grid (6 rows x 7 days = 42 cells)
  const remainingDays = 42 - days.length;
  for (let i = 1; i <= remainingDays; i++) {
    days.push({
      dayNumber: i,
      isCurrentMonth: false,
      isHoliday: false,
      leaves: []
    });
  }

  return days;
});

// Get color class based on leave type and status
const getLeaveColorClass = (leaveType, status) => {
  if (status === 'Pending') return 'bg-yellow-100 text-yellow-800';

  switch (leaveType) {
    case 'Annual Leave':
    case 'Paid Leave':
      return 'bg-orange-100 text-orange-800';
    case 'Sick Leave':
      return 'bg-red-100 text-red-800';
    case 'Casual Leave':
      return 'bg-blue-100 text-blue-800';
    case 'Unpaid Leave':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-purple-100 text-purple-800 w-fit text-xl';
  }
};

const getTooltipContent = (leaves) => {
  return leaves.map(leave =>
    `${leave.employeeName} - ${leave.leaveType}${leave.status === 'Pending' ? ' (Pending)' : ''}`
  ).join('\n');
};
</script>