<template>
  <div class="border rounded-md p-4 h-full overflow-y-auto">
    <div class="space-y-4">
      <div v-for="(leave, index) in leaves" :key="index" 
           class="flex items-center gap-3 pb-3 border-b last:border-0 cursor-pointer hover:bg-gray-50 p-2 rounded"
           @click="handleLeaveClick(leave)">
        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600">
          <User class="w-4 h-4" />
        </div>
        <div>
          <div class="text-blue-500 font-medium">{{ leave.date }}</div>
          <div class="text-sm text-gray-700">{{ leave.employeeName }} - {{ leave.leaveType }}</div>
        </div>
      </div>
    </div>
  </div>

  <Dialog v-model="showCancelDialog" :options="{
    title: 'Cancel Leave',
    message: `Are you sure you want to cancel ${selectedLeave?.employeeName}'s ${selectedLeave?.leaveType} leave on ${selectedLeave?.date}?`,
    icon: {
      name: 'alert-triangle',
      appearance: 'warning',
    },
    actions: [
      {
        label: 'Cancel Leave',
        theme: 'red',
        variant: 'solid',
        onClick: () => {
          cancelLeave();
        },
      },
      {
        label: 'Close',
        theme: 'gray',
        variant: 'subtle',
      },
    ],
  }" />
</template>

<script setup>
import { User } from 'lucide-vue-next';
import { Dialog,createResource ,toast} from 'frappe-ui'
import { ref } from 'vue'

const showCancelDialog = ref(false)
const selectedLeave = ref(null)

const props = defineProps({
  leaves: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['cancelLeave'])

const handleLeaveClick = (leave) => {
  selectedLeave.value = leave
  showCancelDialog.value = true
}

const cancelLeave = () => {
  // console.log('CancelLeave triggered',selectedLeave.value.id)
  
  // showCancelDialog.value = false
  const delete_request = createResource({
    url: "frappe.client.set_value",
    makeParams: () => ({
      doctype: "Leave Application",
      name: selectedLeave.value.id,
      fieldname: {
        status:"Cancelled",
        docstatus: 2
        
      }
    }),
    auto: true,
    onSuccess: (resp) => {
      // console.log("Document deleted sucessfully")
      emit('cancelLeave', selectedLeave.value)
      toast({
            title: 'Success',
            text: `leave cancelled sucessfully`,
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
      // alert("Cancelled leave request")
    },
    onError: (error) => {
      // console.log("Error while deleting doc", error)
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to cancel request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    }
  })
  showCancelDialog.value = false
}
</script>