<template>
  <div class="p-4">
    <div class="flex items-center gap-2 mb-4">
      <h2 class="text-lg font-medium">Pending Leave Requests</h2>
      <div class="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
        {{ pendingCount }}
      </div>
    </div>
    <!-- Pending requests table -->
    <div class="border rounded-md overflow-hidden">
      <table class="w-full text-sm">
        <thead class="bg-gray-50">
          <tr>
            <th class="text-left p-4 font-medium">UPCOMING LEAVE</th>
            <th class="text-left p-4 font-medium">LEAVE TYPE</th>
            <th class="text-left p-4 font-medium">REQUESTED ON</th>
            <th class="text-left p-4 font-medium">LEAVE NOTE</th>
            <th class="text-left p-4 font-medium">STATUS</th>
            <th class="text-left p-4 font-medium">ACTIONS</th>
          </tr>
        </thead>
        <tbody>
          <tr v-if="!requests.length">
            <td colspan="6" class="p-4 text-center text-gray-500">No pending requests available</td>
          </tr>
          <tr v-for="(request, index) in requests" :key="index" class="border-t">
            <td class="p-4">{{ request.period }}</td>
            <td class="p-4">{{ request.type }}</td>
            <td class="p-4">{{ request.requestedOn }}</td>
            <td class="p-4">{{ request.note }}</td>
            <td class="p-4">
              <span class="bg-red-100 text-red-600 px-3 py-1 rounded-full text-xs">
                {{ request.status }}
              </span>
            </td>
            <td class="p-4 flex gap-2">
              <button class="w-8 h-8 rounded-full border flex items-center justify-center"
                @click="openLeaveDetails(request)">
                <fillViewIcon class="w-4 h-4" />
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Leave details popup -->
    <div v-if="showLeaveDetails" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg w-full max-w-xl p-6 max-h-[90vh] text-sm overflow-y-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Leave Request Details</h2>
          <button @click="showLeaveDetails = false" class="text-gray-500 hover:text-gray-700">
            <X class="w-5 h-5" />
          </button>
        </div>

        <!-- Content -->
        <div class="space-y-8" v-if="selectedLeaveDetails">
          <!-- Leave details -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Leave Period</div>
              <div class="font-medium">{{ selectedLeaveDetails.period }}</div>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Leave Type</div>
              <div class="font-medium">{{ selectedLeaveDetails.type }}</div>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Requested On</div>
              <div class="font-medium">{{ selectedLeaveDetails.requestedOn }}</div>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Status</div>
              <div class="font-medium">
                <span class="px-2 py-1 rounded-md text-sm" :class="{
                  'bg-red-100 text-red-600': selectedLeaveDetails.status === 'Pending',
                  'bg-green-100 text-green-600': selectedLeaveDetails.status === 'Approved',
                  'bg-gray-100 text-gray-600': selectedLeaveDetails.status === 'Rejected'
                }">
                  {{ selectedLeaveDetails.status }}
                </span>
              </div>
            </div>
          </div>

          <!-- Leave note -->
          <div>
            <div class="text-sm text-gray-500 mb-1">Leave Note</div>
            <div class="p-3 bg-gray-50 rounded-md">
              {{ selectedLeaveDetails.note }}
            </div>
          </div>

          <!-- Leave balance -->
          <!-- <div>
            <div class="text-sm text-gray-500 mb-1">Leave Balance</div>
            <div class="grid grid-cols-4 gap-2">
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Sick Leave</div>
                <div class="font-medium">6 days</div>
              </div>
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Parental Leave</div>
                <div class="font-medium">28 days</div>
              </div>
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Paid Leave</div>
                <div class="font-medium">12 days</div>
              </div>
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Bereavement Leave</div>
                <div class="font-medium">4 days</div>
              </div>
            </div>
          </div> -->
          <div>
            <div class="text-sm text-gray-500 mb-1">Leave Balance</div>
            <div class="grid grid-cols-4 gap-2">
              <div v-for="(balance, index) in props.balances" :key="index" class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">{{ balance.type }}</div>
                <div class="font-medium">{{ balance.count }} days</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-3 mt-6">
          <button @click="cancelLeaveRequest(selectedLeaveDetails)"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            v-if="selectedLeaveDetails.status == 'Pending'">
            Cancel Leave
          </button>

          <button @click="showLeaveDetails = false"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { CheckIcon, XIcon, X } from 'lucide-vue-next';
import fillViewIcon from '../../icons/fillViewIcon.vue'
import { createResource,toast } from 'frappe-ui';

// Component props
const props = defineProps({
  requests: {
    type: Array,
    required: true
  },
  balances: {
    type: Array,
    required: true
  }
});
console.log(props.balances)
// const pendingCount = computed(() => {
//   return props.requests.length;
// });
const pendingCount = computed(() => {
  return props.requests.filter(req => req.status === 'Pending').length;
});
// Leave details popup
const showLeaveDetails = ref(false);
const selectedLeaveDetails = ref(null);

const openLeaveDetails = (request) => {
  selectedLeaveDetails.value = request;
  showLeaveDetails.value = true;
};
const cancelLeaveRequest = (request) => {
  // console.log('Requesting cancellation for:', request);
  request.status = 'Cancellation Requested';
  const delete_request = createResource({
    url: "frappe.client.delete",
    makeParams: () => ({
      doctype: "Leave Application",
      name: request.id
    }),
    auto: true,
    onSuccess: (resp) => {
      // console.log("Document deleted sucessfully")
      toast({
            title: 'Success',
            text: 'Document cancelled sucessfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
      showLeaveDetails.value = false;
      // requests.value = requests.value.filter(r => r.id !== request.id);
    },
    onError: (error) => {
      // console.log("Error while deleting doc", error)
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to cancel request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    }
  })

};

</script>