<template>
  <div class="w-full">
    <h2 class="text-base font-medium mb-4">Leave History</h2>
    <div class="border rounded">
      <!-- History tabs -->
      <div class="border-b flex mb-1">
        <button v-for="tab in historyTabs" :key="tab" class="px-4 py-2 font-medium"
          :class="{ 'border-b-2 border-primary text-primary': activeHistoryTab === tab }"
          @click="$emit('updateHistoryTab', tab)">
          {{ tab }}
        </button>
      </div>

      <!-- History content -->
      <div class="w-full h-64 overflow-auto p-2">
        <div v-if="activeHistoryTab === 'Upcoming'" class="space-y-2">
          <!-- Show leave cards if there is data -->
          <div v-if="upcomingLeaves.length > 0">
            <div v-for="(leave, index) in upcomingLeaves" :key="index"
              class="p-4 py-1 border-b rounded-md flex items-start gap-4" @click="handleLeaveClick(leave)">
              <!-- Leave type icon -->
              <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-500">
                <component :is="getIconComponent(leave.icon)" class="w-5 h-5" />
              </div>

              <!-- Leave details -->
              <div>
                <div class="font-medium text-blue-500">{{ leave.date }}</div>
                <div class="text-sm text-gray-600">{{ leave.type }}</div>
                <div class="text-xs text-gray-500">{{ leave.duration }}</div>
              </div>
            </div>
          </div>
          <!-- Show "No data" message if no leaves -->
          <div v-else class="text-gray-500 text-sm text-center mt-8">
            No data available
          </div>
        </div>

        <div v-if="activeHistoryTab === 'Past'" class="space-y-2">
          <div v-for="(leave, index) in PastLeaves" :key="index"
            class="p-4 py-1 border-b rounded-md flex items-start gap-4">
            <!-- Leave type icon -->
            <div class="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-purple-500">
              <component :is="getIconComponent(leave.icon)" class="w-5 h-5" />
            </div>

            <!-- Leave details -->
            <div>
              <div class="font-medium text-blue-500">{{ leave.date }}</div>
              <div class="text-sm text-gray-600">{{ leave.type }}</div>
              <div class="text-xs text-gray-500">{{ leave.duration }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <Dialog v-model="showCancelDialog" :options="{
    title: 'Cancel Leave',
    message: `Are you sure you want to cancel your ${selectedLeave?.type} leave on ${selectedLeave?.date}?`,
    icon: {
      name: 'alert-triangle',
      appearance: 'warning',
    },
    actions: [
      {
        label: 'Cancel Leave',
        theme: 'red',
        variant: 'solid',
        onClick: () => {
          cancelLeave();
        },
      },
      {
        label: 'Close',
        theme: 'gray',
        variant: 'subtle',
      },
    ],
  }" />

</template>

<script setup>
import { Dialog, createResource } from 'frappe-ui'
import { ref } from 'vue'
import {
  RefreshCwIcon,
  PlaneTakeoffIcon,
} from 'lucide-vue-next';
import SickLeave from '../../icons/SickLeave.vue'
import VacationIcons from '../../icons/VacationIcons.vue'

const showCancelDialog = ref(false)
const selectedLeave = ref(null)


// Component props
const props = defineProps({
  historyTabs: {
    type: Array,
    required: true
  },
  activeHistoryTab: {
    type: String,
    required: true
  },
  upcomingLeaves: {
    type: Array,
    required: true
  },
  PastLeaves: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['updateHistoryTab', 'cancelLeave'])
const handleLeaveClick = (leave) => {
  selectedLeave.value = leave
  showCancelDialog.value = true
}

const cancelLeave = () => {
  // console.log('CancelLeave triggered')
  // emit('cancelLeave', selectedLeave.value)
  const delete_request = createResource({
    url: "frappe.client.set_value",
    makeParams: () => ({
      doctype: "Leave Application",
      name: selectedLeave.value.name,
      fieldname: {
        status: "Cancelled",
        docstatus: 2

      }
    }),
    auto: true,
    onSuccess: (resp) => {
      console.log("Document deleted sucessfully")
      emit('cancelLeave', selectedLeave.value)

      alert("Cancelled leave request")
    },
    onError: (error) => {
      console.log("Error while deleting doc", error)
    }
  })
  showCancelDialog.value = false
}

const getIconComponent = (iconName) => {
  const iconMap = {
    'RefreshCw': SickLeave,
    'PlaneTakeoff': VacationIcons,

  };

  return iconMap[iconName] || RefreshCwIcon;
};
</script>