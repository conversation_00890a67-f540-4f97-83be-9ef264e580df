<template>
  <div class="border rounded-md h-[35.5rem] overflow-auto">
    <!-- Form header with balance check link -->
    <div class="flex justify-between items-center mb-4 p-4 bg-[#DED8E1]">
      <h2 class="text-base font-medium text[#574E60]">Leave Request</h2>
      <!-- <button class="text-sm text-[#72667B]">Check Leave Balance</button> -->
      <button class=" px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
        @click="$emit('scrollToRequests')">
        My Requests
      </button>
    </div>

    <!-- Leave request form -->
    <div class="space-y-4 p-2">
      <!-- Date selection -->
      <div class="bg-white p-4 rounded-md border">
        <div class="grid grid-cols-3 gap-2 items-center">
          <div :class="optionalHoliday?'w-full pointer-events-none':''">
            <label class="text-sm text-gray-600 block mb-1">From</label>
            <DatePicker v-model="fromDate" variant="subtle" placeholder="Select date" :disabled="false" />
          </div>

          <div class="flex items-center justify-center mt-4">
            <div class="bg-gray-100 rounded px-2 py-1 text-sm">
              {{ leaveDuration }} days
            </div>
          </div>

          <div :class="optionalHoliday?'w-full pointer-events-none':''">
            <label class="text-sm text-gray-600 block mb-1">To</label>
            <DatePicker v-model="toDate" variant="subtle" placeholder="Select date" :disabled="false" />
          </div>
        </div>
      </div>

      <!-- Leave type selection -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Select the type of leave you want to apply</label>
        <div class="relative">
          <select v-model="selectedLeaveType" class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm">
            <option value="" disabled>Select</option>
            <option v-for="type in leaveTypes" :key="type" :value="type">{{ type }}</option>
          </select>
          <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <ChevronDownIcon class="w-4 h-4 text-gray-500" />
          </div>
        </div>
      </div>
      <div v-if="optionalHoliday">
        <label class="text-sm text-gray-600 block mb-1">Select the optional holiday</label>
        <div class="relative">
          <select v-model="selectedHoliday" class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm">
            <option value="" disabled>Select</option>
            <option 
              v-for="(holiday, index) in optionalHolidays" 
              :key="index" 
              :value="holiday.holiday_date">
              {{ holiday.description }}
            </option>
          </select>
          <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <ChevronDownIcon class="w-4 h-4 text-gray-500" />
          </div>
        </div>
      </div>

      <!-- Leave duration type -->
      <div class="flex gap-2">
        <div class="flex justify-end bg-[#EEEAF4] rounded-sm p-1">
          <button type="button" v-for="type in ['Full Day', 'Half Day']" :key="type"
            class="px-4 py-1 rounded-sm text-xs" :class="{ 'bg-white shadow-xl': durationType === type }"
            @click="durationType = type">
            {{ type }}
          </button>
        </div>
        <!-- Warning message -->
        <div v-if="leaveDuration > 0" class="flex items-center gap-1 text-xs text-red-500">
          <AlertCircleIcon class="w-4 h-4 mr-2" />
          You are requesting for {{ leaveDuration }} <br> days of leave
        </div>
      </div>

      <!-- Half day selection -->
      <div v-if="durationType === 'Half Day' && fromDate && toDate">
        <label class="text-sm text-gray-600 block mb-1">Half day On</label>
        <div class="relative">
          <select v-model="selectedHalfDay" class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm">
            <option value="" disabled>Select day</option>
            <option v-for="day in halfDayOptions" :key="day" :value="day">
              {{ formatDate(day) }}
            </option>
          </select>
          <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <ChevronDownIcon class="w-4 h-4 text-gray-500" />
          </div>
        </div>
      </div>

      <!-- Note/Reason field -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Note / Reason</label>
        <textarea v-model="leaveReason" class="w-full border rounded p-2 text-sm min-h-[100px]"
          placeholder="Type Here"></textarea>
      </div>

      <!-- Notify field with Autocomplete -->

      <div>
        <label class="text-sm text-gray-600 block mb-1">Notify</label>
        <Autocomplete v-model="notifyEmployee" :options="employeeOptions" placeholder="Search Employee" :multiple="true"
          class="w-full" option-label="label" option-value="value" />
      </div>

      <!-- Form actions -->
      <div class="flex gap-4 justify-end">
        <button type="button" @click="clearForm" class="px-6 py-2 rounded-full bg-gray-600 text-white">
          Clear
        </button>
        <button @click="submitLeaveRequest" type="submit" class="px-6 py-2 rounded-full bg-gray-600 text-white">
          Request
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch,onMounted } from 'vue';
import { ChevronDownIcon, AlertCircleIcon } from 'lucide-vue-next';
import { DatePicker, Autocomplete, createResource ,toast} from 'frappe-ui';

// Component props
const props = defineProps({
  leaveTypes: {
    type: Array,
    required: true
  },
  employeeOptions: {
    type: Array,
    required: true
  },
  employeeId:{
    type: String,
    required: true
  },
  leaveApprover:{
    type: String,
    required: true
  }
});

// Define emits
const emit = defineEmits(['submitRequest']);

// Form state
const fromDate = ref(null);
const toDate = ref(null);
const selectedLeaveType = ref('');
const durationType = ref('Full Day');
const leaveReason = ref('');
const notifyEmployee = ref([]);
const selectedHalfDay = ref('');
const optionalHoliday=ref(false)
const selectedHoliday = ref('')
const optionalHolidays = ref([])
const employeeOptions = ref([
  // { label: 'Waseem Ahmed', value: '<EMAIL>' },
  // { label: 'Sandeep kakde', value: '<EMAIL>' },
]);

// const leaveDuration = computed(() => {
//   if (!fromDate.value || !toDate.value) return 0;
//   const from = new Date(fromDate.value);
//   const to = new Date(toDate.value);
//   const diffTime = Math.abs(to - from);
//   const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
//   return diffDays + 1; 
// });

const leaveDuration = computed(() => {
  if (!fromDate.value || !toDate.value) return 0;

  const from = new Date(fromDate.value);
  const to = new Date(toDate.value);
  const diffTime = Math.abs(to - from);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

  // Subtract 0.5 if Half Day is selected
  return durationType.value === 'Half Day' ? diffDays - 0.5 : diffDays;
});


// Date formatting function
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};
const halfDayOptions = computed(() => {
  if (!fromDate.value || !toDate.value) return [];
  
  const dates = [];
  const currentDate = new Date(fromDate.value);
  const endDate = new Date(toDate.value);
  
  while (currentDate <= endDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return dates;
});

/**
 * Submits the leave request form
 */
const submitLeaveRequest = () => {
  if (!fromDate.value || !toDate.value || !selectedLeaveType.value || 
      (durationType.value === 'Half Day' && !selectedHalfDay.value)) {
    alert('Please fill all required fields');
    return;
  }

  const newRequest = {
    period: `${formatDate(fromDate.value)} - ${formatDate(toDate.value)} (${leaveDuration.value} days)`,
    type: selectedLeaveType.value,
    requestedOn: formatDate(new Date()),
    note: leaveReason.value || 'No reason provided',
    status: 'Pending',
    notify: notifyEmployee.value,
    durationType: durationType.value,
    ...(durationType.value === 'Half Day' && { halfDayOn: formatDate(selectedHalfDay.value) })
  };

  
  
  if(props.employeeId && props.leaveApprover){
    const new_leave = createResource({
      url:'frappe.client.insert',
      makeParams:()=>({
        doc: {
          doctype: "Leave Application",
          employee: props.employeeId,
          leave_type: selectedLeaveType.value,
          from_date: fromDate.value,
          to_date: toDate.value,
          leave_approver: props.leaveApprover,
          half_day_date:selectedHalfDay.value ?new Date(selectedHalfDay.value).toISOString().split('T')[0]: null,
          half_day: durationType.value === 'Half Day'? 1 :0,
          description:leaveReason.value || 'No reason provided',
          notify_users :notifyEmployee.value.map(emp => ({
                      doctype: "IDP Notify User",
                      parenttype: "Leave Application",
                      parentfield: "notify_users",
                      user: emp.value
                    }))
        }
      }),
      auto:true,
      onSuccess:(resp)=>{
        emit('submitRequest', newRequest);
        // alert("Leave application created")
        toast({
            title: 'Success',
            text: 'Leave application created successfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
        // console.log("resp",resp)
        
      },
      onError:(error)=>{
        // alert(error)
        // console.log("error",error)
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create leave application',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      }
    })
  }
  clearForm();
  // alert('Leave request submitted successfully!');
};

// Update clearForm to reset half day selection
const clearForm = () => {
  fromDate.value = null;
  toDate.value = null;
  selectedLeaveType.value = '';
  durationType.value = 'Full Day';
  leaveReason.value = '';
  notifyEmployee.value = [];
  selectedHalfDay.value = '';
  selectedHoliday.value=''
  optionalHoliday.value=false
};
function get_user(){
  const user = createResource({
    url: 'frappe.client.get_list',
      makeParams: () => ({
        doctype: 'User',
        fields: ['name', 'full_name'],
        filters: [['enabled', '=', 1]],
      }),
      auto: true,
      onSuccess: (res) => {
        console.log(res)
        employeeOptions.value = res.map(user => ({
          label: user.full_name || user.name,
          value: user.name
        }));
      },
      onError: (error) => {
        console.log(error)
      },
  })
}

onMounted(() => {
  get_user()
})

// Watch the leaveType value
watch(selectedLeaveType, async (newValue, oldValue) => {
    if (newValue) {
        try {
            const optionalLeaves=createResource({
              url:"inspira.inspira.api.leaves.user_leaves.optional_holiday",
              makeParams:()=>({
                leave_type:newValue
              }),
              auto: true,
              onSuccess: (res) => {
                // console.log(res.is_optional_leave)
                if (res.is_optional_leave){
                  optionalHoliday.value = true
                  optionalHolidays.value=res.holiday_list
                  fromDate.value = ''
                  toDate.value = ''
                }else{
                  optionalHoliday.value = false
                }
              },
              onError:(error)=>{
                console.log(error)
              }
            })
            // You can update other form fields or state here based on API response
        } catch (error) {
            console.error('API Error:', error)
        }
    }
})

watch(selectedHoliday, (newValue) => {
  if (newValue) {
    const holiday = optionalHolidays.value.find(h => h.holiday_date === newValue)
    if (holiday) {
      fromDate.value = holiday.holiday_date
      toDate.value = holiday.holiday_date
      leaveReason.value = holiday.description
    }
  } 
})
</script>