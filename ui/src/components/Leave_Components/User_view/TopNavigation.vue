<template>
      <div class="flex gap-2 mr-4 justify-end bg-[#EEEAF4] rounded-md p-0.5">
        <button 
          v-for="role in roles" 
          :key="role" 
          class="px-4 py-0.5 rounded"
          :class="{'bg-white shadow-xl': activeRole === role}"
          @click="$emit('updateRole', role)"
        >
          {{ role }}
        </button>
      </div>
  </template>
  
  <script setup>
  const props = defineProps({
    roles: {
      type: Array,
      required: true
    },
    activeRole: {
      type: String,
      required: true
    }
  });
  
  // Define emits
  defineEmits(['updateRole']);
  </script>
  
  