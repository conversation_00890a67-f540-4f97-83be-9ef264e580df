<template>
  <div>
    <h2 class="text-base font-medium mb-4">Leave Balances</h2>
    <div class="w-full h-44 overflow-auto p-0.5">
      <div v-if="balances.length > 0" class="grid grid-cols-2 gap-4">
        <div v-for="balance in balances" :key="balance.type" class="p-4 border rounded-md shadow-sm">
          <div class="text-sm text-gray-600">{{ balance.type }}</div>
          <div class="text-2xl mt-1">{{ balance.count }} / {{ balance.Total }}</div>
        </div>
      </div>
      <div v-else class="flex items-center justify-center h-full text-gray-500 text-sm border rounded">
        No data available
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  balances: {
    type: Array,
    required: true
  }
});
</script>
