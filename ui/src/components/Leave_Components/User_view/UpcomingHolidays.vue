<template>
  <div>
    <h2 class="text-base font-medium mb-4">Upcoming Holidays</h2>
    <div v-if="holidays.length > 0" class="w-full h-[33.5rem] overflow-auto border rounded p-2 space-y-2">
      <div v-for="(holiday, index) in holidays" :key="index" class="p-4 border-b rounded-md flex items-start gap-4">
        <!-- Holiday icon -->
        <div
          class="w-7 h-7 rounded-full bg-purple-200 text-gray-700 text-xs font-bold flex items-center justify-center">
          {{ getInitials(holiday.name) }}
        </div>

        <!-- Holiday details -->
        <div class="flex-1">
          <div class="text-sm text-blue-500">{{ holiday.date }}</div>
          <div class="text-xs text-gray-500">{{ holiday.type }} - {{ holiday.name }}</div>
        </div>
      </div>
    </div>
    <div v-else class="text-gray-500 text-sm text-center border h-[33.5rem] py-8 rounded">
        No data available
      </div>
  </div>
</template>

<script setup>
// Component props
const props = defineProps({
  holidays: {
    type: Array,
    required: true
  }
});

const getInitials = (name) => {
  if (!name || typeof name !== 'string') return 'NA';

  const words = name.trim().split(' ').filter(Boolean);
  if (words.length === 0) return 'NA';

  return words
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2) || 'NA';
};

</script>