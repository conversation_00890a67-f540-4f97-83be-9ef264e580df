<template>
  <div class="w-full">
    <!-- Main content grid -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 p-4">
      <!-- Left column - Leave balances and history -->
      <div class="space-y-6">
        <!-- Leave Balances component -->
        <LeaveBalances :balances="leaveBalances" />

        <!-- Leave History component -->
        <LeaveHistory 
        :historyTabs="historyTabs" 
        :activeHistoryTab="activeHistoryTab" 
        :upcomingLeaves="upcomingLeaves" 
        :PastLeaves="PastLeaves"
        @updateHistoryTab="activeHistoryTab = $event" 
        @cancelLeave="handleCancelLeave" />
      </div>

      <!-- Middle column - Upcoming holidays component -->
      <UpcomingHolidays :holidays="upcomingHolidays" />

      <!-- Right column - Leave request form component -->
      <LeaveRequestForm :leaveTypes="leaveTypes" :employeeId="employee_name" :leaveApprover="leaveApprover" @submitRequest="handleLeaveRequest" @scrollToRequests="scrollToRequests" />
    </div>

    <!-- Pending Leave Requests component -->
    <PendingRequests :requests="pendingRequests" :balances="leaveBalances" class="scrollToRequests" />
  </div>
</template>

<script setup>
import { ref ,onMounted } from 'vue';
import LeaveBalances from './User_view/LeaveBalances.vue';
import LeaveHistory from './User_view/LeaveHistory.vue';
import UpcomingHolidays from './User_view/UpcomingHolidays.vue';
import LeaveRequestForm from './User_view/LeaveRequestForm.vue';
import PendingRequests from './User_view/PendingRequests.vue';
import { sessionUser} from '../../data/session'
import {createResource,createListResource} from 'frappe-ui'
// Leave balances data
const login_user = sessionUser()
const leaveBalances = ref([
  // { type: 'Sick Leave', count: 6 ,Total:6 },
  // { type: 'Parental Leave', count: 28,Total:28 },
  // { type: 'Paid Leave', count: 12 ,Total:12},
  // { type: 'Bereavement Leave', count: 4,Total:4 },
  // { type: 'National Leaves', count: 2 ,Total:2 },
  // { type: 'Happy Leaves', count: 8,Total:10 },
]);

// Leave history data
const historyTabs = ['Upcoming', 'Past'];
const activeHistoryTab = ref('Upcoming');

// Upcoming leaves data
const upcomingLeaves = ref([
  // {
  //   date: 'Jan 1st',
  //   type: 'Sick Leave',
  //   duration: '1 day',
  //   icon: 'RefreshCw'
  // },
  // {
  //   date: 'Jan 4th - 8th 2025',
  //   type: 'Vacation',
  //   duration: '4 days',
  //   icon: 'PlaneTakeoff'
  // },
  // {
  //   date: 'Jan 4th - 8th 2025',
  //   type: 'Vacation',
  //   duration: '4 days',
  //   icon: 'PlaneTakeoff'
  // },
]);
// Pasat leaves data
const PastLeaves = ref([
  // {
  //   date: 'Dec 31st',
  //   type: 'Enjoy Leave',
  //   duration: '1 day',
  //   icon: 'PlaneTakeoff'
  // },
  // {
  //   date: 'Jan 1st - 7th 2025',
  //   type: 'Bali Trip',
  //   duration: '4 days',
  //   icon: 'PlaneTakeoff'
  // },
  // {
  //   date: 'Jan 4th - 8th 2025',
  //   type: 'Vacation',
  //   duration: '4 days',
  //   icon: 'PlaneTakeoff'
  // },
  // {
  //   date: 'Dec 31st',
  //   type: 'Enjoy Leave',
  //   duration: '1 day',
  //   icon: 'PlaneTakeoff'
  // },
  // {
  //   date: 'Jan 1st - 7th 2025',
  //   type: 'Bali Trip',
  //   duration: '4 days',
  //   icon: 'PlaneTakeoff'
  // },
  // {
  //   date: 'Jan 4th - 8th 2025',
  //   type: 'Vacation',
  //   duration: '4 days',
  //   icon: 'PlaneTakeoff'
  // },
]);

// Leave types data
const leaveTypes = ref([
  // 'Sick Leave', 'Casual Leave', 'Vacation', 'Parental Leave', 'Bereavement Leave'
]);

// Upcoming holidays data
const upcomingHolidays = ref([
  // {
  //   date: 'Jan 1st, 2025',
  //   type: 'National Holiday',
  //   name: 'New Year',
  //   icon: 'NationalHoliday'
  // },
  // {
  //   date: 'Jan 26th, 2025',
  //   type: 'Public Holiday',
  //   name: 'Republic Day',
  //   icon: 'Flag'
  // },
  // {
  //   date: 'Mar 18th, 2025',
  //   type: 'Regional Holiday',
  //   name: 'Holi',
  //   icon: 'Umbrella'
  // },
  // {
  //   date: 'Jun 15th, 2025',
  //   type: 'Public Holiday',
  //   name: 'Independence Day',
  //   icon: 'Flag'
  // },
  // {
  //   date: 'Oct 2nd, 2025',
  //   type: 'Regional Holiday',
  //   name: 'Gandhi Jayanti / Dussehra',
  //   icon: 'Award'
  // },
  // {
  //   date: 'Nov 18th - 20th, 2025',
  //   type: 'Regional Holiday',
  //   name: 'Diwali',
  //   icon: 'Gift'
  // },
  // {
  //   date: 'Dec 26th, 2025',
  //   type: 'National Holiday',
  //   name: 'Christmas',
  //   icon: 'ChristmasFlag'
  // }
]);

// Pending leave requests data
const pendingRequests = ref([
  // {
  //   period: 'Mar 21 - Mar 26, 2025 (5 days)',
  //   type: 'Sick Leave',
  //   requestedOn: 'Jan 10, 2025',
  //   note: 'Wedding Celebrations',
  //   status: 'Pending'
  // }
]);

const handleLeaveRequest = (requestData) => {
  // Add to pending requests
  pendingRequests.value.unshift(requestData);
};

const scrollToRequests = () => {
  document.querySelector('.scrollToRequests').scrollIntoView({ behavior: 'smooth' });
};

const employee_name =ref('')
const leaveApprover = ref('')
const date = new Date()
const current_date = date.toISOString().split('T')[0]
// onMounted(() => {
//   const employee = createResource({
//       url: 'frappe.client.get',
//       makeParams: () => ({
//         doctype: 'Employee',
//         user_id: login_user,
//         status :'Active',
//         // fieldname: ['name','leave_approver']
//         // value: section.name,
//       }),
//       auto: true,
//       onSuccess: (res) => {
//         employee_name.value = res.name
//         leaveApprover.value = res.leave_approver
//         console.log(employee_name.value)
//         console.log(login_user)
//         get_leave_balance(employee_name.value,current_date)
//         get_holidays(employee_name.value,current_date,current_date)
//       },
//       onError: (error) => {
//         console.log(error)
//       },
//   })

//   get_pending_leaves(login_user)
  
// })
onMounted(() => {
  const employee = createResource({
    url: 'frappe.client.get_value',
    makeParams: () => ({
      doctype: 'Employee',
      filters: {
        user_id: login_user,
        status: 'Active'
      },
      fieldname: ['name', 'leave_approver']
    }),
    auto: true,
    onSuccess: (res) => {
      console.log(res)
      employee_name.value = res.name
      leaveApprover.value = res.leave_approver

      console.log("Employee:", employee_name.value)
      console.log("Leave Approver:", leaveApprover.value)

      get_leave_balance(employee_name.value, current_date)
      get_holidays(employee_name.value, current_date, current_date)
    },
    onError: (error) => {
      console.log("Error fetching employee:", error)
    }
  })

  get_pending_leaves(login_user)
})

function get_pending_leaves(login_user){
  const ownerOfTaskResource = createResource({
    url: 'inspira.inspira.api.leaves.user_leaves.get_pending_leaves',
    makeParams: () => ({
      user: login_user,
    }),
    auto: true,
    onSuccess: (data) => {
      pendingRequests.value = data.pending_leaves
      PastLeaves.value = data.past_leaves
      upcomingLeaves.value = data.upcoming_leaves
      // leaveTypes.value = data.leave_types
    },
    onError: (error) => {
      console.error('Failed to fetch user name:', error)
    },
  })
}

function get_leave_balance(employee_name,date){
  console.log(employee_name)
  const leave_balance = createResource({
    url: 'hrms.hr.doctype.leave_application.leave_application.get_leave_details',
    makeParams: () => ({
      employee: employee_name,
      date:date
    }),
    auto: true,
    onSuccess: (data) => {
      const rawBalances = data.leave_allocation || {}
      const lwps = data.lwps || [];
      leaveBalances.value = Object.entries(rawBalances).map(([type, details]) => ({
        type,
        count: details.remaining_leaves,
        Total: details.total_leaves
      }))
      const allocated = Object.entries(rawBalances)
        .filter(([_, details]) => details.total_leaves > 0)
        .map(([type]) => type);
      leaveTypes.value = [...allocated, ...lwps];
    },
    onError: (error) => {
      console.error('Failed to fetch user name:', error)
    },
  })
}

function get_holidays(employee_name,from_date,to_date){
  const holidays = createResource({
    url: "inspira.inspira.api.leaves.user_leaves.get_holidays_for_employee",
    params: {
      employee: employee_name,
    },
    auto: true,
    onSuccess: (data) => {
      console.log(data)
      upcomingHolidays.value = data.map((holiday) => ({
        date: formatHolidayDate(holiday.holiday_date), // format as 'Jan 1st, 2025'
        type: holiday.holiday_type || 'Holiday',
        name: holiday.description || 'Unnamed Holida"y',
        icon: ""
      }));
    },
  })
}
function formatHolidayDate(dateString) {
  const date = new Date(dateString);
  const options = { month: 'short' }; // e.g., "Jan"
  const month = new Intl.DateTimeFormat('en-US', options).format(date);
  const day = date.getDate();
  const year = date.getFullYear();

  // Get ordinal suffix for day
  const ordinal = (n) => {
    if (n > 3 && n < 21) return 'th'; // 4th-20th
    switch (n % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  };

  return `${month} ${day}${ordinal(day)}, ${year}`; // e.g., "Jan 1st, 2025"
}

const handleCancelLeave = (leave) => {
  leave.status = 'Cancelled'
  console.log('Leave cancelled:', leave)
}

</script>
