<template>
  <div class="w-full">
    <!-- Calendar and upcoming leaves grid -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 h-full">
      <div class="md:col-span-3 h-full">
        <div class="flex justify-between items-center p-2">
          <div class="flex items-center gap-2">
            <button class="p-1" @click="navigateMonth(-1)">
              <ChevronLeft class="w-5 h-5 text-gray-600" />
            </button>
            <div class="flex items-center gap-1">
              <span class="text-gray-800 font-medium w-32 text-center">{{ currentMonth }} , {{ currentYear }}</span>
            </div>
            <button class="p-1" @click="navigateMonth(1)">
              <ChevronRight class="w-5 h-5 text-gray-600" />
            </button>
            <button
              class="px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
              @click="goToCurrentMonth">
              This month
            </button>
            <button
              class="ml-4 px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
              @click="scrollToRequests">
              Pending Requests
            </button>
          </div>

          <div class="flex items-center gap-2">
            <div class="relative">
              <Autocomplete placeholder="Select Employee" class="w-48" :options="employeeOptions"
                v-model="selectedEmployee" @change="handleEmployeeChange">
                <template #prefix>
                  <UserIcon />
                </template>
              </Autocomplete>
            </div>
          </div>
        </div>

        <LeaveCalendar :key="selectedEmployee" :currentMonth="currentMonth" :currentYear="currentYear"
          :calendarData="processedCalendarData" :selectedEmployee="selectedEmployeeName" />
      </div>

      <div class="h-[39.5rem]">
        <h2 class="text-lg mb-3 mt-3">Upcoming Leaves</h2>
        <UpcomingLeaves :leaves="upcomingLeaves.map(leave => ({
          date: leave.displayDate,
          employeeName: leave.employeeName,
          leaveType: leave.leaveType,
          avatar: null,
          id : leave.id
        }))" 
        @cancelLeave="handleCancelLeave"/>
      </div>
    </div>

    <!-- Pending leave requests -->
    <PendingLeaveRequests :requests="pendingRequests" @approveRequest="approveRequest" @rejectRequest="rejectRequest"
      @viewDetails="openLeaveDetails" class="scrollToRequests" />

    <!-- Leave details popup -->
    <div v-if="showLeaveDetails" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg w-full max-w-xl p-6 max-h-[90vh] text-sm overflow-y-auto">
        <!-- Header -->
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Leave Request Details</h2>
          <button @click="showLeaveDetails = false" class="text-gray-500 hover:text-gray-700">
            <X class="w-5 h-5" />
          </button>
        </div>

        <!-- Content -->
        <div class="space-y-4" v-if="selectedLeaveDetails">
          <!-- Employee info -->
          <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-md">
            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center text-purple-600">
              <User class="w-5 h-5" />
            </div>
            <div>
              <div class="font-medium">{{ selectedLeaveDetails.user.name }}</div>
              <div class="text-sm text-gray-500">EID: {{ selectedLeaveDetails.user.id }}</div>
            </div>
          </div>

          <!-- Leave details -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Leave Period</div>
              <div class="font-medium">{{ selectedLeaveDetails.upcomingLeave }}</div>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Leave Type</div>
              <div class="font-medium">{{ selectedLeaveDetails.leaveType }}</div>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Requested On</div>
              <div class="font-medium">{{ selectedLeaveDetails.requestedOn }}</div>
            </div>
            <div class="flex flex-col gap-1">
              <div class="text-sm text-gray-500">Status</div>
              <div class="font-medium">
                <span class="px-2 py-1 rounded-md text-sm" :class="{
                  'bg-red-100 text-red-600': selectedLeaveDetails.status === 'Pending',
                  'bg-green-100 text-green-600': selectedLeaveDetails.status === 'Approved',
                  'bg-gray-100 text-gray-600': selectedLeaveDetails.status === 'Rejected'
                }">
                  {{ selectedLeaveDetails.status }}
                </span>
              </div>
            </div>
          </div>

          <!-- Leave note -->
          <div>
            <div class="text-sm text-gray-500 mb-1">Leave Note</div>
            <div class="p-3 bg-gray-50 rounded-md">
              {{ selectedLeaveDetails.leaveNote }}
            </div>
          </div>

          <!-- Leave balance (mock data) -->
          <!-- <div>
            <div class="text-sm text-gray-500 mb-1">Leave Balance</div>
            <div class="grid grid-cols-4 gap-2">
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Sick Leave</div>
                <div class="font-medium">6 days</div>
              </div>
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Casual Leave</div>
                <div class="font-medium">8 days</div>
              </div>
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Paid Leave</div>
                <div class="font-medium">12 days</div>
              </div>
              <div class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">Unpaid Leave</div>
                <div class="font-medium">Unlimited</div>
              </div>
            </div>
          </div> -->
          <div>
            <div class="text-sm text-gray-500 mb-1">Leave Balance</div>
            <div class="grid grid-cols-4 gap-2">
              <div v-for="(balance, index) in leaveBalance" :key="index" class="p-3 bg-gray-50 rounded-md">
                <div class="text-sm text-gray-500">{{ balance.type }}</div>
                <div class="font-medium">{{ balance.count }} days</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-3 mt-6"
          v-if="selectedLeaveDetails && selectedLeaveDetails.status === 'Pending'">
          <button @click="rejectRequest(selectedLeaveDetails)"
            class="px-4 py-2 border border-red-500 text-red-500 rounded-md hover:bg-red-50">
            Reject
          </button>
          <button @click="approveRequest(selectedLeaveDetails)"
            class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600">
            Approve
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { ChevronDown, ChevronLeft, ChevronRight, User, X } from 'lucide-vue-next';
import {
  toast,
  Autocomplete,
} from 'frappe-ui'
import UserIcon from '../icons/UserIcon.vue'
import LeaveCalendar from './Manager_view/LeaveCalendar.vue';
import UpcomingLeaves from './Manager_view/UpcomingLeaves.vue';
import PendingLeaveRequests from './Manager_view/PendingLeaveRequests.vue';
import { sessionUser} from '../../data/session'
import {createResource,createListResource} from 'frappe-ui'

const login_user = sessionUser()
// Employee data
const employees = ref([
  // { id: '129819', name: 'Kevin Edward' },
  // { id: '129820', name: 'Rucha Mahabal' },
  // { id: '129821', name: 'Kristin Watson' },
  // { id: '129822', name: 'Akshay Rane' },
  // { id: '129823', name: 'Amit Tandoni' },
  // { id: '129824', name: 'Riddhi Chavan' },
  // { id: '129825', name: 'Radha Singhane' }
]);
const leaveBalance = ref([])
// Employee options for autocomplete
const employeeOptions = computed(() => {
  return employees.value.map(emp => ({
    label: emp.name,
    value: emp.id
  }));
});

// Current date state
const date = ref(new Date());
const currentMonth = computed(() => {
  const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
  return months[date.value.getMonth()];
});
const currentYear = computed(() => date.value.getFullYear());

// Employee selection (store ID as string)
const selectedEmployee = ref('');

// Selected employee name (for filtering)
const selectedEmployeeName = computed(() => {
  if (!selectedEmployee.value) return '';
  // Find employee by ID (from autocomplete)
  const employee = employees.value.find(emp => emp.id === selectedEmployee.value);
  return employee ? employee.name : '';
});
const selectedEmployeeId = computed(() => {
  if (!selectedEmployee.value) return '';
  // Find employee by ID (from autocomplete)
  const employee = employees.value.find(emp => emp.id === selectedEmployee.value);
  return employee ? employee.id : '';
});
// Updated leave data in properly structured format
const upcomingLeaves = ref([
  // {
  //   id: 1,
  //   employeeName: 'Kevin Edward',
  //   leaveType: 'Casual Leave',
  //   startDate: '2025-04-02',
  //   endDate: '2025-04-03',
  //   displayDate: 'April 2nd - 4th, 2025',
  //   status: 'Approved'
  // },
  // {
  //   id: 2,
  //   employeeName: 'Rucha Mahabal',
  //   leaveType: 'Casual Leave',
  //   startDate: '2025-02-11',
  //   endDate: '2025-02-12',
  //   displayDate: 'Feb 11th - 12th, 2025',
  //   status: 'Approved'
  // },
  // {
  //   id: 3,
  //   employeeName: 'Kristin Watson',
  //   leaveType: 'Unpaid Leave',
  //   startDate: '2025-02-24',
  //   endDate: '2025-02-25',
  //   displayDate: 'Feb 24th - 25th, 2025',
  //   status: 'Approved'
  // },
  // {
  //   id: 4,
  //   employeeName: 'Akshay Rane',
  //   leaveType: 'Casual Leave',
  //   startDate: '2025-03-24',
  //   endDate: '2025-03-25',
  //   displayDate: 'Mar 24th - 25th, 2025',
  //   status: 'Approved'
  // },
  // {
  //   id: 5,
  //   employeeName: 'Amit Tandoni',
  //   leaveType: 'Sick Leave',
  //   startDate: '2025-03-12',
  //   endDate: '2025-03-21',
  //   displayDate: 'Mar 12th - 21st, 2025',
  //   status: 'Approved'
  // },
  // {
  //   id: 6,
  //   employeeName: 'Riddhi Chavan',
  //   leaveType: 'Casual Leave',
  //   startDate: '2025-03-18',
  //   endDate: '2025-03-18',
  //   displayDate: 'Mar 18th, 2025',
  //   status: 'Approved'
  // },
  // {
  //   id: 7,
  //   employeeName: 'Radha Singhane',
  //   leaveType: 'Unpaid Leave',
  //   startDate: '2025-03-24',
  //   endDate: '2025-03-25',
  //   displayDate: 'Mar 24th - 25th, 2025',
  //   status: 'Approved'
  // }
]);

// Month navigation functions
const goToCurrentMonth = () => {
  date.value = new Date();
};

const navigateMonth = (direction) => {
  const newDate = new Date(date.value);
  newDate.setMonth(newDate.getMonth() + direction);
  date.value = newDate;
};

// Process upcoming leaves data to be displayed on calendar
// const processedCalendarData = computed(() => {
//   const monthIndex = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'].indexOf(currentMonth.value);
//   const daysInMonth = getDaysInMonth(currentYear.value, monthIndex);
//   const firstDayOfMonth = getFirstDayOfMonth(currentYear.value, monthIndex);

//   const data = [];

//   // Add holidays (weekends)
//   for (let i = 1; i <= daysInMonth; i++) {
//     const dayOfWeek = (firstDayOfMonth + i - 1) % 7;
//     if (dayOfWeek === 0) {
//       data.push({ day: i, isHoliday: true, holidayName: 'Holiday: Sunday' });
//     }
//     if (dayOfWeek === 6) {
//       data.push({ day: i, isHoliday: true, holidayName: 'Holiday: Saturday' });
//     }
//   }

//   // Add leaves if employee is selected
//   if (selectedEmployeeName.value) {
//     console.log("Filtering for employee:", selectedEmployeeName.value);

//     upcomingLeaves.value.forEach(leave => {
//       const startDate = new Date(leave.startDate);
//       const endDate = new Date(leave.endDate);

//       // Only process if leave is in current month/year
//       if ((startDate.getMonth() === monthIndex && startDate.getFullYear() === currentYear.value) ||
//         (endDate.getMonth() === monthIndex && endDate.getFullYear() === currentYear.value)) {

//         // Only show exactly matched employee name
//         if (leave.employeeName === selectedEmployeeName.value) {
//           let startDay = startDate.getMonth() === monthIndex ? startDate.getDate() : 1;
//           let endDay = endDate.getMonth() === monthIndex ? endDate.getDate() : daysInMonth;

//           data.push({
//             employeeName: leave.employeeName,
//             leaveType: leave.leaveType,
//             startDay: startDay,
//             endDay: endDay,
//             day: startDay,
//             status: leave.status
//           });
//         }
//       }
//     });
//   }

//   console.log("Calendar data updated:", data.length, "items");
//   return data;
// });
const employeeHolidays = ref([]);
// Define the resource only once

function getHolidays(){
  createResource({
    url: "inspira.inspira.api.leaves.user_leaves.get_holidays_for_employee",
    makeParams: () => ({
      employee: selectedEmployeeId.value
    }),
    auto: false,
    onSuccess: (data) => {
      employeeHolidays.value = data;
    }
  }).fetch();
}
// Watch for employee change and trigger fetch
watch(selectedEmployeeId, (newVal) => {
  if (newVal) {
    // employeeHolidayResource.fetch();
    getHolidays()
  } else {
    employeeHolidays.value = [];
  }
}, { immediate: true });

// const processedCalendarData = computed(() => {
//   const monthIndex = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'].indexOf(currentMonth.value);
//   const daysInMonth = getDaysInMonth(currentYear.value, monthIndex);
//   const firstDayOfMonth = getFirstDayOfMonth(currentYear.value, monthIndex);

//   const data = [];

//   // Add holidays (weekends)
//   for (let i = 1; i <= daysInMonth; i++) {
//     if (!selectedEmployeeId.value){
//       const dayOfWeek = (firstDayOfMonth + i - 1) % 7;
//       if (dayOfWeek === 0) {
//         data.push({ day: i, isHoliday: true, holidayName: 'Holiday: Sunday' });
//       }
//       if (dayOfWeek === 6) {
//         data.push({ day: i, isHoliday: true, holidayName: 'Holiday: Saturday' });
//       }
//     }else{
//       console.log(":::::::::::::")
//         console.log(employeeHolidays.value)
//         const empHoliday = employeeHolidays.value.find(item => {
//           console.log(item)
//           let dateObj = new Date(item.holiday_date);
//           // console.log(dateObj.getDate())
//           return dateObj.getDate() === i
//         });
//         console.log(empHoliday)
//         if (empHoliday) {
          
//           data.push({ day: i, isHoliday: true, holidayName: empHoliday.description });
//         }
//       console.log(data)
//     }
//   }

//   const leaveDayCount = {}; // For aggregating leave counts when no employee is selected

//   upcomingLeaves.value.forEach(leave => {
//     const startDate = new Date(leave.startDate);
//     const endDate = new Date(leave.endDate);

//     // Only process leaves that touch this month/year
//     if ((startDate.getMonth() === monthIndex && startDate.getFullYear() === currentYear.value) ||
//         (endDate.getMonth() === monthIndex && endDate.getFullYear() === currentYear.value)) {

//       let startDay = startDate.getMonth() === monthIndex ? startDate.getDate() : 1;
//       let endDay = endDate.getMonth() === monthIndex ? endDate.getDate() : daysInMonth;

//       if (selectedEmployeeName.value) {
//         // Show individual leave range for selected employee
//         if (leave.employeeName === selectedEmployeeName.value) {
//           data.push({
//             employeeName: leave.employeeName,
//             leaveType: leave.leaveType,
//             startDay: startDay,
//             endDay: endDay,
//             day: startDay,
//             status: leave.status
//           });
//         }
//       } else {
//         // Count leaves per day for all employees
//         // for (let d = startDay; d <= endDay; d++) {
//         //   leaveDayCount[d] = (leaveDayCount[d] || 0) + 1;
//         // }
//         data.push({
//             employeeName: leave.employeeName,
//             leaveType: leave.leaveType,
//             startDay: startDay,
//             endDay: endDay,
//             day: startDay,
//             status: leave.status
//         });

//       }
//     }
//   });

//   // Add aggregate leave counts to data when no employee is selected
//   //if (!selectedEmployeeName.value) {
//   //  for (let day in leaveDayCount) {
//   //    data.push({
//   //      day: parseInt(day),
//   //      // leaveCount: leaveDayCount[day],
//   //      employeeName:leaveDayCount[day],
        
//   //    });
//   //  }
//   //}

//   console.log("Calendar data updated:", data.length, "items");
//   return data;
// });

// Calendar helper functions

const processedCalendarData = computed(() => {
  const monthIndex = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ].indexOf(currentMonth.value);

  const daysInMonth = getDaysInMonth(currentYear.value, monthIndex);
  const firstDayOfMonth = getFirstDayOfMonth(currentYear.value, monthIndex);

  const data = [];

  // Append holidays
  for (let i = 1; i <= daysInMonth; i++) {
    if (!selectedEmployeeId.value) {
      const dayOfWeek = (firstDayOfMonth + i - 1) % 7;
      if (dayOfWeek === 0) { // Sunday
        data.push({ day: i, isHoliday: true, holidayName: 'Holiday: Sunday' });
      }
      if (dayOfWeek === 6) { // Saturday
        data.push({ day: i, isHoliday: true, holidayName: 'Holiday: Saturday' });
      }
    } else {
      const empHoliday = employeeHolidays.value.find(item => {
        let dateObj = new Date(item.holiday_date);
        return dateObj.getDate() === i &&
               dateObj.getMonth() === monthIndex &&
               dateObj.getFullYear() === currentYear.value;
      });

      if (empHoliday) {
        data.push({ day: i, isHoliday: true, holidayName: empHoliday.description,leaveType:'Holiday' });
      }
    }
  }

  // Append leaves
  upcomingLeaves.value.forEach(leave => {
    const startDate = new Date(leave.startDate);
    const endDate = new Date(leave.endDate);

    if ((startDate.getMonth() === monthIndex && startDate.getFullYear() === currentYear.value) ||
        (endDate.getMonth() === monthIndex && endDate.getFullYear() === currentYear.value)) {

      let startDay = startDate.getMonth() === monthIndex ? startDate.getDate() : 1;
      let endDay = endDate.getMonth() === monthIndex ? endDate.getDate() : daysInMonth;

      for (let d = startDay; d <= endDay; d++) {
        if (selectedEmployeeName.value) {
          if (leave.employeeName === selectedEmployeeName.value) {
            data.push({
              day: d,
              employeeName: leave.employeeName,
              leaveType: leave.leaveType,
              status: leave.status
            });
          }
        } else {
          data.push({
            day: d,
            employeeName: leave.employeeName,
            leaveType: leave.leaveType,
            status: leave.status
          });
        }
      }
    }
  });

  console.log("Calendar data updated:", data.length, "items");
  return data;
});

const getDaysInMonth = (year, month) => {
  return new Date(year, month + 1, 0).getDate();
};

const getFirstDayOfMonth = (year, month) => {
  return new Date(year, month, 1).getDay();
};

// Pending leave requests
const pendingRequests = ref([
  // {
  //   id: 1,
  //   upcomingLeave: 'Mar 21 - Mar 26, 2025 (5 days)',
  //   user: {
  //     name: 'Kevin Edward',
  //     id: '129819'
  //   },
  //   leaveType: 'Paid Leave',
  //   requestedOn: 'Jan 11, 2025',
  //   leaveNote: 'Wedding Celebrations',
  //   status: 'Pending'
  // },
  // {
  //   id: 2,
  //   upcomingLeave: 'Mar 27, 2025 (1 day)',
  //   user: {
  //     name: 'Kevin Edward',
  //     id: '129819'
  //   },
  //   leaveType: 'Sick Leave',
  //   requestedOn: 'Jan 10, 2025',
  //   leaveNote: 'Checkup Appointment',
  //   status: 'Pending'
  // }
]);

// Leave details popup
const showLeaveDetails = ref(false);
const selectedLeaveDetails = ref(null);
const currentDate = new Date()
const current_date = currentDate.toISOString().split('T')[0]
const openLeaveDetails = (request) => {
  console.log("request request",request)
  selectedLeaveDetails.value = request;
  showLeaveDetails.value = true;
  const leave_balance = createResource({
    url: 'hrms.hr.doctype.leave_application.leave_application.get_leave_details',
    makeParams: () => ({
      employee: request.employee,
      date:current_date
    }),
    auto: true,
    onSuccess: (data) => {
      const rawBalances = data.leave_allocation || {}
      leaveBalance.value = Object.entries(rawBalances).map(([type, details]) => ({
        type,
        count: details.remaining_leaves,
        Total: details.total_leaves
      }))
    },
    onError: (error) => {
      console.error('Failed to fetch user name:', error)
    },
  })
};

// Handle approve/reject requests
const approveRequest = (request) => {
  const index = pendingRequests.value.findIndex(r => r.id === request.id);
  if (index !== -1) {
    pendingRequests.value[index].status = 'Approved';
    submit_record(request,'Approved')
  }
  showLeaveDetails.value = false;
};

const rejectRequest = (request) => {
  const index = pendingRequests.value.findIndex(r => r.id === request.id);
  if (index !== -1) {
    pendingRequests.value[index].status = 'Rejected';
    submit_record(request,'Rejected')
  }
  showLeaveDetails.value = false;
};

const autocompleteSelection = ref(null);
const handleEmployeeChange = (value) => {
  selectedEmployee.value = value;
};
watch(selectedEmployee, (newVal) => {
  if (newVal && typeof newVal === 'object' && newVal.value) {
    // If we received an object with value property, extract just the ID
    selectedEmployee.value = newVal.value;
  }
}, { immediate: true });
const scrollToRequests = () => {
  document.querySelector('.scrollToRequests').scrollIntoView({ behavior: 'smooth' });
};
let allpendingRequests = []
let allupcomingLeaves = []
// Get employee records
function get_leaves() {
  const user = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'Leave Application',
      fields: [
        'name', 'employee_name', 'employee', 'leave_type',
        'from_date', 'to_date', 'posting_date', 'status',
        'total_leave_days', 'description'
      ],
      filters: [['leave_approver', '=', login_user]],
    }),
    auto: true,
    onSuccess: (res) => {
      const today = new Date().toISOString().split('T')[0]; // 'YYYY-MM-DD'
      const formatDate = (date) =>
        new Date(date).toLocaleString('en-US', { month: 'short', day: 'numeric' });

      // Approved Leaves (Upcoming)
      allupcomingLeaves= res
        .filter(leave => leave.status === "Approved" &&
          (leave.from_date >= today || leave.to_date >= today))
        .map(leave => {
          const startDateStr = formatDate(leave.from_date);
          const endDateStr = formatDate(leave.to_date);
          const year = new Date(leave.to_date).getFullYear();
          const displayDate = leave.total_leave_days > 1
            ? `${startDateStr} - ${endDateStr}, ${year}`
            : `${startDateStr}, ${year}`;

          return {
            id: leave.name,
            employeeName: leave.employee_name,
            leaveType: leave.leave_type,
            startDate: leave.from_date,
            endDate: leave.to_date,
            displayDate,
            status: leave.status,
            leaveNote: leave.description,
            employee: leave.employee
          };
        });

      // Pending Leave Requests
      allpendingRequests= res
        .filter(leave => leave.status === "Open")
        .map(leave => {
          const startDateStr = formatDate(leave.from_date);
          const endDateStr = formatDate(leave.to_date);
          const year = new Date(leave.to_date).getFullYear();
          const days = leave.total_leave_days;
          const upcomingLeave = days > 1
            ? `${startDateStr} - ${endDateStr}, ${year} (${days} day${days > 1 ? 's' : ''})`
            : `${startDateStr} (1 day)`;

          return {
            id: leave.name,
            employee: leave.employee,
            upcomingLeave,
            user: {
              name: leave.employee_name,
              id: leave.employee,
            },
            leaveType: leave.leave_type,
            requestedOn: leave.posting_date,
            leaveNote: leave.description || '',
            status: "Pending",
          };
        });

      // Unique Employees List
      const uniqueMap = new Map();
      res.forEach(emp => {
        if (!uniqueMap.has(emp.employee)) {
          uniqueMap.set(emp.employee, {
            id: emp.employee,
            name: emp.employee_name
          });
        }
      });
      employees.value = Array.from(uniqueMap.values());
      upcomingLeaves.value  = allupcomingLeaves
      pendingRequests.value = allpendingRequests
    },
    onError: (error) => {
      console.error('Error fetching leave applications:', error);
    },
  });
}

watch(selectedEmployee, (newEmp) => {
  if (!newEmp) {
    upcomingLeaves.value = allupcomingLeaves;
    pendingRequests.value = allpendingRequests;
  } else {
    upcomingLeaves.value = allupcomingLeaves.filter(l => l.employee === newEmp);
    pendingRequests.value = allpendingRequests.filter(l => l.employee === newEmp);
  }
});
function submit_record(response,status){
  const leaveRecord = createResource({
    url:'inspira.inspira.api.leaves.user_leaves.submit',
    params:({
      name:response.id,
      status:status
    }),
    auto:true,
    onSuccess:(resp)=>{
      // console.log("leave Request Submitted")
      toast({
            title: 'Success',
            text: `leave ${status} sucessfully`,
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
    },
    onError:(error)=>{
      //  console.log(error)
       toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to cancel request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    }
  })
}
onMounted(() => {
  get_leaves()
});

const handleCancelLeave = (leave) => {
  leave.status = 'Cancelled'
  console.log('Leave cancelled:', leave)
}

</script>