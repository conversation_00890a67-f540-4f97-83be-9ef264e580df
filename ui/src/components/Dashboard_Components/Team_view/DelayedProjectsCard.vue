<template>
  <div class="bg-white rounded-sm shadow-md p-4 h-full">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Delayed Projects</h2>
    
    <div class="overflow-auto h-56">
      <table class="min-w-full">
        <thead>
          <tr class="bg-[#ECE6F0] text-left sticky top-0">
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Project Name</th>
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Current Milestone</th>
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Progress</th>
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Planned End Date</th>
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Days Delayed</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(project, index) in delayedProjects" :key="index" class="border-b border-gray-100 bg-[#F7F7FD]">
            <td class="py-2 px-3 text-sm text-gray-700">{{ project.name }}</td>
            <td class="py-2 px-3 text-sm text-gray-600">{{ project.milestone }}</td>
            <td class="py-2 px-3">
              <div class="flex items-center gap-2">
                <div class="w-24 bg-gray-200 h-2.5">
                  <div class="bg-[#5d4a8a] h-2.5" :style="{ width: project.progress + '%' }"></div>
                </div>
                <span class="text-xs text-gray-600">{{ project.progress }}%</span>
              </div>
            </td>
            <td class="py-2 px-3 text-sm text-gray-600">{{ formatDate(project.endDate,'DD-MMM-YYYY') }}</td>
            <td class="py-2 px-3 text-sm font-medium" :class="project.daysDelayed > 0 ? 'text-red-500' : 'text-[#9d91c4]'">
              {{ project.daysDelayed }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { formatDate } from '../../../utils/format';
defineProps({
  delayedProjects: {
    type: Array,
    required: true
  }
});
</script>