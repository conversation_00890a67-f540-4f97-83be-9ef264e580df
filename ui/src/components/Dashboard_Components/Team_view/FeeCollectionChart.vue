<template>
  <div class="w-full h-full">
    <apexchart type="donut" height="128" :options="chartOptions" :series="chartData.series"></apexchart>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
});

const chartOptions = computed(() => {
  return {
    chart: {
      type: 'donut',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      }
    },
    plotOptions: {
      pie: {
        donut: {
          size: '70%',
          labels: {
            show: true,
            name: {
              show: false
            },
            value: {
              show: true,
              fontSize: '14px',
              fontWeight: 600,
              color: '#1f2937',
              formatter: function(val) {
                return val + '%';
              }
            },
            total: {
              show: true,
              showAlways: true, 
              label: props.chartData.labels[0],
              fontSize: '12px',
              color: '#6b7280',
              formatter: function() {
                return props.chartData.series[0] + '%';
              }
            }
          }
        }
      }
    },
    colors: ['#9d91c4', '#7e73a6'],
    labels: props.chartData.labels,
    dataLabels: {
      enabled: false
    },
    legend: {
      show: false
    },
    stroke: {
      width: 0
    }
  };
});
</script>