<template>
  <table class="w-full table-fixed border-b border-gray-100">
    <tbody>
      <tr class="h-8">
        <td class="text-sm text-gray-600 px-4 w-1/2 border-r border-gray-100">
          {{ label }}
        </td>
        <td class="px-6 w-1/2">
          <div class="flex justify-between items-center">
            <span class="text-sm font-medium">{{ count }}</span>
            <!-- <button class="text-gray-500 hover:text-gray-700">
              <ArrowUpRightIcon size="16" />
            </button> -->
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</template>



<script setup>
import { ArrowUpRight as ArrowUpRightIcon } from 'lucide-vue-next';

defineProps({
  label: {
    type: String,
    required: true
  },
  count: {
    type: String,
    required: true
  }
});
</script>