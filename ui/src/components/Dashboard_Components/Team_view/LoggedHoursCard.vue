<template>
  <div class="bg-white w-full flex flex-col gap-2 h-full">


    <table class="w-full table-fixed h-full">
      <tbody>
      <tr>
        <td class="bg-gray-100 p-3 text-sm text-gray-600">
          Compliance
        </td>
        <td class="bg-[#F9F4F4] p-3 text-sm text-gray-600 border-r border-white">
          <div class="flex items-center">
            <span>{{ complianceMetrics.monthLabel }}:</span>
            <span class="font-medium ml-1">{{ complianceMetrics.currentMonth }}</span>
          </div>
        </td>
        <td class="bg-[#F9F4F4] p-3 text-sm text-gray-600">
          <div class="flex items-center">
            <span>YTD:</span>
            <span class="font-medium ml-1">{{ complianceMetrics.ytd }}</span>
          </div>
        </td>
      </tr>
      </tbody>
    </table>

    <div class="bg-white rounded-sm shadow-md p-4 h-full">
      <h2 class="text-lg font-medium text-gray-700 mb-8">Logged Hours</h2>

      <div class="flex items-center">
        <div class="w-1/2">
          <apexchart type="donut" height="200" :options="chartOptions" :series="chartData.series"></apexchart>
        </div>
        <div class="w-1/2 pl-4">
          <div v-for="(item, index) in chartData.details" :key="index" class="flex items-center justify-between mb-2">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: item.color }"></div>
              <span class="text-sm text-gray-600 ml-1">{{ item.label }}</span>
            </div>
            <span class="text-sm font-medium">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>

  </div>

</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  },
  complianceMetrics: {
    type: Object,
    required: true
  }
});

const chartOptions = computed(() => {
  return {
    chart: {
      type: 'donut',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      }
    },
    plotOptions: {
      pie: {
        donut: {
          size: '65%',
          labels: {
            show: true,
            name: {
              show: false
            },
            value: {
              show: true
            },
            total: {
              show: true,
              showAlways: true, 
              label: props.chartData.series[0],
              formatter: function () {
                return props.chartData.series[0]+ '%';
              }
            }
          }
        }
      }
    },
    colors: ['#9d91c4', '#7e73a6'],
    labels: props.chartData.labels,
    dataLabels: {
      enabled: false
    },
    legend: {
      show: false
    },
    stroke: {
      width: 0
    }
  };
});
</script>