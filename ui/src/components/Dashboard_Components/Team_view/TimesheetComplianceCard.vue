<template>
  <div class="bg-white rounded-sm shadow-md p-4 h-full">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Quarterly Timesheet Compliance</h2>
    
    <div class="h-64">
      <apexchart type="bar" height="100%" :options="chartOptions" :series="chartData.series"></apexchart>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
});

const chartOptions = computed(() => {
  return {
    chart: {
      type: 'bar',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      }
    },
    plotOptions: {
      bar: {
        borderRadius: 2,
        columnWidth: '40%'
      }
    },
    dataLabels: {
      enabled: false
    },
    colors: ['#5d4a8a'],
    xaxis: {
      categories: props.chartData.categories,
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      }
    },
    yaxis: {
      min: 0,
      max: 100,
      tickAmount: 4,
      labels: {
        formatter: function(val) {
          return val + '%';
        }
      }
    },
    grid: {
      borderColor: '#dedede',
      strokeDashArray: 4,
      xaxis: {
        lines: {
          show: false
        }
      }
    },
    tooltip: {
      y: {
        formatter: function(val) {
          return val + '%';
        }
      }
    }
  };
});
</script>