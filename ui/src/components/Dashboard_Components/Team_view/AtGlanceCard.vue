<template>
  <div class="bg-white rounded-sm shadow-md w-full p-4 h-full">
    <h2 class="text-lg font-medium text-gray-700 mb-4">At a glance</h2>

    <!-- Projections Table -->

    <div class="w-full flex gap-2">

      <div class="flex flex-col gap-2">
        <table class="w-full table-fixed">
          <tbody>
            <tr>
              <td class="bg-[#EDEDF1] p-1.5 text-sm text-gray-600 border-r border-white rounded-s-sm">
                Projections
              </td>
              <td class="bg-[#F9F4F4] p-1.5 text-sm text-gray-600 border-r border-white">
                <div class="flex flex-col items-center">
                  <span>Remaining FY</span>
                  <span class="font-medium">{{ projections.remainingFY || 0}}</span>
                </div>
              </td>
              <td class="bg-[#F9F4F4] p-1.5 text-sm text-gray-600 rounded-e-sm">
                <div class="flex flex-col items-center">
                  <span>Current Month:</span>
                  <span class="font-medium">{{ projections.currentMonth ||0}}</span>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <div class="shadow-xl border p-3">
          <h3 class="text-sm font-medium text-gray-600 mb-2">Fee Collection Overview</h3>
          <div class="flex items-center">
            <div class="w-1/2">
              <FeeCollectionChart :chartData="feeCollectionData" />
            </div>
            <div>
              <div v-for="(item, index) in feeCollectionData.details" :key="index"
                class="flex items-center justify-between gap-6">
                <div class="flex items-center">
                  <div class="w-3 h-3 rounded-full mr-1" :style="{ backgroundColor: item.color }"></div>
                  <span class="text-xs text-gray-600">{{ item.label }}</span>
                </div>
                <div>
                  <span v-if="item.value" class="text-xs font-medium">{{ item.value }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Chargeability Overview -->
      <!-- <div class="w-[40%] flex flex-col gap-2 shadow-md bg-[#F7F7FD]">
        <div class="bg-gray-100 p-3 flex justify-between items-center">
          <div class="flex items-center">
            <span class="text-sm text-gray-600">Chargeability :</span>
            <span class="text-sm font-medium ml-1">{{ chargeability.value }}</span>
          </div>
          <div class="bg-amber-100 text-amber-800 px-2 py-0.5 rounded text-xs">
            {{ chargeability.status }}
          </div>
        </div>
        <div class="space-y-6 overflow-y-auto ">
          <div v-for="(metric, index) in financialMetrics" :key="index"
            class="flex justify-between items-center border-b border-white p-3">
            <span class="text-sm text-gray-600">{{ metric.label }}</span>
            <span class="text-sm font-medium">{{ metric.value }}</span>
          </div>
        </div>
      </div> -->

    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import FeeCollectionChart from './FeeCollectionChart.vue';

defineProps({
  projections: {
    type: Object,
    required: true
  },
  chargeability: {
    type: Object,
    required: true
  },
  feeCollectionData: {
    type: Object,
    required: true
  },
  financialMetrics: {
    type: Array,
    required: true
  }
});
</script>