<template>
  <div class="bg-white rounded-sm shadow-md p-3 h-full">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Actions <span class="text-red-700 ml-2 ">!</span></h2>
    
    <!-- Header -->
    <div class="grid grid-cols-2 mb-2">
      <div class="bg-[#f3f0ff] py-1 px-3">
        <span class="text-sm font-medium text-gray-700">Requests</span>
      </div>
      <div class="bg-[#f3f0ff] py-1 px-3">
        <span class="text-sm font-medium text-gray-700">Pending</span>
      </div>
    </div>
    
    <!-- Action Items -->
    <div class="space-y-2 overflow-auto h-48">
      <ActionItem v-for="(item, index) in actionItems" :key="index" 
                 :label="item.label" :count="item.count" />
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
import ActionItem from './ActionItem.vue';

defineProps({
  actionItems: {
    type: Array,
    required: true
  }
});
</script>
<style scoped>
.overflow-auto{
  scrollbar-width: thin;
}
</style>