<template>
  <div class="bg-white rounded-sm shadow-md p-4 h-full">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-lg font-medium text-gray-800">Tracking</h2>
      <div class="text-sm text-gray-600">Team Count : {{ teamCount }}</div>
    </div>

    <!-- Metrics Grid -->
    <div class="flex flex-col gap-10 bg-[#F7F7FD] p-4 border">
      <div class="grid grid-cols-2 gap-10">
        <div v-for="(metric, index) in trackingMetrics" :key="index">
          <div class="text-2xl font-semibold text-gray-900 mb-1">{{ metric.value }}</div>
          <div class="text-sm text-gray-600">{{ metric.label }}</div>
        </div>
      </div>

      <!-- On Site and On Leave Sections -->
      <div class="grid grid-cols-2 gap-6">
        <!-- On Leave Section -->
        <div>
          <!-- <h3 class="text-sm font-medium text-gray-800 mb-3">On Leave</h3> -->
          <!-- <h3 class="text-sm font-medium text-gray-800 mb-3">On Leave</h3> -->
          <div class="flex -space-x-1">
            <div v-for="letter in onLeaveData.letters" :key="letter"
              class="w-7 h-7 rounded-full bg-[#b7b6db] flex items-center justify-center text-gray-800 text-sm font-medium border">
              {{ letter }}
            </div>
            <div
              class="w-7 h-7 rounded-full bg-[#b7b6db] flex items-center justify-center text-gray-800 text-xs font-medium border">
              +{{ onLeaveData.additional }}
            </div>
          </div>
        </div>
        <!-- On Site Section -->
        <div>
          <!-- <h3 class="text-sm font-medium text-gray-800 mb-3">On Site</h3> -->
          <div class="flex -space-x-1">
            <div v-for="letter in onSiteData.letters" :key="letter"
              class="w-7 h-7 rounded-full bg-[#b7b6db] flex items-center justify-center text-gray-800 text-sm font-medium border">
              {{ letter }}
            </div>
            <div
              class="w-7 h-7 rounded-full bg-[#b7b6db] flex items-center justify-center text-gray-800 text-xs font-medium border">
              +{{ onSiteData.additional }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  teamCount: {
    type: Number,
    required: true
  },
  trackingMetrics: {
    type: Array,
    required: true
  },
  onSiteData: {
    type: Object,
    required: true
  },
  onLeaveData: {
    type: Object,
    required: true
  }
});
</script>