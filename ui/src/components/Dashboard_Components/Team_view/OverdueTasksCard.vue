<template>
  <div class="bg-white rounded-sm shadow-md p-4 h-full">
    <h2 class="text-lg font-medium text-gray-700 mb-4">Employees with most overdue tasks</h2>
    
    <div class="overflow-auto h-56">
      <table class="min-w-full">
        <thead>
          <tr class="bg-[#ECE6F0] text-left sticky top-0">
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Employee</th>
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Assigned</th>
            <th class="py-2 px-3 text-sm font-medium text-gray-700 border-r border-gray-100 whitespace-nowrap">Overdue</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(employee, index) in employees" :key="index" class="border-b border-gray-100 bg-[#F7F7FD]">
            <td class="py-2 px-3 text-sm text-gray-700">{{ employee.name }}</td>
            <td class="py-2 px-3 text-sm text-gray-600">{{ employee.assigned }}</td>
            <td class="py-2 px-3 text-sm font-medium" :class="employee.overdue > 10 ? 'text-red-500' : 'text-[#7c7c7c]'">
              {{ employee.overdue }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
defineProps({
  employees: {
    type: Array,
    required: true
  }
});
</script>