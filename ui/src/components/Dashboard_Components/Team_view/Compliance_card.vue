<template>
  <section class="bg-white rounded-sm shadow-md h-full">
    <div class="p-3 border-b flex justify-between items-center">
      <h2 class="text-lg font-medium text-gray-700">Timesheet Compliance</h2>
        <router-link to="/hrms#Timesheets" class="text-gray-500 hover:text-gray-700">
        <external-link-icon class="w-5 h-5" />
      </router-link>
    </div>

    <div class="overflow-auto h-64">
      <table class="min-w-full">
        <thead>
          <tr class="bg-[#ECE6F0] sticky top-0">
            <th class="py-3 px-4 text-center text-sm font-medium text-gray-700 border-r border-gray-100">Critical
              Employees</th>
            <th class="py-3 px-4 text-center text-sm font-medium text-gray-700 border-r border-gray-100">Current Week -
              Compliance</th>
            <th class="py-3 px-4 text-center text-sm font-medium text-gray-700">Monthly %</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(employee, index) in complianceData.employees" :key="index" class="bg-purple-50">
            <td class="py-3 px-4 text-sm text-gray-800 border-r-2 border-gray-100 text-center">{{ employee.name }}</td>
            <td class="py-3 px-4 border-r-2 border-gray-100">
              <div class="flex items-center justify-between">
                <div v-for="(day, dayIndex) in employee.irregularities" :key="dayIndex" class="relative">
                  <div class="w-5 h-5 rounded-full border border-purple-300 flex items-center justify-center">
                    <!-- Filled (checkmark) -->
                    <svg v-if="day === 'filled'" xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 text-purple-600"
                      fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>

                    <!-- Holiday (H) -->
                    <!-- <span v-else-if="day === 'holiday'" class="text-[9px] text-purple-600">H</span> -->
                    <svg v-else-if="day === 'holiday'" xmlns="http://www.w3.org/2000/svg"
                      class="h-3 w-3 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>

                    <!-- Weekoff (X) -->
                    <!-- <svg v-else-if="day === 'weekoff'" xmlns="http://www.w3.org/2000/svg"
                      class="h-3 w-3 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg> -->
                    <svg v-else-if="day === 'weekoff'" xmlns="http://www.w3.org/2000/svg"
                      class="h-3 w-3 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>

                    <!-- Partial (!) -->
                    <span v-else-if="day === 'partial'" class="text-[9px] text-purple-600">!</span>


                    <!-- Empty (unfilled) -->
                    <span v-else class="w-5 h-5"></span>
                  </div>

                  <!-- Connecting line -->
                  <!-- <div v-if="dayIndex < employee.irregularities.length - 1"
                    class="absolute top-1/2 left-full w-2 h-0.5 bg-purple-300 transform -translate-y-1/2"></div> -->
                </div>
              </div>
            </td>
            <td class="py-3 px-4 text-sm text-gray-800 text-center">{{ employee.compliance }}%</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>
</template>

<script setup>
import { ExternalLink as ExternalLinkIcon } from 'lucide-vue-next';
import { RouterLink } from 'vue-router'
defineProps({
  complianceData: {
    type: Object,
    required: true
  }
});
</script>