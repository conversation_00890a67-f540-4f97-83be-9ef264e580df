<template>
  <section class="bg-white rounded-sm shadow-md">
    <div class="p-3 border-b">
      <h2 class="text-lg font-medium text-gray-700">Attendance & Leaves</h2>
    </div>

    <div class="p-2 flex gap-4">
      <!-- Left: Calendar Section -->
      <div class="flex-1">
        <div class="border rounded-sm shadow bg-[#F7F7FD]">
          <div class="flex justify-between items-center mb-2 bg-[#ECE6F0] p-2">
            <div class="flex items-center gap-2">
              <button @click="prevMonth" class="text-gray-500 hover:text-gray-700">
                <ChevronLeft class="w-4 h-4" />
              </button>
              <span class="text-sm font-medium text-gray-700">{{ currentMonthYear }}</span>
              <button @click="nextMonth" class="text-gray-500 hover:text-gray-700">
                <ChevronRight class="w-4 h-4" />
              </button>
            </div>
            <Edit3 class="w-4 h-4 text-gray-500" />
          </div>

          <div class="grid grid-cols-7 gap-1 text-center p-1">
            <div v-for="day in ['S', 'M', 'T', 'W', 'T', 'F', 'S']" :key="day" class="text-xs font-medium">
              {{ day }}
            </div>
          </div>

          <div class="grid grid-cols-7 gap-1 text-center p-1">
            <div v-for="day in blankDays" :key="'blank-' + day" class="h-6"></div>
            <div v-for="day in daysInMonth" :key="day" @click="selectDate(day)"
              class="w-6 h-6 mx-auto flex items-center justify-center rounded-full cursor-pointer text-xs"
              :class="getDateClass(day)">
              {{ day }}
            </div>
          </div>
        </div>
      </div>

      <!-- Middle: Check in/out Section -->
      <div class="flex-1">
        <div class="bg-[#F7F7FD] rounded-sm shadow p-4 h-full">
          <!-- Main Time Display -->
          <div class="text-2xl font-bold text-gray-800 mb-1">{{ currentTime }}</div>
          <div class="text-sm text-gray-500 mb-4">{{ formattedDate }}</div>

          <!-- Check in/out Section -->
          <div class="mb-4">
            <div class="flex justify-between mb-2">
              <span class="text-sm text-gray-600 ml-4">Check in</span>
              <span class="text-sm text-gray-600 mr-6">Check out</span>
            </div>
            <div class="flex justify-between mb-4">
              <div class="flex items-center gap-1">
                <ArrowDownLeftIcon class="w-3 h-3 text-green-500" />
                <span class="text-sm  flex border rounded bg-white p-1.5">{{ trackingData.checkInTime }}
                <Clock class="w-3 h-3 text-gray-400 ml-1" /></span>
              </div>
              <div class="flex items-center gap-1">
                <ArrowUpRight class="w-3 h-3 text-red-500" />
                <span class="text-sm flex border rounded bg-white p-1.5">{{ trackingData.checkOutTime }}
                <Clock class="w-3 h-3 text-gray-400 ml-1" /></span>
              </div>
            </div>
          </div>

          <!-- Stats Section -->
          <div class="flex justify-between gap-2">
            <div>
              <div class="text-md text-gray-800">{{ trackingData.avgHours }}</div>
              <div class="text-[12px] text-gray-500">Avg/Hrs Per Day</div>
            </div>
            <div>
              <div class="text-md text-gray-800">{{ trackingData.onTimeArrival }}%</div>
              <div class="text-[12px] text-gray-500">On Time Arrival</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right: Leave Information -->
      <div class="flex-1">
        <div class="bg-[#F7F7FD] rounded-sm shadow h-full">
          <table class="w-full border-separate border-spacing-0 text-left">
            <tbody>
              <tr>
                <td class="border border-white text-sm text-gray-700 p-4.5">Total Allocated Leaves</td>
                <td class="border border-white text-2xl text-gray-800 p-4.5 text-right">{{ leaveData.totalAllocated }}
                </td>
              </tr>
              <tr>
                <td class="border border-white text-sm text-gray-700 p-4.5">Leaves Utilized</td>
                <td class="border border-white text-2xl text-gray-800 p-4.5 text-right">{{ leaveData.utilized }}</td>
              </tr>
              <tr>
                <td class="text-sm text-gray-700 p-4.5">Leaves Remaining</td>
                <td class="border-l-2 border-white text-2xl text-gray-800 p-4.5 text-right">{{ leaveData.remaining }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>


    </div>
  </section>
</template>

<script setup>
import { ChevronLeft, ChevronRight, Edit3, Check, ArrowUpRight, Clock, ArrowDownLeftIcon } from 'lucide-vue-next';
import { ref, computed, onMounted, watch } from 'vue';

const props = defineProps({
  attendanceData: {
    type: Object,
    required: true
  },
  trackingData: {
    type: Object,
    required: true
  },
  leaveData: {
    type: Object,
    required: true
  },
  allAttendanceMonths: {
    type: Array,
    required: true
  }
});

// Define emits to communicate with parent component
const emit = defineEmits(['month-changed']);

const currentDate = ref(new Date());
const selectedDate = ref(null);
const time = ref(new Date());
const currentAttendanceData = ref({ days: [] });

// Parse the initial month from props
onMounted(() => {
  if (props.attendanceData.month) {
    const [month, year] = props.attendanceData.month.split(' ');
    currentDate.value = new Date(`${year}-${month}-01`);
  }
  
  // Set initial attendance data
  currentAttendanceData.value = props.attendanceData;

  // Update time every second
  updateTime();
  setInterval(updateTime, 1000);
});

// Watch for changes in currentDate and update attendance data accordingly
watch(currentDate, (newDate) => {
  updateAttendanceForCurrentMonth();
}, { deep: true });

// Also watch for changes in allAttendanceMonths prop
watch(() => props.allAttendanceMonths, () => {
  updateAttendanceForCurrentMonth();
}, { deep: true });

const updateTime = () => {
  time.value = new Date();
};

const updateAttendanceForCurrentMonth = () => {
  const currentMonthStr = currentDate.value.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  
  // Find matching month data in allAttendanceMonths
  const matchingMonth = props.allAttendanceMonths.find(monthData => 
    monthData.month === currentMonthStr
  );
  
  if (matchingMonth) {
    currentAttendanceData.value = matchingMonth;
    // Emit event to parent to update the main attendance data if needed
    emit('month-changed', matchingMonth);
  } else {
    // If no data found for this month, set empty data
    currentAttendanceData.value = {
      month: currentMonthStr,
      days: [],
      percentage: 0
    };
    emit('month-changed', currentAttendanceData.value);
  }
};

const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
});

const daysInMonth = computed(() => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();
  return new Date(year, month + 1, 0).getDate();
});

const blankDays = computed(() => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();
  const firstDay = new Date(year, month, 1).getDay();
  return firstDay;
});

// Format current time as HH:MM:SS AM/PM
const currentTime = computed(() => {
  const hours = time.value.getHours();
  const minutes = time.value.getMinutes();
  const seconds = time.value.getSeconds();
  const ampm = hours >= 12 ? 'PM' : 'AM';

  const formattedHours = hours % 12 || 12;
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds} ${ampm}`;
});

// Format current date as DAY DD, MONTH YYYY
const formattedDate = computed(() => {
  const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
  return time.value.toLocaleDateString('en-US', options).toUpperCase();
});

const prevMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1);
};

const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1);
};

const selectDate = (day) => {
  selectedDate.value = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth(),
    day
  );
};

const getDateClass = (day) => {
  const date = new Date(
    currentDate.value.getFullYear(),
    currentDate.value.getMonth(),
    day
  );
  const dateStr = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

  // Use currentAttendanceData instead of props.attendanceData
  const attendanceDay = currentAttendanceData.value.days?.find(d => d.date === dateStr);

  if (!attendanceDay) return 'text-gray-400';

  return {
    'bg-[#93c592] text-white': attendanceDay.status === 'present',
    'bg-[#c59293] text-white': attendanceDay.status === 'absent',
    'bg-gradient-to-t from-red-400 to-green-400 text-white': attendanceDay.status === 'half-day'
  };
};
</script>