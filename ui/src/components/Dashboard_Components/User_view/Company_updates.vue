<template>
    <section class="bg-white rounded-sm shadow-md">
      <div class="p-3 border-b">
        <h2 class="text-lg font-medium text-gray-700">Company Updates</h2>
      </div>
      
      <div class="p-2">
        <div v-for="(update, index) in updates" :key="index" 
             class="mb-4 last:mb-0 p-2 bg-purple-50 rounded-sm">
          <div class="flex justify-between items-start">
            <h3 class="font-medium text-gray-800">{{ update.name }}</h3>
            <span class="text-xs text-gray-500">{{ update.timeAgo }}</span>
          </div>
          <p class="text-sm text-gray-600 mt-1" v-html="formatMessage(update.message)"></p>
        </div>
        
        <div v-if="updates.length === 0" class="text-center py-4 text-gray-500">
          No updates available
        </div>
      </div>
    </section>
  </template>
  
  <script setup>
  defineProps({
    updates: {
      type: Array,
      required: true
    }
  });
  
  const formatMessage = (message) => {
    return message.replace('❤️', '❤️');
  };
  </script>