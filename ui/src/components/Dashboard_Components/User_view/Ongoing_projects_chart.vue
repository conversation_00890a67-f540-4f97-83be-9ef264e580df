<template>
  <section class="bg-white rounded-sm shadow-md">
    <div class="p-3 border-b">
      <h2 class="text-lg font-medium text-gray-700">Ongoing Projects</h2>
    </div>

    <div class="p-2">
      <apexchart
        type="donut"
        height="250"
        :options="chartOptions"
        :series="[projectStats.delayedPercentage, projectStats.onTrackPercentage]"
      ></apexchart>
    </div>
  </section>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  projectStats: {
    type: Object,
    required: true
  }
});

const chartOptions = computed(() => {
  return {
    chart: {
      type: 'donut',
      fontFamily: 'inherit',
    },
    colors: ['#D7CCE9', '#8F82AA'],
    labels: [
      `${props.projectStats.onTrackProjects} Projects On track`,
      `${props.projectStats.delayedProjects} Delayed Projects`,
    ],
    dataLabels: {
      enabled: false
    },
    plotOptions: {
      pie: {
        donut: {
          size: '75%',
          labels: {
            show: true,
            name: {
              show: true,
              fontSize: '14px',
              offsetY: -5,
              color: '#666'
            },
            value: {
              show: true,
              fontSize: '14px',
              fontWeight: 'bold',
              color: '#111',
              offsetY: 10,
              formatter: function (val) {
                return val + '%';
              }
            },
            total: {
              show: true,
              label: `${props.projectStats.onTrackProjects} Projects On track`,
               fontSize: '12px',
              formatter: function () {
                return `${props.projectStats.onTrackPercentage}%`;
              }
            }
          }
        }
      }
    },
    legend: {
      position: 'bottom',
      offsetY: 0,
      horizontalAlign: 'center',
      itemMargin: {
        horizontal: 10,
        vertical: 8
      },
      markers: {
        width: 12,
        height: 12,
        radius: 2
      }
    },
    stroke: {
      width: 0
    },
    tooltip: {
      enabled: true
    }
  };
});
</script>