<template>
  <section class="bg-white rounded-sm shadow-md">
    <div class="p-3">
      <h2 class="text-lg font-medium text-gray-700">Projects</h2>
    </div>

    <div class="overflow-auto max-h-72">
      <table class="min-w-full h-full">
        <thead>
          <tr class="bg-[#ECE6F0] sticky top-0">
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Project</th>
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Milestone</th>
            <!-- <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Start Date</th> -->
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">End Date</th>
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Days Left</th>
          </tr>
        </thead>
        <tbody class="bg-purple-50">
          <tr v-if="projects && projects.length > 0" v-for="(project, index) in projects" :key="index">
            <td class="py-3 px-4 text-sm text-gray-800">{{ project.project }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ project.milestone }}</td>
            <!-- <td class="py-3 px-4 text-sm text-gray-800">{{ project.startDate }}</td> -->
            <td class="py-3 px-4 text-sm text-gray-800">{{ project.endDate }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ project.daysLeft }}</td>
          </tr>
          <tr v-else>
            <td colspan="100%">
              <div class="flex flex-col items-center justify-center h-60 w-full text-center text-gray-500">
                <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="text-sm font-medium text-gray-500">No Data Found</h3>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>
</template>

<script setup>
defineProps({
  projects: {
    type: Array,
    required: true
  }
});
</script>