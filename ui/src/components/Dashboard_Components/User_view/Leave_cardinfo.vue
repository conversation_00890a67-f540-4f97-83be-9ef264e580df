<template>
  <section class="bg-white rounded-sm shadow-md ">
    <div class="p-3 border-b">
      <h2 class="text-lg font-medium text-gray-700">Leave</h2>
    </div>

    <div class=" bg-purple-50 p-1">
      <table class="w-full text-left">
        <tbody>
          <tr>
            <td class="text-sm font-medium text-gray-700 py-2 pr-12 border-r border-purple-300">Total Allocated Leaves</td>
            <td class="text-lg font-bold text-gray-800 py-2 pl-4">{{ leaveData.totalAllocated }}</td>
          </tr>
          <tr>
            <td class="text-sm font-medium text-gray-700 py-2 pr-12 border-r border-purple-300">Leaves Utilized</td>
            <td class="text-lg font-bold text-gray-800 py-2 pl-4">{{ leaveData.utilized }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>
</template>

<script setup>
defineProps({
  leaveData: {
    type: Object,
    required: true
  }
});
</script>
