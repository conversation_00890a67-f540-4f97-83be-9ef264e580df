<template>
  <section class="rounded-sm shadow-md">
    <div class="p-3 border-b border-white flex justify-between items-center">
      <h2 class="text-lg font-medium text-gray-700">Task Status</h2>
        <router-link to="/My_Tasks" class="text-gray-500 hover:text-gray-700">
        <external-link-icon class="w-5 h-5" />
      </router-link>
    </div>

    <table class="w-full border-separate border-spacing-0 text-left p-3">
      <tbody class="bg-[#F7F7FD] shadow">
        <tr>
          <td class=" p-5 flex items-center gap-2">
            <div class="w-3 h-3 rounded-xs bg-red-400"></div>
            <span class="text-sm text-gray-700">Past Due</span>
          </td>
          <td class="border border-white p-5 text-right text-lg font-bold text-gray-800">{{ taskStatus.pastDue }}</td>
        </tr>
        <tr>
          <td class="border-t border-white p-5 flex items-center gap-2">
            <div class="w-3 h-3 rounded-xs bg-green-400"></div>
            <span class="text-sm text-gray-700">This Week</span>
          </td>
          <td class="border border-white p-5 text-right text-lg font-bold text-gray-800">{{ taskStatus.thisWeek }}</td>
        </tr>
        <tr>
          <td class="border-t border-white p-5 flex items-center gap-2">
            <div class="w-3 h-3 rounded-xs bg-blue-400"></div>
            <span class="text-sm text-gray-700">Due Later</span>
          </td>
          <td class="border border-white p-5 text-right text-lg font-bold text-gray-800">{{ taskStatus.dueLater }}</td>
        </tr>
        <tr>
          <td class="border-t border-white p-5 flex items-center gap-2">
            <div class="w-3 h-3 rounded-xs bg-gray-400"></div>
            <span class="text-sm text-gray-700">No Due date</span>
          </td>
          <td class="border border-white p-5 text-right text-lg font-bold text-gray-800">{{ taskStatus.noDueDate }}</td>
        </tr>
      </tbody>
    </table>
  </section>
</template>

<script setup>
import { ExternalLink as ExternalLinkIcon } from 'lucide-vue-next';
import { RouterLink } from 'vue-router'
defineProps({
  taskStatus: {
    type: Object,
    required: true
  }
});
</script>
