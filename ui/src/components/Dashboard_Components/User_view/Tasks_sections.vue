<template>
  <section class="bg-white rounded-sm shadow-md">
    <div class="p-3 border-b">
      <h2 class="text-lg font-medium text-gray-700">Tasks</h2>
    </div>

    <div class="border-b">
      <div class="flex">
        <button v-for="tab in tabs" :key="tab.id" @click="activeTab = tab.id"
          class="px-6 py-3 text-sm font-medium border-b-2 transition-colors" :class="activeTab === tab.id ?
            'border-purple-600 text-purple-600' :
            'border-transparent text-gray-500 hover:text-gray-700'">
          {{ tab.name }}
        </button>
      </div>
    </div>

    <div class="overflow-auto max-h-56">
      <table class="min-w-full">
        <thead>
          <tr class="bg-[#ECE6F0] sticky top-0">
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Task Name</th>
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Group</th>
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Project</th>
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Due Date</th>
            <th class="py-3 px-4 text-left text-sm font-medium text-gray-700">Status</th>
          </tr>
        </thead>
        <tbody class="bg-purple-50">
          <tr v-for="(task, index) in currentTasks" :key="index">
            <td class="py-3 px-4 text-sm text-gray-800">{{ task.name }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ task.group }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ task.project }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">{{ task.dueDate }}</td>
            <td class="py-3 px-4 text-sm text-gray-800">
              <span class="w-full text-center px-2 py-1 rounded-md text-xs font-medium"
                :style="{ backgroundColor: task.statusColor || 'gray' }">
                {{ task.taskStatus || '-' }}
              </span>
            </td>
          </tr>
          <tr v-if="currentTasks.length === 0">
            <td colspan="5" class="py-4 text-center text-gray-500">No tasks found</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  tasks: {
    type: Object,
    required: true
  }
});

const tabs = [
  { id: 'overdue', name: 'Overdue' },
  { id: 'todo', name: 'To do' }
];

const activeTab = ref('overdue');

const currentTasks = computed(() => {
  return props.tasks[activeTab.value] || [];
});
</script>