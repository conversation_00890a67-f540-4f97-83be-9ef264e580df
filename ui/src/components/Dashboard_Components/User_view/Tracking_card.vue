<template>
  <div class="bg-white rounded-sm shadow-md p-4">
    <h3 class="text-gray-700 font-medium mb-4">Tracking</h3>
    
    <div class="bg-[#F7F7FD] rounded-lg p-4">
      <div class="flex justify-between mb-3">
        <div>
          <div class="text-xl font-bold text-indigo-900">{{ trackingData.avgHours }}</div>
          <div class="text-xs text-gray-500">AVG / HOURS A DAY</div>
        </div>
        <div>
          <div class="text-xl font-bold text-indigo-900">{{ trackingData.onTimeArrival }}%</div>
          <div class="text-xs text-gray-500">ON TIME ARRIVAL</div>
        </div>
      </div>
      
      <div class="flex justify-between items-center mb-2">
        <div class="text-xl font-bold text-gray-800">{{ currentTime }}</div>
        <div class="bg-purple-100 text-purple-600 text-xs px-3 py-1 rounded-md">
          {{ trackingData.status }}
        </div>
      </div>
      
      <div class="text-xs text-gray-500 mb-4">
        {{ formattedDate }}
      </div>
      
      <div class="text-sm text-gray-700 font-medium mb-3">
        {{ trackingData.message }}
      </div>
      
      <div class="flex -space-x-2">
        <template v-for="(letter, index) in trackingData.dayLetters" :key="index">
          <div 
            class="w-7 h-7 rounded-full flex items-center justify-center text-xs font-medium border-2"
            :class="index === trackingData.activeDay ? 'bg-[#B1B2D8] text-[#171837]' : 'bg-[#B1B2D8] text-[#171837]'"
          >
            {{ letter }}
          </div>
        </template>
        <div class="w-7 h-7 rounded-full bg-[#B1B2D8] text-[#171837] flex items-center justify-center text-xs font-medium">
          +{{ trackingData.additionalDays }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// Props with default values for the component
const props = defineProps({
  trackingData: {
    type: Object,
    default: () => ({
      avgHours: 6.8,
      onTimeArrival: 60,
      status: 'CHECKED IN',
      message: 'Out today',
      dayLetters: ['A', 'R', 'S', 'P', 'M', 'N', 'O'],
      additionalDays: 8,
      activeDay: 0
    })
  }
});

// Current time state
const time = ref(new Date());

// Update time every second
const updateTime = () => {
  time.value = new Date();
};

// Format current time as HH:MM:SS AM/PM
const currentTime = computed(() => {
  const hours = time.value.getHours();
  const minutes = time.value.getMinutes();
  const seconds = time.value.getSeconds();
  const ampm = hours >= 12 ? 'PM' : 'AM';
  
  const formattedHours = hours % 12 || 12;
  const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;
  
  return `${formattedHours}:${formattedMinutes}:${formattedSeconds} ${ampm}`;
});

// Format current date as DAY DD, MONTH YYYY
const formattedDate = computed(() => {
  const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
  return time.value.toLocaleDateString('en-US', options).toUpperCase();
});

// Set up timer when component is mounted
onMounted(() => {
  updateTime();
  setInterval(updateTime, 1000);
});
</script>