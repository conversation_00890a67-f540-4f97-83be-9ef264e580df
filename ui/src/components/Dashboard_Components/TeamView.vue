<template>
  <div class="w-full flex flex-col gap-2">
    <!-- Top row -->
    <div class="w-full flex gap-2 items-stretch">
      <div class="w-[39.5%] h-full">
        <AtGlanceCard :projections="projections" :chargeability="chargeability" :feeCollectionData="feeCollectionData"
          :financialMetrics="financialMetrics" />
      </div>
      <div class="w-[30%] h-full">
        <TrackingCard :teamCount="teamCount" :trackingMetrics="trackingMetrics" :onSiteData="onSiteData"
          :onLeaveData="onLeaveData" />
      </div>
      <div class="w-[30%] h-full">
        <ActionsCard :actionItems="actionItems" />
      </div>
    </div>

    <!-- Middle row -->
    <div class="w-full flex gap-2 items-stretch">
      <div class="w-[70%] h-full">
        <DelayedProjectsCard :delayedProjects="delayedProjects" />
      </div>
      <div class="w-[30%] h-full">
        <OverdueTasksCard :employees="employeesWithOverdueTasks" />
      </div>
    </div>

    <!-- Bottom row -->
    <div class="w-full flex gap-2 items-stretch">
      <div class="w-[40%] h-full">
        <Compliance_card :complianceData="complianceData" />
      </div>
      <div class="w-[30%] h-full">
        <TimesheetComplianceCard :chartData="timesheetComplianceData" />
      </div>
      <div class="w-[30%] h-full">
        <LoggedHoursCard :chartData="loggedHoursData" :complianceMetrics="complianceMetrics" />
      </div>
    </div>
  </div>
</template>

<script setup>
import {onMounted, ref } from 'vue';
import AtGlanceCard from './Team_view/AtGlanceCard.vue';
import TrackingCard from './Team_view/TrackingCard.vue';
import ActionsCard from './Team_view/ActionsCard.vue';
import DelayedProjectsCard from './Team_view/DelayedProjectsCard.vue';
import OverdueTasksCard from './Team_view/OverdueTasksCard.vue';
import Compliance_card from './Team_view/Compliance_card.vue';
import TimesheetComplianceCard from './Team_view/TimesheetComplianceCard.vue';
import LoggedHoursCard from './Team_view/LoggedHoursCard.vue';
import { createResource } from 'frappe-ui';
const is_employee = window.emp_id

// At a Glance data
const projections = ref({
  //remainingFY: '20,000,00',
  // currentMonth: '9,000,00'
});

const chargeability = ref({
  value: '2.1',
  status: 'Alert'
});

const feeCollectionData = ref({
  series: [
    // 85,15
  ],
  labels: [
    // 'Fee Collected', 'Balance'
  ],
  details: [
    // { color: '#7e73a6', label: 'Invoiced', value: '1,00,000' },
    // { color: '#7e73a6', label: 'Fee Collected', value: '85,000' },
    // { color: '#7e73a6', label: 'Balance ', value: '15,000' },
  ]
});

const financialMetrics = ref([
  { label: 'Revenue Generated', value: '32,00,000' },
  { label: 'Total Expenses', value: '5,00,000' },
  { label: 'Total Seat Cost', value: '50,00,00,000' }
]);

// Tracking data
const teamCount = ref();

const trackingMetrics = ref([
  // { label: 'Early Checkouts', value: 9 },
  // { label: 'Late Check-ins', value: 5 },
  // { label: 'On Leave', value: 8 },
  // { label: 'Outdoor Duty', value: 3 }
]);

const onSiteData = ref({
  // letters: ['A', 'R', 'P', 'O'],
  // additional: 8
});

const onLeaveData = ref({
  // letters: ['A', 'R', 'P', 'O'],
  // additional: 4
});

// Actions data
const actionItems = ref([
  // { label: 'Leave Requests', count: '8' },
  // { label: 'Regularization', count: '14' },
  // { label: 'Timesheet', count: '7' },
  // { label: 'Reimbursement', count: '8' },
  // { label: 'Leave Requests', count: '8' },
  // { label: 'Regularization', count: '14' },
  // { label: 'Timesheet', count: '7' },
  // { label: 'Reimbursement', count: '8' },
  // { label: 'Outdoor Duty', count: '9' }
]);

// Delayed Projects data
const delayedProjects = ref([
  // {
  //   name: 'Breach Candy Square',
  //   milestone: 'Consultation',
  //   progress: 50,
  //   endDate: '01 Feb 2024',
  //   daysDelayed: 12
  // },
  // {
  //   name: 'Urban Oasis: Coastal Luxury A 701',
  //   milestone: 'Concept Development',
  //   progress: 25,
  //   endDate: '16 Feb 2024',
  //   daysDelayed: 5
  // },
  // {
  //   name: 'Breach Candy Square',
  //   milestone: 'Design Presentation & Approval',
  //   progress: 25,
  //   endDate: '27 Feb 2024',
  //   daysDelayed: 5
  // },
  // {
  //   name: 'Bohemian Bliss: By the Bay B 405',
  //   milestone: 'Space Planning & Layout Development',
  //   progress: 50,
  //   endDate: '29 Feb 2024',
  //   daysDelayed: 5
  // },
  // {
  //   name: 'Urban Oasis: Coastal Luxury A 701',
  //   milestone: 'Design Development & Detailing',
  //   progress: 75,
  //   endDate: '01 Feb 2024',
  //   daysDelayed: 5
  // },
  // {
  //   name: 'The Velvet Sky Loft B 905',
  //   milestone: 'Budgeting & Procurement',
  //   progress: 75,
  //   endDate: '16 Feb 2024',
  //   daysDelayed: 5
  // }
]);

// Employees with overdue tasks data
const employeesWithOverdueTasks = ref([
  // { name: 'Aarav Sharma', assigned: 9, overdue: 5 },
  // { name: 'Priya Gupta', assigned: 7, overdue: 3 },
  // { name: 'Rohan Mehta', assigned: 8, overdue: 1 },
  // { name: 'Sneha Patel', assigned: 7, overdue: 4 },
  // { name: 'Vikram Singh', assigned: 10, overdue: 2 },
  // { name: 'Rishit Nathwani', assigned: 6, overdue: 6 }
]);

// Compliance data (already in your code)
const complianceData = ref({
  employees: [
    // {
    //   name: 'Aarav Sharma',
    //   irregularities: ['filled', false, 'holiday', 'weekoff', 'filled', 'filled', 'weekoff'],
    //   compliance: '83%'
    // },
    // {
    //   name: 'Priya Gupta',
    //   irregularities: ['filled', 'holiday', 'weekoff', false, 'filled', 'filled', 'weekoff'],
    //   compliance: '66%'
    // },
    // {
    //   name: 'Rohan Mehta',
    //   irregularities: ['filled', 'filled', 'filled', false, false, false, false],
    //   compliance: '49%'
    // },
    // {
    //   name: 'Sneha Patel',
    //   irregularities: [false, 'filled', false, false, 'filled', 'filled', false],
    //   compliance: '32%'
    // },
    // {
    //   name: 'Vikram Singh',
    //   irregularities: [false, 'filled', false, false, 'filled', false, false],
    //   compliance: '16%'
    // }
  ]
});

// Timesheet Compliance data
const timesheetComplianceData = ref({
  series: [
    // {
    //   name: 'Compliance',
    //   data: [50, 100, 75, 65]
    // }
  ],
  categories: [
    // 'Q1', 'Q2', 'Q3', 'Q4'
  ]
});

// Logged Hours data
const loggedHoursData = ref({
  series: [
    // 85, 15
  ],
  labels: [
    // 'Non Billable hours', 'Billable hours'
  ],
  details: [
    // { color: '#9d91c4', label: 'Non Billable hours', value: '85%' },
    // { color: '#7e73a6', label: 'Billable hours', value: '15%' }
  ]
});

const complianceMetrics = ref({
  // march: '90 %',
  // ytd: '87%'
});
function get_team_details(){
  const tem_details = createResource({
    url: 'inspira.inspira.api.team_landing.team_landing.get_team_details',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        console.log(res)
        // actionItems.value = res
        if (res && res.action_items) {
          actionItems.value = res.action_items;
        }
        if(res && res.employeesWithOverdueTasks){
          employeesWithOverdueTasks.value = res.employeesWithOverdueTasks
        }
        projections.value = res.projections
        teamCount.value = res.employees
        trackingMetrics.value = res.tracking_details.tracking_metrics
        onSiteData.value = res.employee_letters.onSiteData
        onLeaveData.value = res.employee_letters.onLeaveData
        feeCollectionData.value = res.fees_collection
        loggedHoursData.value = res.loggedHoursData
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
function get_milestone_project_data(){
  const tem_details = createResource({
    url: 'inspira.inspira.api.projects.projects_landing.get_milestone_project_data',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        console.log(res)
        // actionItems.value = res
        if (res && res.team_data) {
          delayedProjects.value = res.team_data;
        }
        
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
function get_overall_quarterly_compliance(){
  const tem_details = createResource({
    url: 'inspira.inspira.api.hrms.timesheet.get_overall_quarterly_compliance',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        console.log(res)
        timesheetComplianceData.value = res
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
function get_current_month_and_ytd_compliance(){
  const tem_details = createResource({
    url: 'inspira.inspira.api.hrms.timesheet.get_current_month_and_ytd_compliance',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        console.log(res)
        complianceMetrics.value = {
          monthLabel: res.month,
          currentMonth: `${res.current_month || 0} %`,
          ytd: `${res.ytd} %`
        };
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
function get_current_week_team_timesheet_compliance(){
  const tem_details = createResource({
    url: 'inspira.inspira.api.hrms.timesheet.get_current_week_team_timesheet_compliance',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        console.log(res)
        complianceData.value.employees = res
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
onMounted(() => {
if (is_employee){
  get_team_details()
  get_overall_quarterly_compliance()
 get_current_month_and_ytd_compliance()
 get_current_week_team_timesheet_compliance()
}
 
 get_milestone_project_data()
 
})
</script>