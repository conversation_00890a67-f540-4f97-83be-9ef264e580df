<template>
  <div class="flex flex-col gap-2">
    <div class="flex flex-col lg:flex-row gap-2">
      <Attendance_card :attendanceData="attendanceData" :trackingData="trackingData" :leaveData="leaveData" :allAttendanceMonths="allAttendanceMonths"
        class="flex-[3]" />
      <Timesheet_card :timesheetData="timesheetData" class="flex-[2]" />
    </div>

    <div class="flex flex-col lg:flex-row gap-2">
      <Projects_table :projects="projects" class="flex-[3]" />
      <Ongoing_projects_chart :projectStats="projectStats" class="flex-[1]" />
    </div>

    <div class="flex flex-col lg:flex-row gap-2">
      <Tasks_sections :tasks="tasks" class="flex-[3]" />
      <Task_status_card :taskStatus="taskStatus" class="flex-[1]" />
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import Projects_table from '../Dashboard_Components/User_view/Projects_table.vue';
import Timesheet_card from '../Dashboard_Components/User_view/Timesheet_card.vue';
import Tasks_sections from '../Dashboard_Components/User_view/Tasks_sections.vue';
import Tracking_card from '../Dashboard_Components/User_view/Tracking_card.vue';
import Attendance_card from '../Dashboard_Components/User_view/Attendance_card.vue';
import Leave_cardinfo from '../Dashboard_Components/User_view/Leave_cardinfo.vue';
import Task_status_card from '../Dashboard_Components/User_view/Task_status_card.vue';
import Ongoing_projects_chart from '../Dashboard_Components/User_view/Ongoing_projects_chart.vue';
import { createResource } from 'frappe-ui';
import { sessionUser} from '../../data/session'
import { formatDate } from '../../utils/format';
const is_employee = window.emp_id

// const attendanceData = ref({
//   month: "May 2025",
//   days: [
//     // {
//     //   date: "2025-05-03",
//     //   status: "present"
//     // },
//     // {
//     //   date: "2025-05-07",
//     //   status: "present"
//     // },
//     // {
//     //   date: "2025-05-15",
//     //   status: "absent"
//     // },
//     // {
//     //   date: "2025-05-12",
//     //   status: "present"
//     // },
//     // {
//     //   date: "2025-05-15",
//     //   status: "absent"
//     // },
//     // {
//     //   date: "2025-05-17",
//     //   status: "half-day"
//     // },
//   ],
//   percentage: 87
// });
const allAttendanceMonths = ref([]);
const attendanceData = ref({
  month: "",
  days: [],
  percentage: 0
});
const trackingData = ref({
  // avgHours: 6.8,
  // onTimeArrival: 60,
  // checkInTime: '09:50 AM',
  // checkOutTime: '18:00 AM',
  // status: 'CHECKED IN'
});

const leaveData = ref({
  // totalAllocated: 32,
  // utilized: 5,
  // remaining: 27
});

const projects = ref([
  // {
  //   project: 'Manor House',
  //   milestone: 'Concept Design',
  //   startDate: '02 Mar 2024',
  //   endDate: '02 Mar 2024',
  //   daysLeft: '08'
  // },
  // {
  //   project: 'Manor House',
  //   milestone: 'Design Development',
  //   startDate: '02 Mar 2024',
  //   endDate: '02 Mar 2024',
  //   daysLeft: '08'
  // },
  // {
  //   project: 'Urban Oasis',
  //   milestone: 'Execution',
  //   startDate: '02 Mar 2024',
  //   endDate: '02 Mar 2024',
  //   daysLeft: '08'
  // },
  // {
  //   project: 'Sky loft',
  //   milestone: 'Decor',
  //   startDate: '02 Mar 2024',
  //   endDate: '02 Mar 2024',
  //   daysLeft: '08'
  // },
  // {
  //   project: 'Nariman Square',
  //   milestone: 'Decor',
  //   startDate: '02 Mar 2024',
  //   endDate: '02 Mar 2024',
  //   daysLeft: '08'
  // },
  // {
  //   project: 'Breach Candy Ave',
  //   milestone: 'Handover',
  //   startDate: '02 Mar 2024',
  //   endDate: '02 Mar 2024',
  //   daysLeft: '08'
  // },
]);

const timesheetData = ref({
  weeks: [
    // {
    //   period: '01 Jan 24 - 07 Jan 24',
    //   days: ['filled', false, 'holiday', 'weekoff', 'filled', 'filled', 'weekoff'],
    //   compliance: '83%'
    // },
    // {
    //   period: '08 Jan 24 - 14 Jan 24',
    //   days: ['filled', 'holiday', 'weekoff', false, 'filled', 'filled', 'weekoff'],
    //   compliance: '100%'
    // },
    // {
    //   period: '15 Jan 24 - 21 Jan 24',
    //   days: ['filled', 'filled', false, 'filled', 'filled', 'weekoff', 'weekoff'],
    //   compliance: '100%'
    // },
    // {
    //   period: '22 Jan 24 - 28 Jan 24',
    //   days: ['filled', 'filled', 'filled', 'filled', 'filled', 'filled', 'weekoff'],
    //   compliance: '100%'
    // },
  ]
});

const tasks = ref({
  overdue: [
    // {
    //   name: 'Requirement Analysis',
    //   group: 'Planner',
    //   project: 'Manor House',
    //   dueDate: '02 Mar 2024'
    // },
    // {
    //   name: 'Spatial Analysis',
    //   group: 'Internal',
    //   project: '-',
    //   dueDate: '02 Mar 2024'
    // },
    // {
    //   name: 'All Metal Frameworks for Platforms/counters/Staircases/Wrought Iron etc',
    //   group: 'Planner',
    //   project: 'Sky loft',
    //   dueDate: '02 Mar 2024'
    // },
    // {
    //   name: 'HR Onboarding',
    //   group: 'Personal',
    //   project: '-',
    //   dueDate: '02 Mar 2024'
    // },
  ],
  todo: []
});

const projectStats = ref({
  // delayedProjects: 6,
  // onTrackProjects: 8,
  // delayedPercentage: 46,
  // onTrackPercentage: 54
});
const taskStatus = ref({
  // pastDue: 32,
  // thisWeek: 5,
  // dueLater: 27,
  // noDueDate: 15
});

function get_employee_attendance() {
  const employee_attendance = createResource({
    url: 'inspira.inspira.api.attendance.attendance.get_employee_attendance',
    makeParams: () => ({}),
    auto: true,
    onSuccess: (resp) => {

      allAttendanceMonths.value = resp; // save all months

      if (resp.length > 0) {
        setMonthAttendance(resp[resp.length - 1]); // default: latest month (last in array)
      }
    },
    onError: (error) => {
      console.log("Error while fetching attendance data", error);
    }
  });

  function setMonthAttendance(monthData) {
    attendanceData.value = {
      month: monthData.month,
      days: monthData.days,
      percentage: monthData.percentage
    };
  }
}
function get_employee_tracking_data(){
  const employee_tracking_data = createResource({
    url: 'inspira.inspira.api.attendance.attendance.get_employee_tracking_data',
    makeParams: () => ({}),
    auto: true,
    onSuccess: (resp) => {
      trackingData.value = resp
      
    },
    onError: (error) => {
      console.log("Error while fetching attendance data", error);
    }
  });
}
function getCurrentDate() {
  const today = new Date();
  const yyyy = today.getFullYear();
  const mm = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  const dd = String(today.getDate()).padStart(2, '0');
  return `${yyyy}-${mm}-${dd}`;
}

// const employee_name =ref('')
// const login_user = sessionUser()
// function get_employee(){
//   const employee = createResource({
//       url: 'frappe.client.get_value',
//       makeParams: () => ({
//         doctype: 'Employee',
//         filters: {
//           user_id: login_user,
//           status: 'Active'
//         },
//         fieldname: ['name', 'leave_approver']
//       }),
//       auto: true,
//       onSuccess: (res) => {
//         console.log(res)
//         get_leave_balance(res.name,getCurrentDate())
        
//       },
//       onError: (error) => {
//         console.log("Error fetching employee:", error)
//       }
//     })
// }
function get_leave_balance(date){
  const leave_balance = createResource({
    url: 'hrms.hr.doctype.leave_application.leave_application.get_leave_details',
    makeParams: () => ({
      employee: window.emp_id,
      date:date
    }),
    auto: true,
    onSuccess: (data) => {
      const rawBalances = data.leave_allocation || {}
      const lwps = data.lwps || [];
      let totalAllocated = 0;
      let utilized = 0;
      let remaining = 0;

      for (const key in rawBalances) {
        const leave = rawBalances[key];
        totalAllocated += parseFloat(leave.total_leaves || 0);
        utilized += parseFloat(leave.leaves_taken || 0);
        remaining += parseFloat(leave.remaining_leaves || 0);
      }

      leaveData.value = {
        totalAllocated: Number(totalAllocated.toFixed(2)),
        utilized: Number(utilized.toFixed(2)),
        remaining: Number(remaining.toFixed(2))
      };
    },
    onError: (error) => {
      console.error('Failed to fetch user name:', error)
    },
  })
}
function get_project(){
  const user = createResource({
    url: 'inspira.inspira.api.projects.projects_landing.get_milestone_project_data',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        projects.value = res.projects.map(project => ({
          project: project.project,
          startDate: formatDate(project.startDate,'DD-MMM-YYYY'),
          endDate: formatDate(project.endDate,'DD-MMM-YYYY'),
          daysLeft: project.daysLeft,
          milestone: project.milestone
        }));
        projectStats.value = res.stats;
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
function get_task_details(){
  const user = createResource({
    url: 'inspira.inspira.api.tasks.tasks.task_details',
      makeParams: () => ({ }),
      auto: true,
      onSuccess: (res) => {
        if (res[0] || res[0].overdue || res[0].todo) {
          tasks.value.overdue = res[0].overdue;
          tasks.value.todo = res[0].todo;
        } else {
          console.warn("Invalid task structure:", res);
        }
        if(res[1]){
          taskStatus.value = res[1]
        }
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
function gettimesheetData(){
  const user = createResource({
    url: 'inspira.inspira.api.hrms.timesheet.get_user_timesheet_compliance_monthly',
      makeParams: () => ({}),
      auto: true,
      onSuccess: (res) => {
        timesheetData.value.weeks = res
      },
      onError: (error) => {
        console.log(error)
      },
  })
}
onMounted(() => {
  if (is_employee){
    get_employee_attendance()
    get_employee_tracking_data()
    get_leave_balance(getCurrentDate())
    gettimesheetData()
  }
  
  get_project()
  get_task_details()
  
})
</script>