<template>
  <div class="flex justify-between items-center">
    <h1 class="text-2xl md:text-[29px] font-normal text-[#625B71]">
      Good {{ greeting }} {{ username }},
    </h1>
    
    <div class="border shadow-md rounded-full flex p-0.25" 
    v-if="is_manager" 
    >
      <button 
        @click="$emit('toggle-view', 'User')" 
        class="px-4 py-2 rounded-full rounded-r-none text-sm"
        :class="activeView === 'User' ? 'bg-purple-200 text-purple-800 shadow-sm' : 'text-purple-800'"
      >
        <span class="flex items-center">
          <user-icon class="w-4 h-4 mr-1" />
          User
        </span>
      </button>
      <button v-if="is_manager" 
        @click="$emit('toggle-view', 'Team')" 
        class="px-4 py-2 rounded-full rounded-l-none text-sm"
        :class="activeView === 'Team' ? 'bg-purple-200 text-purple-800 shadow-sm' : 'text-purple-800'"
      >
        <span class="flex items-center" >
          <users-icon class="w-4 h-4 mr-1" />
          Team
        </span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { User as UserIcon, Users as UsersIcon } from 'lucide-vue-next';

const props = defineProps({
  username: {
    type: String,
    required: true
  },
  activeView: {
    type: String,
    required: true
  }
});

defineEmits(['toggle-view']);
const is_manager = window.is_manager
const greeting = computed(() => {
  const hour = new Date().getHours();
  // console.log("hours details",hour);
  if (hour < 12) return 'Morning';
  if (hour < 18) return 'Afternoon';
  return 'Evening';
});
</script>