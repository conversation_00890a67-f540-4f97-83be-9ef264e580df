<template>
  <div class="w-full">
    <div class="flex items-center gap-2 mb-4">
      <span>
        <SemiHumburgerIcon />
      </span>
      <h1 class="Main_heading">{{ projectName }}</h1>
    </div>

    <div class="card w-full">
      <!-- main tabs starts here  -->
      <div class="tabs-wrapper">
        <div class="tabs">
          <button v-for="tab in mainTabs" :key="tab" @click="changeTab(tab)"
            :class="['tab', { active: activeTab === tab }]">
            {{ tab }}
          </button>
        </div>
      </div>


      <div class="tab-content-wrapper">
        <div class="tab-content pt-3 w-full">
          <Transition name="fade" mode="out-in">
            <div v-if="activeTab === 'Overview'" key="overview">
              <Coming_Soon_template />
            </div>
            <div v-else-if="activeTab === 'Contracts'" key="contracts">
              <ContractsDashboard />
            </div>

            <div v-else-if="activeTab === 'Planner'" key="planner">
              <div v-if="isLoading" class="flex justify-center items-center py-10">
                <div class="flex items-center gap-2">
                  <FeatherIcon :name="'loader'" class="h-8 w-8 text-purple-600 animate-spin" />
                  <span class="text-sm text-purple-600 ">Almost There ...</span>
                </div>
              </div>
              <div v-else>
                <div class="subtabs">
                  <button v-for="subtab in subtabs" :key="subtab.value" @click="activeSubTab = subtab.value"
                    :class="['subtab', { active: activeSubTab === subtab.value }]">
                    {{ subtab.label }}
                  </button>
                </div>

                <div class="subtab-content mt-4 w-full">
                  <Transition name="fade" mode="out-in">
                    <MileStones :activeSubTab="activeSubTab" :projectId="projectId" />
                  </Transition>
                </div>
              </div>
            </div>

            <div v-else-if="activeTab === 'Approvals'" key="approvals">
              <Coming_Soon_template />
            </div>
            <div v-else-if="activeTab === 'Materials'" key="materials">
              <Coming_Soon_template />
            </div>
            <div v-else-if="activeTab === 'Site-Updates'" key="Site-Updates">
              <SiteUpdates />
            </div>
          </Transition>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, watch, defineAsyncComponent } from 'vue'
import { createResource, FeatherIcon } from 'frappe-ui'
import { useRoute } from 'vue-router'
import SemiHumburgerIcon from './icons/SemiHumburgerIcon.vue'
import Coming_Soon_template from './Coming_Soon_template.vue'
import { sessionUser } from '../data/session'

// Lazy load components for better performance
const ContractsDashboard = defineAsyncComponent(() => import('./ContractsDashboard.vue'))
const MileStones = defineAsyncComponent(() => import('./MileStones.vue'))
const SiteUpdates = defineAsyncComponent(() => import('./SiteUpdates_Components/SiteUpdates.vue'))


const login_user = sessionUser()
const route = useRoute()
let projectName = route.params.name
let projectId = ref(route.query.id)

let activeTab = ref('Overview')

const activeSubTab = ref()

const defaultTabs = [
  'Overview',
  'Planner',
  'Approvals',
  'Materials',
  'Site-Updates',
]
const allTabs = [
  'Overview',
  'Contracts',
  'Planner',
  'Approvals',
  'Materials',
  'Site-Updates',
]
const mainTabs = ref([...defaultTabs])
let subtabs = ref([])
let isLoading = ref(false)

function changeTab(tab) {
  activeTab.value = tab
  window.location.hash = tab
  if (tab === 'Planner') {
    get_milestones()
  }
}

const get_milestones = () => {
  if (!projectId.value) return
  isLoading.value = true
  const ownerOfTaskResource = createResource({
    url: 'inspira.inspira.api.projects.project_planner.get_milestones',
    makeParams: () => ({
      project_id: projectId.value,
    }),
    auto: true,
    onSuccess: (data) => {
      console.log('data', data)
      subtabs.value = data
      if (data.length && data[0]?.value) {
        activeSubTab.value = data[0]?.value
      } else {
        activeSubTab.value = 'null'
      }
      isLoading.value = false
    },
    onError: (error) => {
      console.error('Failed to fetch user name:', error)
      isLoading.value = false
    },
  })
}
function showcontract() {
  const show_contract = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'IDP Project',
      fields: ['name', "project_name"],
      filters: [
        ['IDP Project User', 'user', '=', login_user],
        ['IDP Project User', 'view_contract', '=', 1]
      ],
    }),
    auto: true,
    onSuccess: (res) => {
      res.forEach(project => {
        if (project.name == projectId.value) {
          mainTabs.value = [...allTabs]
        }
      })

    },
    onError: (error) => {
      console.log(error)
    },
  })
}
showcontract()

watch(
  () => route.params.name,
  (newName) => {
    projectName = newName
  }
)
watch(
  () => route.query.id,
  (newId) => {
    if (newId) {
      projectId.value = newId
      get_milestones()
    }
  }
)

const updateTabFromHash = () => {
  const hash = window.location.hash.replace('#', '')
  if (mainTabs.value.includes(hash)) {
    activeTab.value = hash
    changeTab(hash)
  }
  else {
    window.location.hash = 'Overview'
  }
}

onMounted(() => {
  updateTabFromHash()
  window.addEventListener('hashchange', updateTabFromHash)
})

onUnmounted(() => {
  window.removeEventListener('hashchange', updateTabFromHash)
})

watch(() => route.query.id, (newId) => {
  projectId.value = newId
  updateTabFromHash()
}, { immediate: true })
</script>

<style scoped>
.card {
  background: white;
  border-radius: 0.375rem;
}

.tabs {
  display: flex;
  gap: 22px;
  border-bottom: 1px solid #8b8b8b;
  margin-top: 0.5rem;
}

.tab {
  padding-bottom: 5px;
  padding-left: 4px;
  color: #4d4d4d;
  font-family: Inter;
  font-weight: 500;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2 cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.tab::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 0.75px;
  background-color: #747474;
  transition: all 0.2 cubic-bezier(0.4, 0, 0.2, 1);
}

.tab:hover::after {
  width: 100%;
  left: 0;
}

.tab.active {
  color: #4d4d4d;
  font-weight: 600;
  border-bottom: 1px solid #747474;
}

.subtabs {
  display: flex;
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  color: var(--Neutral-D_Grey, #4d4d4d);
  border: 0.4px solid #8b8686;
  border-radius: 5.6px;
  font-family: Inter;
  font-size: 11px;
  font-style: normal;
  font-weight: 600;
  line-height: 19.2px;
}

.subtab {
  padding: 0.25rem 1.25rem;
  border: none;
  color: #4d4d4d;
  cursor: pointer;
  border-right: 0.4px solid #8b8686;
}

.subtab.active {
  background: #e8e5ff;
  position: relative;
  z-index: 1;
}

.subtab.active:first-child {
  border-top-left-radius: 5.6px;
  border-bottom-left-radius: 5.6px;
}

.subtab.active:last-child {
  border-top-right-radius: 5.6px;
  border-bottom-right-radius: 5.6px;
}

.fade-enter-active {
  transition: all 0.2 cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.fade-enter-from {
  opacity: 0;
  transform: translateY(100px);
}

.fade-leave-to {
  opacity: 0;
  transform: translateY(-100px);
}

.transform {
  transition: transform 0.3s ease-in-out;
}

.rotate-this {
  transform: rotate(270deg);
}

.Main_heading {
  font-family: 'Roboto';
  font-size: 22px;
}

.overflow-y-auto {
  scrollbar-width: thin;
}
</style>
