<template>
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#paint0_diamond_2048_1237_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0 0.012 -0.012 0 12 12.61)"><rect x="0" y="0" width="1039.05" height="1039.05" fill="url(#paint0_diamond_2048_1237)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1039.05" height="1039.05" transform="scale(1 -1)" fill="url(#paint0_diamond_2048_1237)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1039.05" height="1039.05" transform="scale(-1 1)" fill="url(#paint0_diamond_2048_1237)" opacity="1" shape-rendering="crispEdges"/><rect x="0" y="0" width="1039.05" height="1039.05" transform="scale(-1)" fill="url(#paint0_diamond_2048_1237)" opacity="1" shape-rendering="crispEdges"/></g></g><rect width="24" height="24" transform="translate(0 0.609985)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_DIAMOND&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.50677084922790527,&#34;g&#34;:0.17317707836627960,&#34;b&#34;:0.72916668653488159,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.86080729961395264,&#34;g&#34;:0.51289767026901245,&#34;b&#34;:0.51289767026901245,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.50677084922790527,&#34;g&#34;:0.17317707836627960,&#34;b&#34;:0.72916668653488159,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.86080729961395264,&#34;g&#34;:0.51289767026901245,&#34;b&#34;:0.51289767026901245,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:1.4695762231022014e-15,&#34;m01&#34;:-24.0,&#34;m02&#34;:24.0,&#34;m10&#34;:24.0,&#34;m11&#34;:1.4695762231022014e-15,&#34;m12&#34;:0.60998535156250},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<defs>
<clipPath id="paint0_diamond_2048_1237_clip_path"><rect width="24" height="24" transform="translate(0 0.609985)"/></clipPath><linearGradient id="paint0_diamond_2048_1237" x1="0" y1="0" x2="500" y2="500" gradientUnits="userSpaceOnUse">
<stop stop-color="#812CBA"/>
<stop offset="1" stop-color="#DC8383"/>
</linearGradient>
</defs>
</svg>

</template>