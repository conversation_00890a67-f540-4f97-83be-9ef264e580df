<template>
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#paint0_angular_2048_1189_clip_path)" data-figma-skip-parse="true"><g transform="matrix(0 0.012 -0.012 0 12 12.01)"><foreignObject x="-1039.05" y="-1039.05" width="2078.11" height="2078.11"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(75, 192, 226, 1) 0deg,rgba(219, 130, 130, 1) 178.2deg,rgba(64, 197, 233, 1) 345.6deg,rgba(75, 192, 226, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><rect width="24" height="24" transform="translate(0 0.00999451)" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.86080729961395264,&#34;g&#34;:0.51289767026901245,&#34;b&#34;:0.51289767026901245,&#34;a&#34;:1.0},&#34;position&#34;:0.49500000476837158},{&#34;color&#34;:{&#34;r&#34;:0.25208333134651184,&#34;g&#34;:0.77267360687255859,&#34;b&#34;:0.91666668653488159,&#34;a&#34;:1.0},&#34;position&#34;:0.95999997854232788}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.86080729961395264,&#34;g&#34;:0.51289767026901245,&#34;b&#34;:0.51289767026901245,&#34;a&#34;:1.0},&#34;position&#34;:0.49500000476837158},{&#34;color&#34;:{&#34;r&#34;:0.25208333134651184,&#34;g&#34;:0.77267360687255859,&#34;b&#34;:0.91666668653488159,&#34;a&#34;:1.0},&#34;position&#34;:0.95999997854232788}],&#34;transform&#34;:{&#34;m00&#34;:1.4695762231022014e-15,&#34;m01&#34;:-24.0,&#34;m02&#34;:24.0,&#34;m10&#34;:24.0,&#34;m11&#34;:1.4695762231022014e-15,&#34;m12&#34;:0.00999450683593750},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<defs>
<clipPath id="paint0_angular_2048_1189_clip_path"><rect width="24" height="24" transform="translate(0 0.00999451)"/></clipPath></defs>
</svg>

</template>