<template>
  <div v-if="!activeEmployee" class="p-4 flex flex-col">

    <div class="w-6/12 flex items-center gap-2">
      <WeekRangePicker v-model="selectedWeekRange" @update:modelValue="handleWeekRangeChange" />
      <button @click="selectCurrentWeek"
        class="text-sm px-3 py-1.5 bg-white border rounded text-[#65558F] font-medium shadow hover:bg-gray-50">
        This Week
      </button>
    </div>

    <!-- Team Timesheet Table -->
    <div v-if="selectedWeekRange.start && selectedWeekRange.end"
      class="w-full mt-6 border-2 rounded-lg overflow-x-auto">
      <table class="w-full min-w-[1000px] border-collapse">
        <!-- Table Header -->
        <thead>
          <tr class="border-b">
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              People
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Days <br> Present
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Total <br> Hours
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Hours <br> Filled
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Compliance
            </th>
            <!-- Days of the week -->
            <template v-for="(day, index) in weekDays" :key="index">
              <th class="p-1 border-r text-sm text-center font-medium text-gray-600">
                <div class="flex flex-col justify-between gap-1">
                  <div>{{ day.dayName }}</div>
                  <div class="text-xs text-gray-500">
                    {{ day.date }}
                  </div>
                  <div class="text-xs text-gray-500 mb-1">
                    {{ calculateDayTotal(index) }}
                  </div>
                  <div>
                    <hr :class="getDayStatusClass(index)" class="h-0.5 rounded" />
                  </div>
                </div>
              </th>
            </template>
            <th class="p-3 border-r text-sm text-center font-medium text-gray-600">
              Total
            </th>
            <th class="p-3 text-sm text-center font-medium text-gray-600">
              Status
            </th>
          </tr>
        </thead>

        <!-- Table Body -->
        <tbody>
          <tr v-if="teamTimesheets.length === 0">
            <td :colspan="7 + weekDays.length" class="text-center  text-gray-500 py-6">
              No data available
            </td>
          </tr>

          <tr v-for="(employee, index) in teamTimesheets" :key="index" class="border-b hover:bg-gray-50"
            :class="{ 'bg-gray-50': index % 2 === 0 }">

            <!-- Employee Name -->
            <td class="p-3 border-r w-64">
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                  <Generic_Icon class="w-8 h-8 rounded-full bg-purple-100 text-purple-600" />
                  <span class="text-sm text-gray-700">{{ employee.name }}</span>
                </div>
                <div>
                  <button @click="openEmployeeTimesheet(employee)"
                    class="flex items-center gap-0.5 text-sm text-gray-700 bg-gray-200 p-1 rounded-sm">
                    Open
                    <span class="text-xs">→</span>
                  </button>
                </div>
              </div>
            </td>

            <!-- Days Present -->
            <td class="p-3 border-r text-sm text-center">
              {{ employee.daysPresent }}
            </td>

            <!-- Total Hours -->
            <td class="p-3 border-r text-sm text-center">
              {{ employee.totalHours }}
            </td>

            <!-- Hours Filled -->
            <td class="p-3 border-r text-sm text-center">
              {{ employee.hoursFilled }}
            </td>

            <!-- Compliance -->
            <td class="p-3 border-r text-sm text-center">
              <!-- <span :class="getComplianceClass(employee.compliance)"> -->
              {{ employee.compliance }}%
              <!-- </span> -->
            </td>

            <!-- Daily Hours -->
            <td v-for="(day, dayIndex) in weekDays" :key="dayIndex" class="p-3 border-r text-sm text-center">
              {{ employee.dailyHours[dayIndex] || '–' }}
            </td>

            <!-- Total -->
            <td class="p-3 border-r text-sm text-center">
              {{ employee.weekTotal }}
            </td>
            <td class="p-3 text-sm text-center">
              <div v-if="employee.workflow_state === 'Approved'" class="text-green-500">
                Approved
              </div>
              <div v-else-if="employee.workflow_state === 'Rejected'" class="text-red-500">
                Rejected
              </div>
              <div v-else class="flex justify-center">
                <button @click="approveTimesheet(employee)"
                  class="px-1 py-1 text-xs text-[#83AA97] rounded hover:bg-green-100">
                  <ApproveIcon />
                </button>
                <button @click="showRejectDialog(employee)"
                  class="px-1 py-1 text-xs text-red-700 rounded hover:bg-red-100">
                  <RejectIcon />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <!-- Empty State -->
    <div v-else
      class="flex flex-col items-center shadow-2xl rounded-md p-12 w-100 h-auto cursor-pointer mb-12 mt-12 text-gray-500">
      <FeatherIcon :name="'clock'" class="h-16 w-12" /> <br />
      <p>Please select a week range to view team timesheets</p>
    </div>

    <!-- Rejection Dialog -->
    <Dialog v-model="showRejectionDialog">
      <template #body-title>
        <h3>Reject Timesheet</h3>
      </template>
      <template #body-content>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Reason for rejecting {{ currentRejectingEmployee?.name }}'s timesheet
          </label>
          <textarea v-model="rejectionReason" class="w-full border rounded p-2 text-sm" rows="3"
            placeholder="Enter reason for rejection..."></textarea>
        </div>
      </template>
      <template #actions>
        <Button variant="solid" @click="rejectTimesheet">
          Submit Rejection
        </Button>
        <Button class="ml-2" @click="showRejectionDialog = false">
          Cancel
        </Button>
      </template>
    </Dialog>
  </div>

  <!-- Timesheet Detail View -->
  <div v-if="activeEmployee">
    <TimesheetDetailView :employee="activeEmployee" :weekRange="selectedWeekRange" :weekDays="weekDays"
      @close="closeEmployeeTimesheet" @update:employee="activeEmployee = $event" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { FeatherIcon, toast, Dialog, Button, createResource } from 'frappe-ui'
import WeekRangePicker from './WeekRangePicker.vue'
import TimesheetDetailView from './TimesheetDetailView.vue'
import { formatDate } from '../utils/format'
import OwnerIcon from './icons/OwnerIcon.vue'
import Generic_Icon from './icons/Generic_Icon.vue'
import ApproveIcon from './icons/ApproveIcon.vue'
import RejectIcon from './icons/RejectIcon.vue'

import {
  frappeSetValue,
  frappeApplyWorkflow,
} from '../utils/frappeAPI'

// State variables
const selectedWeekRange = ref({ start: null, end: null })
const teamTimesheets = ref([])
const weekDays = ref([])
const activeEmployee = ref(null)

// Methods
const handleWeekRangeChange = () => {
  updateWeekDays()
  fetchTeamTimesheets()
}

const updateWeekDays = () => {
  if (!selectedWeekRange.value.start || !selectedWeekRange.value.end) return

  const days = []
  const startDate = new Date(selectedWeekRange.value.start)
  const endDate = new Date(selectedWeekRange.value.end)

  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    const dayName = currentDate.toLocaleDateString('en-US', {
      weekday: 'long',
    })
    const date = currentDate.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
    })

    days.push({
      dayName: dayName,
      date: date,
      fullDate: formatDate(currentDate, 'YYYY-MM-DD'),
      hours: '0h',
    })

    currentDate.setDate(currentDate.getDate() + 1)
  }

  weekDays.value = days
}

const fetchTeamTimesheets = async () => {
  try {
    const teamTimesheetsResource = createResource({
      url: 'inspira.inspira.api.hrms.timesheet.get_team_timesheets',
      makeParams: () => ({
        start_date: formatDate(selectedWeekRange.value.start, 'YYYY-MM-DD'),
        end_date: formatDate(selectedWeekRange.value.end, 'YYYY-MM-DD'),
      }),
      auto: true,
      onSuccess: (data) => {
        console.log(data)
        teamTimesheets.value = data;
      },
      onError: (error) => {
        console.error('Failed to fetch tasks:', error);
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to fetch tasks',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        });
      },
    })
    //   await new Promise(resolve => setTimeout(resolve, 500))

    //   teamTimesheets.value = [
    //     {
    //       id: 1,
    //       name: 'Abhas Chowdhry',
    //       daysPresent: 5,
    //       totalHours: 120,
    //       hoursFilled: 108,
    //       compliance: 90,
    //       dailyHours: ['1h 1m', '–', '–', '–', '–', '–', '–'],
    //       weekTotal: '6h 6m',
    //       status: ''
    //     }
    //   ]
  } catch (error) {
    console.error('Error fetching team timesheets:', error)
    toast({
      title: 'Error',
      text: 'Failed to fetch team timesheets',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
  }
}

// Calculate total hours for each day across all employees
const calculateDayTotal = (dayIndex) => {
  let totalMinutes = 0
  for (const employee of teamTimesheets.value) {
    totalMinutes += parseTime(employee.dailyHours[dayIndex])
  }
  return formatTime(totalMinutes)
}

// Parse time string to minutes
const parseTime = (timeStr) => {
  if (!timeStr || timeStr === '–') return 0

  let hours = 0
  let minutes = 0

  if (timeStr.includes('h')) {
    const parts = timeStr.split('h')
    hours = parseInt(parts[0]) || 0

    if (parts[1] && parts[1].includes('m')) {
      minutes = parseInt(parts[1].replace('m', '').trim()) || 0
    }
  } else if (timeStr.includes(':')) {
    const [h, m] = timeStr.split(':')
    hours = parseInt(h) || 0
    minutes = parseInt(m) || 0
  }

  return hours * 60 + minutes
}

// Format minutes to time string
const formatTime = (minutes) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h ${mins}m`
}

const getInitials = (name) => {
  if (!name) return ''
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
}

const getComplianceClass = (compliance) => {
  if (compliance >= 90) return 'text-green-600'
  if (compliance >= 80) return 'text-yellow-600'
  return 'text-red-600'
}

// Get CSS class for day status indicator based on total hours
const getDayStatusClass = (dayIndex) => {
  let totalMinutes = 0
  for (const employee of teamTimesheets.value) {
    totalMinutes += parseTime(employee.dailyHours[dayIndex])
  }
  const totalHours = totalMinutes / 60

  if (totalHours === 0) return 'bg-red-500'
  if (totalHours < 8) return 'bg-yellow-500'
  return 'bg-green-500'
}

const openEmployeeTimesheet = (employee) => {
  activeEmployee.value = employee
}

const closeEmployeeTimesheet = () => {
  activeEmployee.value = null
}

const getCurrentWeekRange = () => {
  const today = new Date()
  const dayOfWeek = today.getDay()
  const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)
  const startDate = new Date(today.setDate(diff))
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + 6)

  return {
    start: startDate,
    end: endDate
  }
}
// Initialize component - don't auto-select current date
onMounted(() => {
  const currentWeek = getCurrentWeekRange()
  const newStart = currentWeek.start.toDateString()
  const currentStart = selectedWeekRange.value.start?.toDateString() || ''

  if (newStart !== currentStart) {
    selectedWeekRange.value = {
      start: new Date(currentWeek.start),
      end: new Date(currentWeek.end)
    }
  }
})

const selectCurrentWeek = () => {
  const currentWeek = getCurrentWeekRange()
  const newStart = currentWeek.start.toDateString()
  const currentStart = selectedWeekRange.value.start?.toDateString() || ''

  if (newStart !== currentStart) {
    selectedWeekRange.value = {
      start: new Date(currentWeek.start),
      end: new Date(currentWeek.end)
    }
  }
}

// State for rejection dialog
const showRejectionDialog = ref(false)
const rejectionReason = ref('')
const currentRejectingEmployee = ref(null)

// Methods
const approveTimesheet = (employee) => {
  const successFunc = (data) => {
    employee.status = data?.workflow_state
    const employeeIndex = teamTimesheets.value.findIndex(
      emp => emp.timesheet_id === employee.timesheet_id
    );

    if (employeeIndex !== -1) {
      teamTimesheets.value[employeeIndex].workflow_state = data?.workflow_state;
    }
  }

  const applyWorkflowParams = {
    docname: employee.timesheet_id,
    doctype: 'IDP Timesheet',
    action: 'Approve',
  }

  const successMsg = `Timesheet for ${employee.name} approved`
  const errorMsg = 'Error while approving timesheet'
  frappeApplyWorkflow({
    applyWorkflowParams: applyWorkflowParams,
    successFunc: successFunc,
    successMsg: successMsg,
    errorMsg: errorMsg,
  })
}

const showRejectDialog = (employee) => {
  currentRejectingEmployee.value = employee
  rejectionReason.value = ''
  showRejectionDialog.value = true
}

const rejectTimesheet = () => {
  if (!rejectionReason.value.trim()) {
    toast({
      title: 'Error',
      text: 'Please enter a rejection reason',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
    return
  }

  const applyWorkflowParams = {
    docname: currentRejectingEmployee.value.timesheet_id,
    doctype: 'IDP Timesheet',
    action: 'Reject',
  }

  const frappeApplyWorkflowSuccessFunc = (data) => {
    currentRejectingEmployee.value.status = data?.workflow_state
    console.log("currentRejectingEmployee.value ", currentRejectingEmployee.value)
    const employeeIndex = teamTimesheets.value.findIndex(
      emp => emp.timesheet_id === currentRejectingEmployee.value.timesheet_id
    );

    if (employeeIndex !== -1) {
      teamTimesheets.value[employeeIndex].workflow_state = data?.workflow_state || 'Rejected';
    }
  }
  const successMsg = `Timesheet for ${currentRejectingEmployee.value.name} rejected`
  const errorMsg = 'Error while rejecting timesheet'

  const rejectionObj = {
    "rejection_reason": rejectionReason.value.trim(),
    "rejected_by": window.user_name
  }

  const setValueParams = {
    name: currentRejectingEmployee.value.timesheet_id,
    doctype: 'IDP Timesheet',
    fieldname: rejectionObj,
    // value: rejectionReason.value.trim(),
  }

  const frappeSetValueSuccessFunc = async (data) => {
    frappeApplyWorkflow({
      applyWorkflowParams: applyWorkflowParams,
      successFunc: frappeApplyWorkflowSuccessFunc,
      successMsg: successMsg,
      errorMsg: errorMsg,
    })
    showRejectionDialog.value = false
  }

  frappeSetValue({
    setValueParams: setValueParams,
    successFunc: frappeSetValueSuccessFunc,
  })
}
</script>

<!-- <style scoped>
td:first-child,
th:first-child {
  width: 200px;
}

td:nth-child(2),
th:nth-child(2) {
  width: 150px;
}

td:last-child,
th:last-child {
  width: 40px;
}

td:first-child,
td:nth-child(2) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


table {
  table-layout: fixed;
  width: 100%;
}
</style> -->