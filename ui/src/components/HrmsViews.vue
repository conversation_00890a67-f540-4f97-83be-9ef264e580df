<template>
  <div class="w-full">
    <div class="bg-white rounded-md w-full">
      <!-- Top Tabs -->
      <div class="border-b border-gray-500">
        <div class="flex overflow-x-auto hide-scrollbar">
          <button v-for="tab in topTabs" :key="tab" @click="changeTopTab(tab)"
            class="pb-1 px-3 text-gray-700 font-medium relative transition-all" :class="[
              activeTopTab === tab
                ? 'font-semibold border-b border-gray-600'
                : 'hover:text-gray-900'
            ]">
            {{ tab }}
          </button>
        </div>
      </div>

      <!-- Tab Content for Top Tabs -->
      <div class="w-full">
        <Transition name="slide" mode="out-in">

          <div v-if="activeTopTab === 'Attendance'" key="attendance">
            <AttendenceManagement />
          </div>

          <!-- Timesheets Tab Content -->
          <div v-else-if="activeTopTab === 'Timesheets'" key="timesheets" class="w-full">
            <!-- Main Tabs (Sub-tabs for Timesheets) -->
            <div class="flex gap-5 border-b border-gray-300 mt-3 px-4 items-center">
              <div class="flex gap-2 items-center">
                <ClockIcon />
                <h1 class="text-[#434343] text-[17px] font-normal font-roboto leading-none">Timesheets</h1>
              </div>
              <button v-for="tab in mainTabs" :key="tab" @click="activeMainTab = tab"
                class="pb-1 px-1 text-[#79747E] text-[14px] font-[600] relative transition-all" :class="[
                  activeMainTab === tab
                    ? 'font-medium border-b border-gray-600'
                    : 'hover:text-gray-900'
                ]">
                {{ tab }}
              </button>
            </div>

            <!-- Sub-Tab Content for Timesheets -->
            <div class="pt-2 w-full">
              <Transition name="slide" mode="out-in">
                <div v-if="activeMainTab === 'User'" key="my-timesheet">
                  <MyTimesheet />
                </div>
                <div v-else-if="activeMainTab === 'Team'" key="team-timesheet">
                  <TeamTimesheet />
                </div>
              </Transition>
            </div>
          </div>
          <div v-else-if="activeTopTab === 'Leave'" key="leave" class="w-full">
            <LeaveManagement />
          </div>
          <div v-else-if="activeTopTab === 'Salary-slip'" key="Salary-slip">
            <SalarySilips />
          </div>
          <div v-else-if="activeTopTab === 'Expenses'" key="expenses">
             <ExpensesManagement />
          </div>
          <div v-else-if="activeTopTab === 'Recruitment'" key="Recruitment">
             <RecruitmentManagement />
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import MyTimesheet from './MyTimesheet.vue';
import TeamTimesheet from './TeamTimesheet.vue';
import ClockIcon from './icons/ClockIcon.vue';
import LeaveManagement from './LeaveManagement.vue';
import AttendenceManagement from './AttendenceManagement.vue';
import SalarySilips from './SalarySilips.vue'
import ExpensesManagement from './ExpensesManagement.vue'
import RecruitmentManagement from './RecruitmentManagement.vue'
import Coming_Soon_template from './Coming_Soon_template.vue';

const is_manager = window.is_manager;
const route = useRoute();
const router = useRouter();

const topTabs = [
  'Attendance',
  'Timesheets',
  'Leave',
  'Salary-slip',
  'Expenses',
  'Recruitment'
];

const mainTabs = is_manager ? ['User', 'Team'] : ['User'];
const activeTopTab = ref('Attendance');
const activeMainTab = ref(localStorage.getItem('activeMainTab') || 'User');

onMounted(() => {
  updateTabFromHash();
});

watch(() => route.hash, (newHash) => {
  updateTabFromHash();
});

watch(activeMainTab, (newVal) => {
  localStorage.setItem('activeMainTab', newVal);
});

const changeTopTab = (tab) => {
  activeTopTab.value = tab;
  router.push({ hash: `#${tab}` });
};

const updateTabFromHash = () => {
  const hash = route.hash.replace('#', '');
  if (topTabs.includes(hash)) {
    activeTopTab.value = hash;
  } else {
    activeTopTab.value = 'Attendance';
    router.replace({ hash: '#Attendance' });
  }
};
</script>

<style scoped>
.slide-enter-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-100px);
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>