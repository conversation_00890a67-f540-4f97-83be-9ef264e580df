<template>
  <div :class="['bg-color text-gray-900 p-5 h-full transition-transform', isOpen ? 'w-44' : 'w-14',]">
    <div>
      <nav>
        <ul class="space-y-4">
          <li class="flex items-center">
            <router-link to="/Dashboard" class="flex items-center">
              <Home />
              <span :class="[
                isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
              ]" class="whitespace-nowrap">Home</span>
            </router-link>
          </li>

          <li class="flex flex-col " @mouseenter="showProjectDropdown = true" @mouseleave="showProjectDropdown = false">
            <div @click="toggleProjectSubmenu" class="flex items-center cursor-pointer">
              <div class="flex items-center justify-center">
                <Project v-if="!showProjectDropdown" />
                <Dropdown v-else />
              </div>
              <router-link to="/Projects">
                <span :class="[
                  isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
                ]" class="whitespace-nowrap">Projects</span>
              </router-link>
            </div>
            <ul v-if="isProjectSubmenuOpen && isOpen" class="pl-8 mt-2">
              <li v-for="project in projectLists" :key="project.id">
                <router-link :to="{
                  path: `/ProjectView/${project.name}`,
                  query: { id: project.id },
                }" class="flex items-center justify-between gap-1 w-28">
                  <ProjectList class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">{{
                    project.name
                  }}</a>
                </router-link>
              </li>
            </ul>
          </li>

          <li class="flex flex-col" @mouseenter="showHRMSDropdown = true" @mouseleave="showHRMSDropdown = false"
            v-if="is_employee">
            <div @click="toggleHRMSSubmenu" class="flex items-center cursor-pointer">
              <div class="flex items-center justify-center">
                <HRMS />
                <!-- <HRMS v-if="!showHRMSDropdown" />
                <Dropdown v-else /> -->
              </div>
              <router-link to="/hrms" class="flex items-center">
                <span :class="[
                  isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
                ]" class="whitespace-nowrap">HRMS</span>
              </router-link>
            </div>
            <ul v-if="isHRMSSubmenuOpen && isOpen" class="pl-8 mt-2">
              <li>
                <router-link to="/hrms#Attendance" class="flex items-center justify-between gap-1 w-28">
                  <AttendenceIcon class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">Attendance</a>
                </router-link>
              </li>
              <li>
                <router-link to="/hrms#Timesheets" class="flex items-center justify-between gap-1 w-28">
                  <ClockIcon class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">Timesheets</a>
                </router-link>
              </li>
              <li>
                <router-link to="/hrms#Leave" class="flex items-center justify-between gap-1 w-28">
                  <LeaveIcons class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">Leave</a>
                </router-link>
              </li>
              <li>
                <router-link to="/hrms#Salary-slip" class="flex items-center justify-between gap-1 w-28">
                  <SalaryIcon class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">Salary-slip</a>
                </router-link>
              </li>
              <li>
                <router-link to="/hrms#Expenses" class="flex items-center justify-between gap-1 w-28">
                  <ExpensesIcon class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">Expenses</a>
                </router-link>
              </li>
              <li>
                <router-link to="/hrms#Recruitment" class="flex items-center justify-between gap-1 w-28">
                  <RecruitmentMainIcon class="w-[15%] min-w-[15%]" />
                  <a class="block py-1 text-xs hover:bg-gray-100 w-[85%] truncate">Recruitment</a>
                </router-link>
              </li>
            </ul>


          </li>


          <li class="flex items-center ">
            <router-link to="/My_Tasks" class="flex items-center">
              <Tasks />
              <span :class="[
                isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
              ]" class="whitespace-nowrap">Tasks</span>
            </router-link>
          </li>

          <li class="flex items-center ">
            <a :href="url" class="flex items-center">
              <CRM />
              <span :class="[
                isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
              ]" class="whitespace-nowrap">CRM</span>
            </a>
          </li>

          <li class="flex items-center ">
            <router-link to="/Primary_Dashboards" class="flex items-center">
              <Dashboard />
              <span :class="[
                isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
              ]" class="whitespace-nowrap">Dashboard</span>
            </router-link>
          </li>

          <li class="flex items-center ">
            <router-link to="" class="flex items-center">
              <Accounts />
              <span :class="[
                isOpen ? 'ml-3 opacity-100 transition-opacity duration-200' : 'ml-0 opacity-0 transition-none',
              ]" class="whitespace-nowrap">Accounts</span>
            </router-link>
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { defineProps, defineEmits } from 'vue'
import { RouterLink } from 'vue-router'
import { createListResource } from 'frappe-ui'
import Home from '../components/icons/Sidebar_Icons/Home.vue'
import Project from '../components/icons/Sidebar_Icons/Project.vue'
import Dropdown from './icons/Sidebar_Icons/Dropdown.vue'
import ProjectList from './icons/Sidebar_Icons/ProjectList.vue'
import HRMS from './icons/Sidebar_Icons/HRMS.vue'
import Tasks from './icons/Sidebar_Icons/Tasks.vue'
import CRM from './icons/Sidebar_Icons/CRM.vue'
import Dashboard from './icons/Sidebar_Icons/Dashboard.vue'
import Accounts from './icons/Sidebar_Icons/Accounts.vue'
import ClockIcon from './icons/TimeSheetSideIcon.vue';
import AttendenceIcon from "./icons/AttendanceSideIcon.vue";
import LeaveIcons from './icons/LeaveSideIcon.vue';
import SalaryIcon from './icons/Salary-slipIcon.vue';
import ExpensesIcon from './icons/ExpensesIcon.vue';
import RecruitmentMainIcon from './icons/RecruitmentMainIcon.vue';


const is_employee = window.emp_id
const url = window.site_url + '/crm/';
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['toggleSidebar'])

const showProjectDropdown = ref(false)
const isProjectSubmenuOpen = ref(false)

const showHRMSDropdown = ref(false)
const isHRMSSubmenuOpen = ref(false)

const showDashboardDropdown = ref(false)
const isDashboardSubmenuOpen = ref(false)

const toggleProjectSubmenu = () => {
  isProjectSubmenuOpen.value = !isProjectSubmenuOpen.value
}

const toggleHRMSSubmenu = () => {
  isHRMSSubmenuOpen.value = !isHRMSSubmenuOpen.value
}

const toggleDashboardSubmenu = () => {
  isDashboardSubmenuOpen.value = !isDashboardSubmenuOpen.value
}


const projectLists = ref([])

let fetchProjects = createListResource({
  doctype: 'IDP Project',
  fields: ['name as id', 'project_name as name'],
  orderBy: 'creation desc',
  filters: [["IDP Project", "contract", "is", "set"]],
  start: 0,
  pageLength: 5,
  onSuccess(data) {
    console.log('project list', data)
    projectLists.value = data
  },
})
fetchProjects.fetch()
</script>

<style scoped>
.bg-color {
  background-color: #f8f7f9;
}

.transition-transform {
  transition: width 0.3s ease, opacity 0.3s ease;
}
</style>