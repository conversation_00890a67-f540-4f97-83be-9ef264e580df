<template>
  <nav class="text-gray-700 text-sm ml-4">
    <ol class="flex items-center space-x-2">
      <li v-for="(crumb, index) in breadcrumbs" :key="index" class="flex items-center">
        <router-link v-if="crumb.path && index !== breadcrumbs.length - 1" :to="crumb.path"
          class="hover:text-gray-700 transition-colors">
          {{ crumb.name }}
        </router-link>
        <span v-else class="text-gray-700">
          {{ crumb.name }}
        </span>
        <span v-if="index < breadcrumbs.length - 1" class="mx-1 text-gray-700">
          >
        </span>
      </li>
    </ol>
  </nav>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const breadcrumbs = ref([])

const generateBreadcrumbs = () => {
  const paths = route.path.split('/')
  let currentPath = ''

  breadcrumbs.value = paths.map(path => {
    currentPath += path ? `/${path}` : ''
    return {
      name: path || 'Home',
      path: currentPath
    }
  }).filter(crumb => crumb.name)

  if (route.path === '/') {
    breadcrumbs.value = [{ name: 'Home >', path: '/' }]
  }
}

watch(() => route.path, generateBreadcrumbs, { immediate: true })
</script>
<style scoped>
ol .flex{
  font-family: "Roboto";
  font-size: 11px;
  font-style: normal;
  font-weight: 500;

}
</style>


<!-- <template>
  <nav class="flex px-4 py-2" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1">
      <li class="inline-flex items-center">
        <router-link 
          to="/" 
          class="text-gray-600 hover:text-gray-600 text-sm font-medium"
        >
          Home
        </router-link>
      </li>
      <li 
        v-for="(crumb, index) in breadcrumbList" 
        :key="index" 
        class="inline-flex items-center"
      >
        <svg 
          class="w-4 h-4 text-gray-700 mx-1" 
          fill="currentColor" 
          viewBox="0 0 20 20"
        >
          <path 
            fill-rule="evenodd" 
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" 
            clip-rule="evenodd"
          />
        </svg>
        <router-link 
          v-if="index !== breadcrumbList.length - 1"
          :to="crumb.path"
          class="text-gray-600 hover:text-gray-600 text-sm font-medium"
        >
          {{ formatBreadcrumb(crumb.name) }}
        </router-link>
        <span 
          v-else 
          class="text-gray-800 text-sm font-medium"
        >
          {{ formatBreadcrumb(crumb.name) }}
        </span>
      </li>
    </ol>
  </nav>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const breadcrumbList = computed(() => {
  const paths = route.path.split('/').filter(Boolean)
  return paths.map((path, index) => {
    return {
      name: path,
      path: '/' + paths.slice(0, index + 1).join('/')
    }
  })
})

const formatBreadcrumb = (text) => {
  if (!text) return ''
  return text
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
</script> -->