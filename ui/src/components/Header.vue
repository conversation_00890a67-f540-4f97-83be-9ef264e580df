<template>
    <header class="bg-color text-gray-700 p-5 flex justify-between items-center w-full h-16">
        <div class="flex items-center gap-16">
            <div class="flex items-center gap-2">
                <button @click="$emit('toggleSidebar')" class="focus:outline-none">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                        class="w-6 h-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
                <h1
                    style="color: var(--Schemes-On-Surface-Variant, #49454F);font-family: Roboto;font-size: 16px;font-style: normal;font-weight: 600;line-height: normal;">
                    Inspira
                </h1>
            </div>
            <!-- <div>
                <Breadcrumb />
            </div> -->
        </div>

        <div class="flex items-center gap-8">

            <!-- <div class="relative w-[22rem]">
                <input type="text"
                    class="h-9 w-full pl-12 rounded-[6px] border border-gray-300 focus:outline-none focus:ring-1 focus:ring-gray-400 text-sm">
                <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-700"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div> -->

            <!-- <button>
                <svg width="25" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g id="Icon">
                        <path id="icon"
                            d="M4 19.37V17.37H6V10.37C6 8.98666 6.41667 7.76166 7.25 6.69499C8.08333 5.61166 9.16667 4.90333 10.5 4.56999V3.87C10.5 3.45333 10.6417 3.10333 10.925 2.81999C11.225 2.51999 11.5833 2.37 12 2.37C12.4167 2.37 12.7667 2.51999 13.05 2.81999C13.35 3.10333 13.5 3.45333 13.5 3.87V4.56999C14.8333 4.90333 15.9167 5.61166 16.75 6.69499C17.5833 7.76166 18 8.98666 18 10.37V17.37H20V19.37H4ZM12 22.37C11.45 22.37 10.975 22.1783 10.575 21.795C10.1917 21.395 10 20.92 10 20.37H14C14 20.92 13.8 21.395 13.4 21.795C13.0167 22.1783 12.55 22.37 12 22.37ZM8 17.37H16V10.37C16 9.26999 15.6083 8.32833 14.825 7.54499C14.0417 6.76166 13.1 6.36999 12 6.36999C10.9 6.36999 9.95833 6.76166 9.175 7.54499C8.39167 8.32833 8 9.26999 8 10.37V17.37Z"
                            fill="#49454F" />
                    </g>
                </svg>
            </button> -->

            <Avatar :shape="'circle'" :image="session.userImage"
                :label="session.user ? session.user.charAt(0).toUpperCase() : 'U'" size="2xl"
                class="border border-gray-700 rounded-full cursor-pointer" @click="toggleMenu" />
        </div>

        <Transition enter-active-class="transition duration-200 ease-out"
            enter-from-class="transform scale-95 opacity-0" enter-to-class="transform scale-100 opacity-100"
            leave-active-class="transition duration-200 ease-in" leave-from-class="transform scale-100 opacity-100"
            leave-to-class="transform scale-95 opacity-0">
            <div v-if="showMenu" @click.stop
                class="toggle-menu absolute top-16 right-2 bg-gray-100 rounded-md shadow-md p-4 z-1000">
                <div class="flex flex-col items-center gap-3">
                    <span class="text-sm text-center">
                        Hey, {{ session.user }}
                    </span>
                    <button @click="handleLogout" class="text-red-400 hover:text-red-500 transition-colors duration-200"
                        aria-label="Logout">
                        <i class="fa-solid fa-right-from-bracket"></i> Logout
                    </button>
                </div>
            </div>
        </Transition>
    </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { session } from '../data/session'
import { Button, Avatar, createResource } from 'frappe-ui'
import Breadcrumb from '../components/Breadcrumb.vue'

const showMenu = ref(false)

const toggleMenu = (e) => {
    e.stopPropagation()
    showMenu.value = !showMenu.value
}

const closeMenu = (e) => {
    showMenu.value = false
}

onMounted(() => {
    document.addEventListener('click', closeMenu)
})

onUnmounted(() => {
    document.removeEventListener('click', closeMenu)
})

// const handleLogout = () => {
//     localStorage.clear()
//     sessionStorage.clear()
//     session.logout.submit()
// }
const handleLogout = () => {
    localStorage.clear()
    sessionStorage.clear()
    session.logout.submit()

    delete window.emp_id
    delete window.user_name
    delete window.designation
    delete window.timezone
    delete window.is_manager
    delete window.csrf_token
    delete window.boot
}
</script>

<style scoped>
.bg-color {
    background-color: #B7B6DB;
    font-family: "Roboto";
}

::v-deep .text-ink-gray-5 {
    color: #4F378A;
    background-color: #e0daf0;
    border: 1px solid #4F378A;
    font-family: "Roboto";
    font-size: 16px;
}

.toggle-menu {
    z-index: 9999 !important;
}
</style>