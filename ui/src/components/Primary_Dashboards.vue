<template>
  <div class="w-full">
    <div class="bg-white rounded-md w-full">
      <!-- Top Tabs -->
      <div class="border-b border-gray-500">
        <div class="flex overflow-x-auto hide-scrollbar">
          <button v-for="tab in dashboardTabs" :key="tab" @click="changeDashboardTab(tab)"
            class="pb-1 px-3 text-gray-700 font-medium relative transition-all" :class="[
              activeDashboardTab === tab
                ? 'font-semibold border-b border-gray-600'
                : 'hover:text-gray-900'
            ]">
            {{ tab }}
          </button>
        </div>
      </div>

      <!-- Tab Content for Dashboard Tabs -->
      <div class="w-full">
        <Transition name="slide" mode="out-in">
          <!-- Landing Tab Content -->
          <div v-if="activeDashboardTab === 'Landing'" key="landing" class="w-full">
            <LandingDashboard />
          </div>

          <!-- Projects Tab Content -->
          <div v-else-if="activeDashboardTab === 'Projects'" key="projects" class="w-full">
            <ProjectsDashboard />
          </div>

          <!-- Financial Tab Content -->
          <div v-else-if="activeDashboardTab === 'Financial'" key="financial" class="w-full">
            <FinancialDashboard />
          </div>

          <!-- CRM Tab Content -->
          <div v-else-if="activeDashboardTab === 'CRM'" key="crm" class="w-full">
            <CRMDashboard />
          </div>

          <!-- Human Resources Tab Content -->
          <div v-else-if="activeDashboardTab === 'Human-Resources'" key="hr" class="w-full">
            <HRDashboard />
          </div>
        </Transition>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onUnmounted, defineAsyncComponent } from 'vue';
import { useRoute, useRouter } from 'vue-router';

// Lazy load components for better performance
const LandingDashboard = defineAsyncComponent(() => import('./Primary_Dashboards_Components/LandingDashboard.vue'));
const ProjectsDashboard = defineAsyncComponent(() => import('./Primary_Dashboards_Components/ProjectsDashboard.vue'));
const FinancialDashboard = defineAsyncComponent(() => import('./Primary_Dashboards_Components/FinancialDashboard.vue'));
const CRMDashboard = defineAsyncComponent(() => import('./Primary_Dashboards_Components/CRMDashboard.vue'));
const HRDashboard = defineAsyncComponent(() => import('./Primary_Dashboards_Components/HRDashboard.vue'));

const route = useRoute();
const router = useRouter();

const dashboardTabs = [
  'Landing',
  'Projects',
  'Financial',
  'CRM',
  'Human-Resources'
];
const activeDashboardTab = ref('Landing');


onMounted(() => {
  updateTabFromHash();
});
watch(() => route.hash, (newHash) => {
  updateTabFromHash();
});
const changeDashboardTab = (tab) => {
  activeDashboardTab.value = tab;
  router.push({ hash: `#${tab}` });
};

const updateTabFromHash = () => {
  const hash = route.hash.replace('#', '');
  if (dashboardTabs.includes(hash)) {
    activeDashboardTab.value = hash;
  } else {
    activeDashboardTab.value = 'Landing';
    router.replace({ hash: '#Landing' });
  }
};
</script>

<style scoped>
.slide-enter-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(200px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-200px);
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}
</style>