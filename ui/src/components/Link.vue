<template>
  <Autocomplete
    ref="autocompleteRef"
    size="sm"
    v-model="value"
    :placeholder="props.placeholder || 'Search'"
    :options="options?.data || []"
    :class="disabled ? 'pointer-events-none' : ''"
    :disabled="disabled"
    @update:query="handleQueryUpdate"
  />
</template>

<script setup>
import { createResource, Autocomplete, debounce } from 'frappe-ui'
import { ref, computed, watch } from 'vue'

const props = defineProps({
  doctype: {
    type: String,
    required: true,
  },
  modelValue: {
    type: String,
    required: false,
    default: '',
  },
  filters: {
    type: Object,
    default: {},
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  showDesc: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: 'Search',
  },
})

const emit = defineEmits(['update:modelValue'])

const autocompleteRef = ref(null)
const searchText = ref('')

const value = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit('update:modelValue', val?.value || '')
  },
})

let label = ''
const options = createResource({
  url: 'frappe.desk.search.search_link',
  params: {
    doctype: props.doctype,
    txt: searchText.value,
    filters: props.filters,
  },
  method: 'POST',
  transform: (data) => {
    return data.map((doc) => {
      label = doc.label ? doc.label : doc.value
      if (props.showDesc) {
        const title = doc?.description?.split(',')?.[0]
        label = title ? title : label
      }

      return {
        label: label,
        value: doc.value,
      }
    })
  },
})

const reloadOptions = (searchTextVal) => {
  options.update({
    params: {
      txt: searchTextVal,
      doctype: props.doctype,
      filters: props.filters,
    },
  })
  options.reload()
}

const handleQueryUpdate = debounce((newQuery) => {
  const val = newQuery || ''
  if (searchText.value === val) return
  searchText.value = val
  reloadOptions(val)
}, 300)

watch(
  () => props.doctype,
  () => {
    if (!props.doctype || props.doctype === options?.params?.doctype) return
    reloadOptions('')
  },
  { immediate: true }
)

watch(
  () => props.filters,
  () => {
    const optionFilters = JSON.stringify(options.params.filters)
    const propFilters = JSON.stringify(props.filters)
    if (optionFilters == propFilters) return
    reloadOptions(searchText.value)
  }
)
</script>
