<template>
    <div class="fixed z-50 bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white rounded-xl shadow-lg border h-16">
      <div class="bg-[#EADDFF] h-full flex item-center w-14 rounded-tl-xl rounded-bl-xl">
        <span class="px-6 py-5 rounded-md text-md font-normal">
          {{ count }}
        </span>
      </div>
      <div class="pr-5">
        <span class="text-md font-normal pl-5">Tasks Selected</span>
      </div>
      <div class="flex items-center gap-4">
        <button @click="$emit('duplicate')" class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DuplicateIcon />
          Duplicate
        </button>
        <button @click="$emit('delete')" class="flex flex-col items-center gap-1 px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DeleteIcon />
          Delete
        </button>
        <div @click="showMoveOptions = !showMoveOptions" class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm cursor-pointer ml-2 relative">
          <MoveIcon />
          Move
          
          <div v-if="showMoveOptions" class="absolute top-full mt-1 left-0 bg-white shadow-lg rounded-md p-2 z-10 min-w-[150px]">
            <div v-for="option in moveOptions" :key="option.id" 
                 class="px-3 py-2 hover:bg-gray-100 cursor-pointer text-left"
                 @click="handleMove(option)">
              {{ option.name }}
            </div>
          </div>
        </div>
      </div>
      <button @click="$emit('clear')" class="ml-2 p-4 hover:bg-gray-100 rounded-full">
        <CrossIcon />
      </button>
    </div>
  </template>
  
  <script setup>
  import { ref } from 'vue';
  // import DuplicateIcon from '../icons/DuplicateIcon.vue';
  // import DeleteIcon from '../icons/DeleteIcon.vue';
  // import MoveIcon from '../icons/MoveIcon.vue';
  // import CrossIcon from '../icons/CrossIcon.vue';
  
  const props = defineProps({
    count: {
      type: Number,
      required: true
    }
  });
  
  const emit = defineEmits(['duplicate', 'delete', 'move', 'clear']);
  
  const showMoveOptions = ref(false);
  const moveOptions = ref([
    { id: 'past', name: 'Past Dates' },
    { id: 'today', name: 'Today' },
    { id: 'week', name: 'This Week' },
    { id: 'unassigned', name: 'Unassigned' },
    { id: 'john', name: 'John Doe' },
    { id: 'priya', name: 'Priya Patel' }
  ]);
  
  const handleMove = (option) => {
    emit('move', option);
    showMoveOptions.value = false;
  };
  </script>