<template>
  <div class="w-full overflow-auto h-[80vh]">
    <!-- Unassigned Section -->
    <TaskSection
      :title="'Unassigned'"
      :count="unassignedTasks.length"
      :tasks="unassignedTasks"
      :expanded="expandedSections.unassigned"
      :selectedTasks="selectedTasks"
      @toggle="toggleSection('unassigned')"
      @task-selected="handleTaskSelected"
      @task-clicked="handleTaskClicked"
      :projectId="projectId"
    />

    <!-- User Sections -->
    <div v-for="user in users" :key="user.id">
      <TaskSection
        :title="user.label"
        :count="getUserTasks(user.value).length"
        :tasks="getUserTasks(user.value)"
        :expanded="expandedSections[user.value]"
        :selectedTasks="selectedTasks"
        @toggle="toggleSection(user.value)"
        @task-selected="handleTaskSelected"
        @task-clicked="handleTaskClicked"
        :projectId="projectId"
      />
    </div>

    <!-- Bottom Selection Actions -->
    <div v-if="selectedTasks.length > 0"
      class="fixed z-50 bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white rounded-xl shadow-lg border h-16">
      <div class="bg-[#EADDFF] h-full flex item-center w-14 rounded-tl-xl rounded-bl-xl">
        <span class="px-6 py-5 rounded-md text-md font-normal">
          {{ selectedTasks.length }}
        </span>
      </div>
      <div class="pr-5">
        <span class="text-md font-normal pl-5">Tasks Selected</span>
      </div>
      <div class="flex items-center gap-4">
        <button @click="handleDuplicate"
          class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DuplicateIcon />
          Duplicate
        </button>
        <button @click="handleDelete"
          class="flex flex-col items-center gap-1 px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DeleteIcon />
          Delete
        </button>
        <Autocomplete :options="users" @update:modelValue="(v) => handleMove(v)" placeholder="Select User">
          <template #target="{ togglePopover }">
            <div @click="togglePopover()"
              class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm cursor-pointer ml-2">
              <MoveIcon />
              Move
            </div>
          </template>
        </Autocomplete>
      </div>
      <button @click="clearSelection" class="ml-2 p-4 hover:bg-gray-100 rounded-full">
        <CrossIcon />
      </button>
    </div>

    <!-- Task Overlay for editing -->
    <TaskOverlay
      v-if="showTaskOverlay"
      :task="currentTask"
      :projectId="projectId"
      @close="showTaskOverlay = false"
      @save="handleTaskSave"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { createResource, createListResource, toast, Autocomplete } from 'frappe-ui';
import TaskSection from './TaskSection.vue';
import TaskOverlay from './TaskOverlay.vue';
import DuplicateIcon from '../icons/DuplicateIcon.vue';
import DeleteIcon from '../icons/DeleteIcon.vue';
import MoveIcon from '../icons/MoveIcon.vue';
import CrossIcon from '../icons/CrossIcon.vue';
import {sessionUser} from '../../data/session'
const login_user = sessionUser()
const props = defineProps({
  projectId: {
    type: String,
    required: true
  }
});

// State
const tasks = ref([]);
const users = ref([]);
const selectedTasks = ref([]);
const expandedSections = ref({
  unassigned: true
});
const showTaskOverlay = ref(false);
const currentTask = ref(null);

// Fetch users and tasks on mount
onMounted(() => {
  fetchUsers();
  fetchTasks();
});

// Fetch users from API
// const fetchUsers = () => {
//   createListResource({
//     doctype: 'IDP Project User',
//     parent: 'IDP Project',
//     fields: ['user as value', 'full_name as label'],
//     orderBy: 'creation desc',
//     groupBy: 'user',
//     start: 0,
//     auto: 1,
//     pageLength: 100,
//     filters: {
//       parent: props.projectId,
//     },
//     onSuccess(data) {
//       users.value = data;
//       users.value = data.filter(user => user.value !== login_user);
//       // Initialize expanded state for each user
//       users.value.forEach(user => {
//         expandedSections.value[user.value] = true;
//       });
//     },
//   });
// };
const fetchUsers = () => {
  createListResource({
    doctype: 'Employee',
    // parent: 'IDP Project',
    fields: ['user_id as value', 'employee_name as label'],
    // orderBy: 'creation desc',
    groupBy: 'user_id',
    start: 0,
    auto: 1,
    pageLength: 100,
    filters: {
      reports_to: window.emp_id,
    },
    onSuccess(data) {
      users.value = data;
      users.value = data.filter(user => user.value !== login_user);
      // Initialize expanded state for each user
      users.value.forEach(user => {
        expandedSections.value[user.value] = true;
      });
    },
  });
};
// Fetch tasks from API
const fetchTasks = () => {
  const tasksResource = createResource({
    url: 'inspira.inspira.api.tasks.tasks.get_all_tasks',
    makeParams: () => ({
      project_id: props.projectId,
      view_type: 'team'
    }),
    auto: true,
    onSuccess: (data) => {
      console.log(data)
      tasks.value = data;
    },
    onError: (error) => {
      console.error('Failed to fetch tasks:', error);
      toast({
        title: 'Error',
        text: error.messages?.[0] || 'Failed to fetch tasks',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      });
    },
  });
};
// const fetchTasks = () => {
//   tasks.value = [
//     {
//       id: '1',
//       subject: 'Complete project proposal',
//       group: 'Personal',
//       status: 'To Do',
//       priority: 'High',
//       planned_start_date: '2025-05-01',
//       planned_end_date: '2025-05-01',
//       actual_start_date: null,
//       actual_end_date: null,
//       assignees: [
//         { value: 'user1', label: 'John Doe' }
//       ],
//       remarks: 'Need to finalize the scope'
// };

// Filter tasks by assignment
const unassignedTasks = computed(() => {
  return tasks.value.filter(task => (!task.assignees || task.assignees.length === 0 || task.assignees.every(a => !a.value)));
});

const getUserTasks = (userId) => {
  return tasks.value.filter(task => 
    task.assignees && 
    task.assignees.some(assignee => assignee.value === userId)
  );
};

// Section toggle
const toggleSection = (section) => {
  expandedSections.value[section] = !expandedSections.value[section];
};

// Task selection
const handleTaskSelected = (taskIds) => {
  selectedTasks.value = taskIds;
};

// Task click
const handleTaskClicked = (task) => {
  currentTask.value = task;
  showTaskOverlay.value = true;
};

// Task save
const handleTaskSave = (task) => {
  showTaskOverlay.value = false;
  fetchTasks();
  toast({
    title: 'Success',
    text: 'Task updated successfully',
    icon: 'check-circle',
    position: 'bottom-right',
    iconClasses: 'text-green-500',
  });
};

// Duplicate tasks
const handleDuplicate = () => {
  createResource({
    url: '/api/method/inspira.inspira.api.projects.project_planner.duplicate_tasks_with_children',
    params: {
      task_ids: selectedTasks.value,
    },
    auto: true,
    onSuccess(data) {
      fetchTasks();
      selectedTasks.value = [];
      toast({
        title: 'Success',
        text: 'Tasks duplicated successfully',
        icon: 'check-circle',
        position: 'bottom-right',
        iconClasses: 'text-green-500',
      });
    },
    onError(err) {
      toast({
        title: 'Error Occurred',
        text: err.messages?.[0] || 'An unknown error occurred',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      });
    },
  });
};

// Delete tasks
const handleDelete = () => {
  createResource({
    url: '/api/method/inspira.inspira.api.projects.project_planner.delete_tasks_with_children',
    params: {
      task_ids: selectedTasks.value,
    },
    auto: true,
    onSuccess(data) {
      fetchTasks();
      selectedTasks.value = [];
      toast({
        title: 'Success',
        text: 'Tasks deleted successfully',
        icon: 'check-circle',
        position: 'bottom-right',
        iconClasses: 'text-green-500',
      });
    },
    onError(err) {
      toast({
        title: 'Error Occurred',
        text: err.messages?.[0] || 'An unknown error occurred',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      });
    },
  });
};

// Move tasks
const handleMove = (targetUser) => {
  // Implementation would depend on your API
  console.log('Move tasks to user:', targetUser);
  
  // Example implementation
  const bulkUpdateParams = selectedTasks.value.map(taskId => ({
    doctype: 'IDP Task',
    docname: taskId,
    // Update assignee field
  }));
  
  // Call API to update tasks
  toast({
    title: 'Success',
    text: 'Tasks moved successfully',
    icon: 'check-circle',
    position: 'bottom-right',
    iconClasses: 'text-green-500',
  });
  
  fetchTasks();
  selectedTasks.value = [];
};

// Clear selection
const clearSelection = () => {
  selectedTasks.value = [];
};
</script>
<style scoped>
.overflow-auto{
  scrollbar-width: thin;
}
</style>