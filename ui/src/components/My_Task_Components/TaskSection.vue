<template>
  <div class="w-full">
    <div class="pt-4 pb-4">
      <!-- Header section -->
      <div class="flex items-center gap-6 cursor-pointer group">
        <div class="flex items-center gap-6 cursor-pointer" @click="$emit('toggle')"
          style="position: sticky;left:10px;">
          <span class="transform transition-transform" :class="{ 'rotate-this': !expanded }">
            <DropDownIcon />
          </span>
          <button style="color: #49454f" class="font-normal">
            {{ title }}
          </button>
          <span class="text-gray-500">{{ count }}</span>
        </div>
      </div>

      <!-- Tasks table -->
      <div v-if="expanded" class="mt-2 w-full">
        <div class="table-container">
          <table class="resizable-table">
            <thead>
              <tr class="text-left" style="background: #ece6f0">
                <ResizableHeader columnName="checkbox" :width="columnWidths.checkbox" style="
                    background-color: white !important;
                    position: sticky;
                    left: 0px;
                    z-index: 20;
                  ">
                  <!-- Empty for checkbox column -->
                </ResizableHeader>

                <ResizableHeader columnName="task" label="Task" :width="columnWidths.task" :minWidth="350"
                  :maxWidth="600" @update:width="(width) => (columnWidths.task = width)"
                  style="position: sticky; left: 50px; z-index: 20" class="taskcolumn">
                </ResizableHeader>

                <ResizableHeader columnName="people" label="People" :width="columnWidths.people" :minWidth="150"
                  :maxWidth="400" @update:width="(width) => (columnWidths.people = width)" />

                <ResizableHeader columnName="group" label="Group" :width="columnWidths.group" :minWidth="150"
                  :maxWidth="400" @update:width="(width) => (columnWidths.group = width)" />

                <ResizableHeader columnName="project" label="Project" :width="columnWidths.project" :minWidth="150"
                  :maxWidth="400" @update:width="(width) => (columnWidths.project = width)" />

                <ResizableHeader columnName="status" label="Status" :width="columnWidths.status" :minWidth="180"
                  :maxWidth="400" @update:width="(width) => (columnWidths.status = width)" />

                <ResizableHeader columnName="date" label="Planned Start Date" :width="columnWidths.planned_start_date"
                  :minWidth="150" :maxWidth="400" @update:width="
                    (width) => (columnWidths.planned_start_date = width)
                  " />

                <ResizableHeader columnName="date" label="Planned End Date" :width="columnWidths.planned_end_date"
                  :minWidth="150" :maxWidth="400" @update:width="
                    (width) => (columnWidths.planned_end_date = width)
                  " />

                <ResizableHeader columnName="date" label="Actual Start Date" :width="columnWidths.actual_start_date"
                  :minWidth="150" :maxWidth="400" @update:width="
                    (width) => (columnWidths.actual_start_date = width)
                  " />

                <ResizableHeader columnName="date" label="Actual End Date" :width="columnWidths.actual_end_date"
                  :minWidth="150" :maxWidth="400" @update:width="
                    (width) => (columnWidths.actual_end_date = width)
                  " />

                <ResizableHeader columnName="priority" label="Priority" :width="columnWidths.priority" :minWidth="150"
                  :maxWidth="400" @update:width="(width) => (columnWidths.priority = width)" />

                <ResizableHeader columnName="actions" :isLastColumn="true" class="last-column-header"
                  :width="columnWidths.actions">
                </ResizableHeader>
              </tr>
            </thead>
            <tbody class="ManageColorState">
              <tr v-for="(task, taskIndex) in tasks" :key="taskIndex"
                class="border-t border-gray-100 group hover:bg-[#F8F7F9]" @click="$emit('task-clicked', task)">

                <td class="relative" style="position: sticky; left: 0px; z-index: 20;background: white;">
                  <!-- Background placeholder -->
                  <div class="absolute inset-0 bg-white" :class="{ 'invisible': isTaskSelected(task.id) || isHovered }">
                  </div>

                  <!-- Interactive elements -->
                  <div class="text-sm py-3 flex items-center gap-2 transition duration-300 relative" :class="{
                    'opacity-100 translate-x-0': isTaskSelected(task.id),
                    'opacity-0 translate-x-1 group-hover:opacity-100 group-hover:translate-x-0': !isTaskSelected(task.id),
                  }" @mouseover="isHovered = true" @mouseleave="isHovered = false">
                    <span class="cursor-move">
                      <!-- <NineDotsIcon /> -->
                    </span>
                    <input type="checkbox" class="border rounded-sm bg-[#CAC4D0]" v-model="localSelectedTasks"
                      :value="task.id" @click.stop />
                  </div>
                </td>

                <!--Task cell: -->
                <td class="py-1 text-sm border-b bg-white group-hover:bg-[#F8F7F9]"
                  style="position: sticky; left: 50px; z-index: 10">
                  <div class="flex items-center justify-between cursor-pointer">
                    <p class="flex items-center gap-1">
                      {{ task.subject || task.name }}
                    </p>
                  </div>
                </td>

                <!-- People cell: -->
                <td class="py-1 text-sm px-4 relative border-b activecell">
                  <div class="flex -space-x-1">
                    <!-- First 4 Assignees -->
                    <div v-if="task.assignees && task.assignees.length"
                      v-for="(assignee, idx) in task.assignees.slice(0, 5)" :key="idx"
                      class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                      <Tooltip :text="assignee.label" :hover-delay="0" :placement="'top'">
                        <span>
                          {{ getInitialsFromFullName(assignee) }}
                        </span>
                      </Tooltip>
                    </div>

                    <!-- Show "+n" Circle with Tooltip for Remaining Assignees -->
                    <div v-if="task.assignees && task.assignees.length > 5"
                      class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                      <Tooltip :text="getRemainingAssigneesLabels(task.assignees.slice(5))" :hover-delay="0"
                        :placement="'top'">
                        <span>
                          +{{ task.assignees.length - 5 }}
                        </span>
                      </Tooltip>
                    </div>

                    <!-- Show Owner Icon if No Assignee -->
                    <div v-else-if="!task.assignees || task.assignees.length === 0"
                      class="w-8 h-8 rounded-full bg-[#CCC2DC] flex items-center justify-center text-xs font-medium text-[#000] ring-1 ring-white">
                      <OwnerIcon />
                    </div>
                  </div>
                </td>


                <!-- Group cell: -->
                <td class="py-1 text-sm px-4 border-b activecell">
                  <div class="flex items-center">
                    <span v-if="task.group === 'Personal'" class="h-2 w-2 rounded-full bg-red-300 mr-2"></span>
                    <span v-else-if="task.group === 'Internal'" class="h-2 w-2 rounded-full bg-green-300 mr-2"></span>
                    <span v-else class="h-2 w-2 rounded-full bg-blue-300 mr-2"></span>
                    {{ task.group }}
                  </div>
                </td>

                <!-- Project cell: -->
                <td class="py-1 text-sm px-4 border-b activecell">
                 <div class="max-w-28 truncate"> {{ task.project || '-' }} </div>
                </td>

                <!-- Status cell: -->
                <td class="py-1 text-sm px-4 border-b activecell">
                  <span class="px-2 py-1 rounded-md text-xs font-medium" :class="{
                    'bg-red-100 text-red-800': task.status === 'To Do',
                    'bg-yellow-100 text-yellow-800': task.status === 'In Progress',
                    'bg-green-100 text-green-800': task.status === 'Done',
                    'bg-gray-100 text-gray-800': task.status === 'Not Required'
                  }">
                    {{ task.status }}
                  </span>
                </td>

                <!-- Date cells: -->
                <td class="py-1 text-sm px-4 border-b activecell">
                  <div class="flex items-center">
                    <CalendarIcon class="h-4 w-4 mr-1" v-if="!task.planned_start_date" />
                    {{ formatDate(task.planned_start_date, 'DD-MMM-YYYY') }}
                  </div>
                </td>

                <td class="py-1 text-sm px-4 border-b activecell">
                  <div class="flex items-center">
                    <CalendarIcon class="h-4 w-4 mr-1" v-if="!task.planned_end_date" />
                    {{ formatDate(task.planned_end_date, 'DD-MMM-YYYY') }}
                  </div>
                </td>

                <td class="py-1 text-sm px-4 border-b activecell">
                  <div class="flex items-center">
                    <CalendarIcon class="h-4 w-4 mr-1" v-if="!task.actual_start_date" />
                    {{ formatDate(task.actual_start_date, 'DD-MMM-YYYY') }}
                  </div>
                </td>

                <td class="py-1 text-sm px-4 border-b activecell">
                  <div class="flex items-center">
                    <CalendarIcon class="h-4 w-4 mr-1" v-if="!task.actual_end_date" />
                    {{ formatDate(task.actual_end_date, 'DD-MMM-YYYY') }}
                  </div>
                </td>

                <!-- Priority cell: -->
                <td class="py-1 text-sm px-4 border-b activecell">
                  <span class="px-2 py-1 rounded-md text-xs font-medium" :class="{
                    'bg-red-100 text-red-800': task.priority === 'Critical',
                    'bg-orange-100 text-orange-800': task.priority === 'High',
                    'bg-yellow-100 text-yellow-800': task.priority === 'Medium',
                    'bg-blue-100 text-blue-800': task.priority === 'Low'
                  }">
                    {{ task.priority }}
                  </span>
                </td>

                <td class="py-1 text-sm px-4 border-b"></td>
              </tr>

              <!-- Empty state -->
              <tr v-if="tasks.length === 0">
                <td colspan="12" class="px-4 py-3 text-sm text-gray-300">
                  <p class="ml-[25%]">No Tasks </p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Tooltip } from 'frappe-ui';
import ResizableHeader from '../ResizableHeader.vue';
import DropDownIcon from '../icons/DropDownIcon.vue';
import CalendarIcon from '../icons/CalendarIcon.vue';
import NineDotsIcon from '../icons/NineDotsIcon.vue';
import OwnerIcon from '../icons/OwnerIcon.vue';
import { formatDate } from '../../utils/format';
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  count: {
    type: Number,
    default: 0
  },
  tasks: {
    type: Array,
    default: () => []
  },
  expanded: {
    type: Boolean,
    default: true
  },
  selectedTasks: {
    type: Array,
    default: () => []
  },
  projectId: {
    type: String,
    required: true
  }
});

const emit = defineEmits(['toggle', 'task-selected', 'task-clicked']);

// Local state
const localSelectedTasks = ref([]);
const isHovered = ref(false);
const draggedTaskIndex = ref(null);

// Column widths
const columnWidths = ref({
  checkbox: 50,
  task: 400,
  people: 220,
  group: 150,
  project: 150,
  status: 220,
  planned_start_date: 250,
  planned_end_date: 250,
  actual_start_date: 250,
  actual_end_date: 250,
  priority: 150,
  actions: 60,
});

// Sync local selected tasks with parent
watch(localSelectedTasks, (newVal) => {
  emit('task-selected', newVal);
});

watch(() => props.selectedTasks, (newVal) => {
  localSelectedTasks.value = [...newVal];
}, { deep: true });

// Check if task is selected
const isTaskSelected = (taskId) => {
  return localSelectedTasks.value.includes(taskId);
};

// Format date
// const formatDate = (dateString) => {
//   if (!dateString) return '';

//   try {
//     const date = new Date(dateString);
//     return date.toLocaleDateString('en-US', {
//       year: 'numeric',
//       month: '2-digit',
//       day: '2-digit'
//     });
//   } catch (e) {
//     return dateString;
//   }
// };

// Get initials from full name
const getInitialsFromFullName = (assignee) => {
  let name = assignee?.label;
  if (!name) return '';

  return name
    .split(' ')
    .filter((word) => word.length)
    .map((word) => word[0].toUpperCase())
    .join('');
};

const getRemainingAssigneesLabels = (remainingAssignees) => {
  return remainingAssignees.map(a => a.label).join(', ');
}

// Drag and drop functionality
const dragStart = (event, taskIndex) => {
  event.dataTransfer.setData('text/plain', JSON.stringify({ taskIndex }));
  event.dataTransfer.effectAllowed = 'move';
  draggedTaskIndex.value = taskIndex;
};

const drop = (event, targetTaskIndex) => {
  event.preventDefault();
  const data = JSON.parse(event.dataTransfer.getData('text/plain'));
  const { taskIndex: sourceTaskIndex } = data;

  if (sourceTaskIndex === targetTaskIndex) return;

  // In a real implementation, you would call an API to update the task order
  console.log(`Move task from index ${sourceTaskIndex} to ${targetTaskIndex}`);

  // Reset drag state
  draggedTaskIndex.value = null;
};

const dragOver = (event, targetTaskIndex) => {
  event.preventDefault();
  const rows = event.currentTarget.parentElement.children;
  for (let i = 0; i < rows.length; i++) {
    rows[i].classList.remove('drag-over');
  }
  event.currentTarget.classList.add('drag-over');
};

const dragLeave = (event) => {
  event.currentTarget.classList.remove('drag-over');
};
</script>

<style scoped>
.rotate-this {
  transform: rotate(270deg);
}

.transform {
  transition: transform 0.3s ease-in-out;
}

.resizable-table {
  table-layout: fixed;
  min-width: fit-content;
  border-collapse: separate;
  border-spacing: 0;
}

.table-container {
  /* overflow-x: auto; */
  width: 100%;
}

body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

.resizable-column>* {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resizable-column:hover {
  background-color: #c8c2d1;
  cursor: pointer;
}

.activecell:hover {
  border: 1px solid rgb(212, 212, 212);
  border-radius: 0.25rem;
}

input[type='checkbox'] {
  outline: none;
  box-shadow: none;
}

input[type='checkbox']:checked {
  background-color: #4a4458;
  border-color: white;
}
</style>