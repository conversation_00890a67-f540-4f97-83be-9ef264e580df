<template>
  <div class="fixed inset-0 bg-black/40 backdrop-blur-xs z-50 flex justify-end" @click.self="$emit('close')">
    <div class="bg-white w-full max-w-xl shadow-xl transform transition-transform duration-300 ease-in-out">
      <!-- Header -->
      <div class="flex items-center justify-end p-4 border-b">
        <!-- <div class="bg-purple-100 text-purple-700 hover:bg-purple-200 rounded-md px-3 flex items-center">
          <Check class="h-4 w-4" />
          <Button variant="secondary" size="sm">
            <span class="text-sm font-medium">Mark Complete</span>
          </Button>
        </div> -->
        <div>
          <Button variant="ghost" size="icon" @click="$emit('close')" class="text-gray-500 hover:text-gray-700">
            <X class="h-5 w-5" />
          </Button>
        </div>
      </div>

      <!-- Task Title -->
      <div class="px-6 pt-4 pb-2">
        <h1 class="text-xl font-bold text-gray-900">
          {{ formData.subject || 'Task Name' }}
        </h1>
      </div>

      <!-- Form Content -->
      <div class="px-6 pb-6 overflow-y-auto max-h-[calc(100vh-150px)]">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
          <!-- Name -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Name</Label>
            <Input v-model="formData.subject" class="w-full text-sm border-gray-300 rounded-md" placeholder="Task name"
              @input="updateTaskTitle" />
          </div>

          <!-- Group -->
           <div :class="formData.group === 'Planner' ? 'w-full pointer-events-none' : 'w-full'" v-if="formData.group === 'Planner'">
            <Label class="block text-sm font-medium text-gray-700 mb-1">Group</Label>
            <Input v-model="formData.group" class="w-full text-sm border-gray-300 rounded-md" placeholder="Select Group"
             />
          </div>
          <div v-else>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Group</Label>
            <Autocomplete v-model="formData.group" placeholder="Select Group" :showOptions="true" :options="taskGroups"
              class="w-full text-sm" />
          </div>

          <!-- Status -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Status</Label>
            <Autocomplete v-model="formData.status" placeholder="Select Status" :showOptions="true"
              :options="statusList" class="w-full text-sm bg-green-100 text-green-800 rounded-md" />
          </div>

          <!-- People -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">People</Label>
            <Autocomplete :options="usersLists" v-model="formData.assignees" placeholder="Select Assignee"
              :multiple="true" :by="(a, b) => a.value === b.value" class="w-full text-sm">
            </Autocomplete>
          </div>

          <!-- Priority -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Priority</Label>
            <Autocomplete v-model="formData.priority" :placeholder="'Select Priority'" :hideSearch="true"
              :options="priorities" class="w-full text-sm">
            </Autocomplete>
          </div>

          <!-- Project -->
          <div v-if="formData.project">
            <Label class="block text-sm font-medium text-gray-700 mb-1">Project</Label>
            <Input v-model="formData.project" disabled class="w-full text-sm bg-gray-50 text-gray-700" />
          </div>

          <!-- Planned Start Date -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Planned Start Date</Label>
            <DatePicker icon-left="calendar" :value="formData.planned_start_date" placeholder="Select date"
              :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')"
              :disabled="false" @change="(date) => formData.planned_start_date = date" class="w-full text-sm" />
          </div>

          <!-- Planned End Date -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Planned End Date</Label>
            <DatePicker icon-left="calendar" :value="formData.planned_end_date" placeholder="Select date" :disabled="false"
            :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')"
              @change="(date) => formData.planned_end_date = date" class="w-full text-sm" />
          </div>

          <!-- Actual Start Date -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Actual Start Date</Label>
            <DatePicker icon-left="calendar" :value="formData.actual_start_date" placeholder="Select date"
            :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')"
              :disabled="false" @change="(date) => formData.actual_start_date = date" class="w-full text-sm" />
          </div>

          <!-- Actual End Date -->
          <div>
            <Label class="block text-sm font-medium text-gray-700 mb-1">Actual End Date</Label>
            <DatePicker icon-left="calendar" :value="formData.actual_end_date" placeholder="Select date"
            :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')"
              :disabled="false" @change="(date) => formData.actual_end_date = date" class="w-full text-sm" />
          </div>
        </div>

        <!-- Remarks - Full Width -->
        <div class="mt-6">
          <Label class="block text-sm font-medium text-gray-700 mb-1">Remarks</Label>
          <Textarea v-model="formData.remarks" class="w-full text-sm border-gray-300 rounded-md"
            placeholder="Enter Remarks here" :rows="6" />
        </div>
      </div>
      <!-- Sticky Footer -->
      <div class="p-6 mb-2 border-t bg-white">
        <Button @click="saveTask" :variant="'solid'" theme="purple"
          class="w-full bg-purple-100 text-purple-700 hover:bg-purple-200">
          Save Task
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed ,watch} from 'vue';
import {
  createResource,
  createListResource,
  toast,
  Tooltip,
  Autocomplete,
  DatePicker,
  Textarea,
  Button,
  Input,
  Select
} from 'frappe-ui';
import {
  Check,
  X,
  ChevronDown,
  ArrowUpRight,
  Paperclip as PaperclipIcon,
  Calendar,
  MoreHorizontal,
  Plus
} from 'lucide-vue-next';
import { formatDate } from '../../utils/format';
import { sessionUser } from '../../data/session';
const currentUser = sessionUser()
const props = defineProps({
  task: {
    type: Object,
    default: null
  },
  projectId: {
    type: String,
    required: true
  }
});

const priorities = [
  { label: 'Urgent', value: 'Urgent' },
  { label: 'High', value: 'High' },
  { label: 'Medium', value: 'Medium' },
  { label: 'Low', value: 'Low' }
];

// const taskGroups = (props.task && props.task.group == "Planner")?[
//   { label: 'Personal', value: 'Personal' },
//   { label: 'Internal', value: 'Internal' },
//   { label: 'Planner', value: 'Planner' }
// ]:[
//   { label: 'Personal', value: 'Personal' },
//   { label: 'Internal', value: 'Internal' }
// ];

const taskGroups =[
  { label: 'Personal', value: 'Personal' },
  { label: 'Internal', value: 'Internal' }
];

const emit = defineEmits(['close', 'save', 'insert']);


// Form data
const formData = ref({
  id: null,
  subject: '',
  group: '',
  status: '',
  assignees: [],
  priority: '',
  planned_start_date: '',
  planned_end_date: '',
  actual_start_date: '',
  actual_end_date: '',
  remarks: '',
  project: props.projectId
});
const updateassignee = (selectedGroup) => {
  if (!selectedGroup) return;
  // Get the group value (handle both object and string formats)
  const groupValue = selectedGroup.value || selectedGroup;
  const matchedUser = usersLists.value.find(user => user.value == currentUser);
  if (matchedUser && groupValue === 'Personal') {
          formData.value.assignees = 
          //usersLists.value =
          [{
            value: matchedUser.value,
            label: matchedUser.label
          }];
        }
  else{
    fetchUsers()
  }
};
watch(() => formData.value.group, (newGroup, oldGroup) => {
  if (newGroup && newGroup !== oldGroup) {
    updateassignee(newGroup);
  }
}, { deep: true });

// UI state
const activeTab = ref('comments');
const newComment = ref('');
const usersLists = ref([]);
const statusList = ref([]);

const GroupList = ref([
  { label: 'Personal', value: 'Personal' },
  { label: 'Internal', value: 'Internal' }
])
const PriorityList = ref([
  { label: 'Critical', value: 'Critical' },
  { label: 'High', value: 'High' },
  { label: 'Medium', value: 'Medium' },
  { label: 'Low', value: 'Low' }
])
// Fetch users and status options
onMounted(() => {
  fetchUsers();
  fetchStatusOptions();

  if (props.task) {
    // Clone the task data to avoid modifying the original
    Object.assign(formData.value, props.task);
    console.log(props.task)
    if(props.task.group){
      const matchedGroup = GroupList.value.find(status => status.label == props.task.group);
        if (matchedGroup) {
          formData.value.group = {
            value: matchedGroup.value,
            label: matchedGroup.label
          };
        }
    }
    
  } else {
    // Set default dates for new tasks
    const today = new Date().toISOString().split('T')[0];
    formData.value.planned_start_date = today;
    formData.value.planned_end_date = today;
  }
});

// Fetch users
const fetchUsers = () => {
  if (!props.task || !props.task.projectID) {
    createListResource({
      doctype: 'User',
      fields: ['name as value', 'full_name as label'],
      filters:[["enabled",'=',1]],
      orderBy: 'full_name asc',
      start: 0,
      pageLength: 500000,
      auto: true,
      onSuccess: (data) => {
        usersLists.value = data
      }
    })
  } else {
    createListResource({
      doctype: 'IDP Project User',
      parent: 'IDP Project',
      fields: ['user as value', 'full_name as label'],
      orderBy: 'creation desc',
      start: 0,
      auto: 1,
      pageLength: 100,
      filters: {
        parent: props.task.projectID,
      },
      onSuccess(data) {
        usersLists.value = data;
      },
    });
  }
};

// Fetch status options
const fetchStatusOptions = () => {
  createListResource({
    doctype: 'IDP Status Master',
    fields: ['name as value', 'status as label'],
    orderBy: 'creation desc',
    start: 0,
    auto: 1,
    pageLength: 100,
    filters: {
      form: "Task",
    },
    onSuccess(data) {
      statusList.value = data;
      if(props.task){
      const matchedStatus = statusList.value.find(status => status.label == props.task.status);
        if (matchedStatus) {
          formData.value.status = {
            value: matchedStatus.value,
            label: matchedStatus.label
          };
        }
      }
    },
  });
};

// Get color class for group
const getGroupColorClass = () => {
  switch (formData.value.group.value) {
    case 'Personal':
      return 'bg-red-500';
    case 'Internal':
      return 'bg-green-500';
    case 'Planner':
      return 'bg-blue-500';
    default:
      return 'bg-gray-500';
  }
};

// Get random avatar color for collaborators
const getRandomAvatarColor = (index) => {
  const colors = [
    '#CCC2DC', '#CDFFD8', '#FFD6E4', '#D4FAFC', '#FFE9C9'
  ];
  return colors[index % colors.length];
};

// Get initials from full name
const getInitialsFromFullName = (assignee) => {
  let name = assignee?.label;
  if (!name) return '';

  return name
    .split(' ')
    .filter((word) => word.length)
    .map((word) => word[0].toUpperCase())
    .slice(0, 2)
    .join('');
};

// Update task title when name changes
const updateTaskTitle = () => {
  // Task title will automatically reflect the name/subject
};

// Save task
const saveTask = () => {
  // Validate required fields
  if (!formData.value.subject) {
    toast({
      title: 'Error',
      text: 'Task name is required',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    });
    return;
  }

  // Prepare assignees data
  let assigneesData = [];
  if (formData.value.assignees && formData.value.assignees.length) {
    assigneesData = formData.value.assignees.map(user => ({
      doctype: 'IDP Task Assignee',
      user: user.value,
      user_name: user.label,
    }));
  }

  // if (!assigneesData.length) {
  //   toast({
  //     title: 'Error',
  //     text: 'At least one assignee is required',
  //     icon: 'alert-circle',
  //     position: 'bottom-right',
  //     iconClasses: 'text-red-500',
  //   });
  //   return;
  // }

  if (!formData.value.status) {
    toast({
      title: 'Error',
      text: 'Status is required',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    });
    return;
  }

  if (!formData.value.group) {
    toast({
      title: 'Error',
      text: 'Group is required',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    });
    return;
  }

  // Prepare task data
  const taskData = {
    doctype: 'IDP Task',
    project: props.projectId,
    subject: formData.value.subject,
    status: formData.value.status?.value || formData.value.status,
    task_assignee: assigneesData,
    type: 'Task',
    is_group: true,
    is_milestone: false,
    planned_start_date: formData.value.planned_start_date,
    planned_end_date: formData.value.planned_end_date,
    actual_start_date: formData.value.actual_start_date,
    actual_end_date: formData.value.actual_end_date,
    remarks: formData.value.remarks,
    priority: formData.value.priority.value,
    group: formData.value.group.value
  };

  // If task already exists, update it
  if (props.task && props.task.id) {
    taskData.name = props.task.id;

    createResource({
      url: 'frappe.client.set_value',
      makeParams: () => ({
        doctype: 'IDP Task',
        name: taskData.name,
        fieldname: taskData
      }),
      auto: true,
      onSuccess: () => {
        toast({
          title: 'Success',
          text: 'Task updated successfully',
          icon: 'check-circle',
          position: 'bottom-right',
          iconClasses: 'text-green-500',
        });
        emit('save', formData.value);
      },
      onError: (error) => {
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to update task',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        });
      },
    });
  } else {
    // Create new task
    createResource({
      url: 'frappe.client.insert',
      makeParams: () => ({
        doc: taskData
      }),
      auto: true,
      onSuccess: (data) => {
        toast({
          title: 'Success',
          text: 'Task created successfully',
          icon: 'check-circle',
          position: 'bottom-right',
          iconClasses: 'text-green-500',
        });
        formData.value.id = data.name;
        emit('save', formData.value);
      },
      onError: (error) => {
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create task',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        });
      },
    });
  }
};
</script>

<style>
/* Add any custom styles here if needed */
.Label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}
</style>