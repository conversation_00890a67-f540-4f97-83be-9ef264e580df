<template>
  <div class="w-full overflow-auto h-[80vh]">
    <!-- Past Dates Section -->
    <TaskSection
      :title="'Past Due'"
      :count="pastTasks.length"
      :tasks="pastTasks"
      :expanded="expandedSections.pastDates"
      :selectedTasks="selectedTasks"
      @toggle="toggleSection('pastDates')"
      @task-selected="handleTaskSelected"
      @task-clicked="handleTaskClicked"
      :projectId="projectId"
    />

    <!-- Today Section -->
    <!-- <TaskSection
      :title="'Today'"
      :count="todayTasks.length"
      :tasks="todayTasks"
      :expanded="expandedSections.today"
      :selectedTasks="selectedTasks"
      @toggle="toggleSection('today')"
      @task-selected="handleTaskSelected"
      @task-clicked="handleTaskClicked"
      :projectId="projectId"
    /> -->

    <!-- This Week Section -->
    <TaskSection
      :title="'This Week'"
      :count="weekTasks.length"
      :tasks="weekTasks"
      :expanded="expandedSections.thisWeek"
      :selectedTasks="selectedTasks"
      @toggle="toggleSection('thisWeek')"
      @task-selected="handleTaskSelected"
      @task-clicked="handleTaskClicked"
      :projectId="projectId"
    />
    <!-- Due Later Selection-->
    <TaskSection
      :title="'Due Later'"
      :count="nextweekTasks.length"
      :tasks="nextweekTasks"
      :expanded="expandedSections.nextweekTasks"
      :selectedTasks="selectedTasks"
      @toggle="toggleSection('nextweekTasks')"
      @task-selected="handleTaskSelected"
      @task-clicked="handleTaskClicked"
      :projectId="projectId"
    />

    <!--No Due Date Selection-->
    <TaskSection
      :title="'No Due Date'"
      :count="noDueDate.length"
      :tasks="noDueDate"
      :expanded="expandedSections.noDueDate"
      :selectedTasks="selectedTasks"
      @toggle="toggleSection('noDueDate')"
      @task-selected="handleTaskSelected"
      @task-clicked="handleTaskClicked"
      :projectId="projectId"
    />
    <!-- Bottom Selection Actions -->
    <div v-if="selectedTasks.length > 0"
      class="fixed z-50 bottom-4 left-1/2 transform -translate-x-1/2 flex items-center gap-2 bg-white rounded-xl shadow-lg border h-16">
      <div class="bg-[#EADDFF] h-full flex item-center w-14 rounded-tl-xl rounded-bl-xl">
        <span class="px-6 py-5 rounded-md text-md font-normal">
          {{ selectedTasks.length }}
        </span>
      </div>
      <div class="pr-5">
        <span class="text-md font-normal pl-5">Tasks Selected</span>
      </div>
      <div class="flex items-center gap-4">
        <button @click="handleDuplicate"
          class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DuplicateIcon />
          Duplicate
        </button>
        <button @click="handleDelete"
          class="flex flex-col items-center gap-1 px-3 py-1 hover:bg-gray-100 rounded-md text-sm">
          <DeleteIcon />
          Delete
        </button>
        <Autocomplete :options="movableSections" @update:modelValue="(v) => handleMove(v)" placeholder="Select Section">
          <template #target="{ togglePopover }">
            <div @click="setMovableSections(), togglePopover()"
              class="flex flex-col items-center px-3 py-1 hover:bg-gray-100 rounded-md text-sm cursor-pointer ml-2">
              <MoveIcon />
              Move
            </div>
          </template>
        </Autocomplete>
      </div>
      <button @click="clearSelection" class="ml-2 p-4 hover:bg-gray-100 rounded-full">
        <CrossIcon />
      </button>
    </div>

    <!-- Task Overlay for editing -->
    <TaskOverlay
      v-if="showTaskOverlay"
      :task="currentTask"
      :projectId="projectId"
      @close="handleTaskOverlayClose"
      @save="handleTaskSave"
      @insert="handleTaskSave"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { createResource, toast, Autocomplete } from 'frappe-ui';
import TaskSection from './TaskSection.vue';
import TaskOverlay from './TaskOverlay.vue';
import DuplicateIcon from '../icons/DuplicateIcon.vue';
import DeleteIcon from '../icons/DeleteIcon.vue';
import MoveIcon from '../icons/MoveIcon.vue';
import CrossIcon from '../icons/CrossIcon.vue';
import { sessionUser } from "../../data/session"

const props = defineProps({
  projectId: {
    type: String,
    required: true
  }
});

// State
let tasks = ref([]);
const selectedTasks = ref([]);
const expandedSections = ref({
  pastDates: true,
  today: true,
  thisWeek: true,
  nextweekTasks:true,
  noDueDate :true
});
const showTaskOverlay = ref(false);
const currentTask = ref(null);
const movableSections = ref([]);

// Fetch tasks on mount
onMounted(() => {
  fetchTasks();
});


let fetchTasks = () => {
  console.log('fetching tasks')
  const tasksResource = createResource({
    url: 'inspira.inspira.api.tasks.tasks.get_tasks',
    makeParams: () => ({
      user_id: sessionUser(),
    }),
    auto: true,
    onSuccess: (data) => {
      console.log('data', data)
      tasks.value = data;
    },
    onError: (error) => {
      console.error('Failed to fetch tasks:', error);
      toast({
        title: 'Error',
        text: error.messages?.[0] || 'Failed to fetch tasks',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      });
    },
  });
};

defineExpose({
  fetchTasks
});


// fetchTasks = () => {
//   tasks.value = [
//     {
//       id: '1',
//       subject: 'Complete project proposal',
//       group: 'Personal',
//       status: 'To Do',
//       priority: 'High',
//       planned_start_date: '2025-05-01',
//       planned_end_date: '2025-05-01',
//       actual_start_date: null,
//       actual_end_date: null,
//       assignees: [
//         { value: 'user1', label: 'John Doe' }
//       ],
//       remarks: 'Need to finalize the scope'
//     },
//   ];
// };


// Filter tasks by date categories
const pastTasks = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  return tasks.value.filter(task => {
    if (!task.planned_end_date) return false;
    const taskDate = new Date(task.planned_end_date);
    return taskDate < today;
  });
});

// const todayTasks = computed(() => {
//   const today = new Date();
//   today.setHours(0, 0, 0, 0);
//   const tomorrow = new Date(today);
//   tomorrow.setDate(tomorrow.getDate() + 1);
  
//   return tasks.value.filter(task => {
//     if (!task.planned_end_date) return false;
//     const taskDate = new Date(task.planned_end_date);
//     return taskDate >= today && taskDate < tomorrow;
//   });
// });

// const weekTasks = computed(() => {
//   const today = new Date();
//   today.setHours(0, 0, 0, 0);
//   const tomorrow = new Date(today);
//   tomorrow.setDate(tomorrow.getDate() + 1);
//   const nextWeek = new Date(today);
//   nextWeek.setDate(nextWeek.getDate() + 7);
  
//   return tasks.value.filter(task => {
//     if (!task.planned_end_date) return false;
//     const taskDate = new Date(task.planned_end_date);
//     return taskDate >= tomorrow && taskDate < nextWeek;
//   });
// });
const weekTasks = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // normalize time to start of day

  const sevenDaysFromToday = new Date(today);
  sevenDaysFromToday.setDate(sevenDaysFromToday.getDate() + 7);

  return tasks.value.filter(task => {
    if (!task.planned_end_date) return false;
    const taskDate = new Date(task.planned_end_date);
    taskDate.setHours(0, 0, 0, 0); // normalize task date
    return taskDate >= today && taskDate < sevenDaysFromToday;
  });
});

const nextweekTasks = computed(() => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // normalize to start of today

  const sevenDaysFromToday = new Date(today);
  sevenDaysFromToday.setDate(sevenDaysFromToday.getDate() + 7);

  return tasks.value.filter(task => {
    if (!task.planned_end_date) return false;
    const taskDate = new Date(task.planned_end_date);
    taskDate.setHours(0, 0, 0, 0); // normalize task date
    return taskDate >= sevenDaysFromToday;
  });
});

const noDueDate = computed(() => {
  return tasks.value.filter(task => (!task.planned_end_date && task.assignees.every(a => a.value)));
});



// Section toggle
const toggleSection = (section) => {
  expandedSections.value[section] = !expandedSections.value[section];
};

// Task selection
const handleTaskSelected = (taskIds) => {
  selectedTasks.value = taskIds;
};

// Task click
const handleTaskClicked = (task) => {
  currentTask.value = task;
  showTaskOverlay.value = true;
};

// Handle overlay close
const handleTaskOverlayClose = () => {
  showTaskOverlay.value = false;
};

// Task save
const handleTaskSave = (task) => {
  console.log('Task updated:', task);
  showTaskOverlay.value = false;
  fetchTasks();

};

// Duplicate tasks
const handleDuplicate = () => {
  createResource({
    url: '/api/method/inspira.inspira.api.projects.project_planner.duplicate_tasks_with_children',
    params: {
      task_ids: selectedTasks.value,
    },
    auto: true,
    onSuccess(data) {
      fetchTasks();
      selectedTasks.value = [];
      toast({
        title: 'Success',
        text: 'Tasks duplicated successfully',
        icon: 'check-circle',
        position: 'bottom-right',
        iconClasses: 'text-green-500',
      });
    },
    onError(err) {
      toast({
        title: 'Error Occurred',
        text: err.messages?.[0] || 'An unknown error occurred',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      });
    },
  });
};

// Delete tasks
const handleDelete = () => {
  createResource({
    url: '/api/method/inspira.inspira.api.projects.project_planner.delete_tasks_with_children',
    params: {
      task_ids: selectedTasks.value,
    },
    auto: true,
    onSuccess(data) {
      fetchTasks();
      selectedTasks.value = [];
      toast({
        title: 'Success',
        text: 'Tasks deleted successfully',
        icon: 'check-circle',
        position: 'bottom-right',
        iconClasses: 'text-green-500',
      });
    },
    onError(err) {
      toast({
        title: 'Error Occurred',
        text: err.messages?.[0] || 'An unknown error occurred',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      });
    },
  });
};

// Set movable sections
const setMovableSections = () => {
  movableSections.value = [
    { label: 'Past Due', value: 'pastDates' },
    { label: 'Today', value: 'today' },
    { label: 'This Week', value: 'thisWeek' },
    { label: 'Due Later', value: 'nextweekTasks' },
    { label: 'No Due Date', value: 'noDueDate' }
  ];
};

// Move tasks
const handleMove = (targetSection) => {
  // Implementation would depend on your API
  console.log('Move tasks to section:', targetSection);
  
  // Example implementation
  const bulkUpdateParams = selectedTasks.value.map(taskId => ({
    doctype: 'IDP Task',
    docname: taskId,
    // Update fields based on target section
  }));
  
  // Call API to update tasks
  toast({
    title: 'Success',
    text: 'Tasks moved successfully',
    icon: 'check-circle',
    position: 'bottom-right',
    iconClasses: 'text-green-500',
  });
  
  fetchTasks();
  selectedTasks.value = [];
};

// Clear selection
const clearSelection = () => {
  selectedTasks.value = [];
};
</script>
<style scoped>
.overflow-auto{
  scrollbar-width: thin;
}
</style>