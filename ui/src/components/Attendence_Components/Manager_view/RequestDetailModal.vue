<template>
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-lg max-w-xl w-full max-h-[90vh] mx-4">
        <!-- Modal Header -->
        <div class="p-4 border-b">
          <h3 class="text-lg font-medium text-gray-700">{{ request.type }}</h3>
        </div>
        
        <!-- Modal Content -->
        <div class="p-4">
          <!-- Shift Time -->
          <div class="mb-4 flex">
            <div class="w-1/2">
              <div class="text-sm text-gray-500">Shift</div>
              <div class="font-medium">{{ request.details.shift || 'N/A' }}</div>
            </div>
            
            <div v-if="request.type === 'Regularization'" class="w-1/2">
              <div class="grid grid-cols-2 gap-2">
                <div>
                  <div class="text-sm text-gray-500">Check in time</div>
                  <div class="font-medium">{{ request.details.checkInTime }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Check out time</div>
                  <div class="font-medium">{{ request.details.checkOutTime }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Attendance Adjustment (for Regularization) -->
          <div v-if="request.type === 'Regularization'" class="mb-4">
            <div class="text-sm text-gray-500 mb-2">Attendance Adjustment</div>
            
            <div class="grid grid-cols-2 gap-4">
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-1">
                  <div class="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center text-green-600">
                    <Check class="w-3 h-3" />
                  </div>
                  <!-- <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                    <Plus class="w-3 h-3" />
                  </div> -->
                </div>
                <div class="border rounded-md flex items-center">
                  <input type="text" disabled :value="request.details.adjustedCheckIn" class="px-2 py-1 w-24 text-sm rounded border-gray-300" />
                  <button class="p-1 text-gray-400">
                    <Clock class="w-4 h-4" />
                  </button>
                </div>
              </div>
              
              <div class="flex items-center gap-2">
                <div class="flex items-center gap-1">
                  <div class="w-5 h-5 rounded-full bg-red-100 flex items-center justify-center text-red-600">
                    <ArrowUp class="w-3 h-3" />
                  </div>
                  <!-- <div class="w-5 h-5 rounded-full bg-gray-200 flex items-center justify-center text-gray-500">
                    <Minus class="w-3 h-3" />
                  </div> -->
                </div>
                <div class="border rounded-md flex items-center">
                  <input type="text" disabled :value="request.details.adjustedCheckOut" class="px-2 py-1 w-24 text-sm rounded border-gray-300" />
                  <button class="p-1 text-gray-400">
                    <Clock class="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Location (for Outdoor requests) -->
          <div v-if="request.details.location" class="mb-4">
            <div class="text-sm text-gray-500">Location</div>
            <div class="font-medium">{{ request.details.location }}</div>
          </div>
          
          <!-- Note -->
          <div class="mb-4">
            <div class="text-sm text-gray-500 mb-1">Note</div>
            <div class="border rounded-md p-3 text-sm bg-gray-50 min-h-[80px]">
              {{ request.details.note || 'No notes provided.' }}
            </div>
          </div>
          
          <!-- Attachments -->
          <div v-if="request.details.attachments && request.details.attachments.length > 0" class="mb-4">
            <div class="text-sm text-gray-500 mb-1">Attachment</div>
            <div v-for="(attachment, index) in request.details.attachments" :key="index" 
                 class="flex items-center gap-2 p-2 border rounded-md">
              <FileText class="w-4 h-4 text-gray-500" />
              <span class="text-sm">{{ attachment }}</span>
            </div>
          </div>
        </div>
        
        <!-- Modal Footer -->
        <div class="p-4 border-t flex justify-end gap-2 text-sm">
          <button 
            @click="$emit('approve', request)" 
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center gap-1"
          >
            <Check class="w-4 h-4" />
            Approve
          </button>
          <button 
            @click="$emit('reject', request)" 
            class="px-4 py-2 bg-red-600 border border-gray-300 text-gray-100 rounded-md hover:bg-red-700 flex items-center gap-1"
          >
            <X class="w-4 h-4" />
            Reject
          </button>
          <button 
            @click="$emit('close')" 
            class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { Check, X, Clock, Plus, Minus, ArrowUp, FileText } from 'lucide-vue-next';
  
  const props = defineProps({
    request: {
      type: Object,
      required: true
    }
  });
  
  defineEmits(['close', 'approve', 'reject']);
  </script>