<template>
    <div class="attendance-calendar">
      <div class="calendar-grid border border-gray-200 rounded-md overflow-hidden">
        <!-- Days of week header -->
        <div class="grid grid-cols-7 text-center border-b">
          <div v-for="day in daysOfWeek" :key="day" class="py-2 text-xs font-medium text-gray-500">
            {{ day }}
          </div>
        </div>
  
        <!-- Calendar days -->
        <div class="grid grid-cols-7">
          <div 
            v-for="(day, index) in calendarDays" 
            :key="index" 
            :class="[
              'border-r border-b min-h-[100px] relative',
              day.isCurrentMonth ? '' : 'bg-gray-50 text-gray-400',
              index % 7 === 6 ? 'border-r-0' : '',
              index >= calendarDays.length - 7 ? 'border-b-0' : ''
            ]"
            @click="selectDay(day)"
          >
            <div class="p-1 text-xs text-gray-700">
              {{ day.dayNumber }}
            </div>
  
            <!-- Shift data for this day -->
            <div v-if="day.isCurrentMonth" class="p-1 space-y-1">
              <div 
                v-for="(event, eventIndex) in day.events" 
                :key="eventIndex"
                :class="[
                  'text-sm p-2 rounded',
                  event.colorClass
                ]"
              >
                {{ event.title }}
                <div v-if="event.time" class="text-[10px]">{{ event.time }}</div>
              </div>
              
              <!-- WO indicator -->
              <div v-if="day.isWO" class="text-xs p-1 text-gray-500  font-medium">
                WO
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  
  const props = defineProps({
    events: {
      type: Array,
      default: () => []
    },
    currentMonth: {
      type: Date,
      default: () => new Date()
    }
  });
  
  const emit = defineEmits(['date-select']);
  
  // Calendar state
  const selectedDay = ref(null);
  const calendarDays = ref([]);
  const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  
  // Select a day
  const selectDay = (day) => {
    if (!day.isCurrentMonth) return;
  
    // Deselect previously selected day
    if (selectedDay.value) {
      selectedDay.value.isSelected = false;
    }
  
    // Select new day
    day.isSelected = true;
    selectedDay.value = day;
  
    // Emit event with selected date and data
    emit('date-select', {
      date: day.date,
      events: day.events
    });
  };
  
  // Find events for a specific date
  const findEventsForDate = (date) => {
    return props.events.filter(event => {
      return event.date.toDateString() === date.toDateString();
    });
  };
  
  // Generate calendar days
  const generateCalendarDays = () => {
    const year = props.currentMonth.getFullYear();
    const month = props.currentMonth.getMonth();
  
    // Get first day of month
    const firstDay = new Date(year, month, 1);
    const firstDayOfWeek = firstDay.getDay();
  
    // Get last day of month
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
  
    // Get days from previous month
    const prevMonthLastDay = new Date(year, month, 0).getDate();
    const prevMonthDays = [];
  
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const day = prevMonthLastDay - i;
      const date = new Date(year, month - 1, day);
  
      prevMonthDays.push({
        date,
        dayNumber: day,
        isCurrentMonth: false,
        isToday: false,
        isSelected: false,
        events: [],
        isWO: false
      });
    }
  
    // Get days from current month
    const currentMonthDays = [];
    const today = new Date();
  
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const isToday = date.toDateString() === today.toDateString();
      const events = findEventsForDate(date);
      
      // Check if it's a weekend (Saturday or Sunday)
      const dayOfWeek = date.getDay();
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
      
      // Determine if it's a work-off day (WO)
      // In this example, we'll consider weekends as WO days if there are no events
      const isWO = isWeekend && events.length === 0;
  
      currentMonthDays.push({
        date,
        dayNumber: day,
        isCurrentMonth: true,
        isToday,
        isSelected: false,
        events,
        isWO
      });
    }
  
    // Get days from next month
    const nextMonthDays = [];
    const totalDaysShown = prevMonthDays.length + currentMonthDays.length;
    const daysToAdd = 42 - totalDaysShown; // 6 rows of 7 days
  
    for (let day = 1; day <= daysToAdd; day++) {
      const date = new Date(year, month + 1, day);
  
      nextMonthDays.push({
        date,
        dayNumber: day,
        isCurrentMonth: false,
        isToday: false,
        isSelected: false,
        events: [],
        isWO: false
      });
    }
  
    // Combine all days
    calendarDays.value = [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];
  };
  
  // Watch for changes in events or current month
  watch(() => props.events, () => {
    generateCalendarDays();
  }, { deep: true });
  
  watch(() => props.currentMonth, () => {
    generateCalendarDays();
  });
  
  // Initialize
  onMounted(() => {
    generateCalendarDays();
  });
  </script>