<template>
  <div class="attendance-manager">
    <!-- Month Navigation -->
    <div class="flex items-center mb-2">
      <div class="flex items-center gap-2">
        <button class="p-2 rounded hover:bg-gray-100" @click="previousMonth">
          <ChevronLeft class="w-5 h-5 text-gray-600" />
        </button>
        <h2 class="text-lg font-medium w-32 text-center">{{ formattedMonth }}</h2>
        <button class="p-2 rounded hover:bg-gray-100" @click="nextMonth">
          <ChevronRight class="w-5 h-5 text-gray-600" />
        </button>
      </div>
      <button class="px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
        @click="setCurrentMonth">
        This month
      </button>
      <button
        class="ml-4 px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
        @click="scrollToRequests">
        Pending Requests
      </button>
    </div>

    <div class="flex flex-col lg:flex-row gap-4">
      <!-- Employee Selection -->
      <div class="w-full lg:w-64 flex-shrink-0">
        <div class="relative">
          <input type="text" placeholder="Search Employee" v-model="searchQuery"
            class="w-full px-3 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-gray-300" />
          <ChevronDown class="absolute right-3 top-2.5 w-4 h-4 text-gray-400" />
        </div>

        <div class="mt-1 border border-gray-200 rounded-md overflow-auto h-[37rem]">
          <div v-for="(employee, index) in filteredEmployees" :key="employee.id" @click="selectEmployee(employee)"
            :class="[
              'flex items-center gap-3 p-6 cursor-pointer hover:bg-gray-50',
              selectedEmployee?.id === employee.id ? 'bg-gray-50' : '',
              index !== filteredEmployees.length - 1 ? 'border-b border-gray-200' : ''
            ]">
            <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-medium">
              {{ employee.initial }}
            </div>
            <div>
              <div class="font-medium text-sm">{{ employee.name }}</div>
              <div class="text-xs text-gray-500">{{ employee.role }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Calendar View -->
      <div class="flex-grow">
        <AttendanceCalendar :events="calendarEvents" :current-month="currentMonth" @date-select="handleDateSelect" />
      </div>
    </div>

    <!-- Pending Requests Section -->
    <div class="mt-8 scrollToRequests">
      <div class="flex items-center gap-2 mb-4">
        <h3 class="text-lg font-medium">Pending Requests</h3>
        <div class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
          {{ pendingRequests.length }}
        </div>
      </div>

      <div class="overflow-x-auto border rounded-md">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Request Type</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Requested On</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Reason</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="request in pendingRequests" :key="request.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ formatDate(request.date) }}</div>
                <div class="text-sm text-gray-500">{{ request.duration }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ request.type }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(request.requestedOn) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request.reason }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="{
                  'bg-yellow-100 text-yellow-800': request.status === 'Pending',
                  'bg-green-100 text-green-800': request.status === 'Approved',
                  'bg-red-100 text-red-800': request.status === 'Rejected'
                }">
                  {{ request.status }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end gap-2">
                  <button @click="approveRequest(request)"
                    class="p-1.5 rounded-full bg-green-50 text-green-600 hover:bg-green-100">
                    <Check class="w-5 h-5" />
                  </button>
                  <button @click="rejectRequest(request)"
                    class="p-1.5 rounded-full bg-red-50 text-red-600 hover:bg-red-100">
                    <X class="w-5 h-5" />
                  </button>
                  <button @click="viewRequestDetails(request)"
                    class="p-1.5 rounded-full bg-gray-50 text-gray-600 hover:bg-gray-100">
                    <fillViewIcon class="w-4 h-4" />
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="pendingRequests.length === 0">
              <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                No requests found
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Request Detail Modal -->
    <RequestDetailModal v-if="showDetailModal" :request="selectedRequest" @close="showDetailModal = false"
      @approve="approveRequest" @reject="rejectRequest" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { format, addMonths, subMonths } from 'date-fns';
import { Check, X, ChevronLeft, ChevronRight, ChevronDown, ArrowUpRight } from 'lucide-vue-next';
import AttendanceCalendar from './Manager_view/AttendanceCalendar.vue';
import RequestDetailModal from './Manager_view/RequestDetailModal.vue';
import fillViewIcon from '../icons/fillViewIcon.vue'
import { sessionUser } from '../../data/session'
import { DatePicker, Autocomplete, createResource,toast } from 'frappe-ui';

const login_user = sessionUser()
// State
const currentMonth = ref(new Date());
const searchQuery = ref('');
const selectedEmployee = ref(null);
const showDetailModal = ref(false);
const selectedRequest = ref(null);

// Sample data - replace with API calls in production
const employees = ref([
  // { id: 1, name: 'Akash Tom', role: 'Administrative Officer', initial: 'A' },
  // { id: 2, name: 'Florence Harmono', role: 'Manager', initial: 'F' },
  // { id: 3, name: 'Steve Merkel Lews', role: 'Associate', initial: 'S' },
  // { id: 4, name: 'Jornad Jibli', role: 'Analyst', initial: 'J' },
  // { id: 5, name: 'Joba Israe', role: 'Associate', initial: 'J' },
  // { id: 6, name: 'Moga Bernard', role: 'Administrative Assistant', initial: 'M' },
]);

// Sample shift types
const shiftTypes = {
  GENERAL: { name: 'General', color: 'bg-blue-50 text-blue-700' },
  MORNING: { name: 'Morning', color: 'bg-yellow-50 text-yellow-700' },
  NIGHT: { name: 'Night Shift', color: 'bg-purple-50 text-purple-700' },
  AFTERNOON: { name: 'Afternoon', color: 'bg-green-50 text-green-700' },
  EARLY_MORNING: { name: 'Early Morning', color: 'bg-pink-50 text-pink-700' },
  EVENING: { name: 'Evening', color: 'bg-indigo-50 text-indigo-700' },
  FABRICATOR: { name: 'Fabrikator', color: 'bg-teal-50 text-teal-700' },
  LEAVE: { name: 'Privilege Leave', color: 'bg-orange-50 text-orange-700' },
  LEAVE_WITHOUT_PAY: { name: 'Leave Without Pay', color: 'bg-red-50 text-red-700' },
  OVERNIGHT: { name: 'Overnight', color: 'bg-gray-50 text-gray-700' },
  WO: { name: 'WO', color: 'bg-gray-50 text-gray-500' },
};

// Sample calendar events data - would be fetched from API based on employee and month
const employeeShifts = ref({
  // 1: [ // Akash Tom
  //   { date: '2025-02-01', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-02', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-05', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-06', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-07', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-08', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-09', type: 'GENERAL', time: '9:00 - 18:00' },
  //   { date: '2025-02-12', type: 'GENERAL', time: '9:00 - 18:00' },
  // ],
  // 2: [ // Florence Harmono
  //   { date: '2025-02-01', type: 'MORNING', time: '11:00 - 15:00' },
  //   { date: '2025-02-02', type: 'MORNING', time: '11:00 - 15:00' },
  //   { date: '2025-02-05', type: 'MORNING', time: '11:00 - 15:00' },
  //   { date: '2025-02-07', type: 'LEAVE', time: '' },
  //   { date: '2025-02-08', type: 'LEAVE', time: '' },
  //   { date: '2025-02-09', type: 'NIGHT', time: '22:00 - 2:00' },
  // ],
  // 3: [ // Steve Merkel Lews
  //   // No shifts
  // ],
  // 4: [ // Jornad Jibli
  //   { date: '2025-02-01', type: 'AFTERNOON', time: '15:00 - 23:30' },
  //   { date: '2025-02-02', type: 'AFTERNOON', time: '15:00 - 23:30' },
  //   { date: '2025-02-07', type: 'AFTERNOON', time: '15:00 - 23:30' },
  //   { date: '2025-02-08', type: 'AFTERNOON', time: '15:00 - 23:30' },
  //   { date: '2025-02-05', type: 'EARLY_MORNING', time: '1:00 - 6:00' },
  //   { date: '2025-02-06', type: 'EARLY_MORNING', time: '1:00 - 6:00' },
  //   { date: '2025-02-12', type: 'EARLY_MORNING', time: '1:00 - 6:00' },
  // ],
  // 5: [ // Joba Israe
  //   { date: '2025-02-05', type: 'EARLY_MORNING', time: '1:00 - 6:00' },
  //   { date: '2025-02-06', type: 'EARLY_MORNING', time: '1:00 - 6:00' },
  //   { date: '2025-02-07', type: 'EARLY_MORNING', time: '1:00 - 6:00' },
  //   { date: '2025-02-07', type: 'FABRICATOR', time: '8:45 - 15:45' },
  //   { date: '2025-02-08', type: 'FABRICATOR', time: '8:45 - 15:45' },
  //   { date: '2025-02-06', type: 'FABRICATOR', time: '8:45 - 15:45' },
  // ],
  // 6: [ // Moga Bernard
  //   { date: '2025-02-01', type: 'LEAVE_WITHOUT_PAY', time: '' },
  //   { date: '2025-02-02', type: 'LEAVE_WITHOUT_PAY', time: '' },
  //   { date: '2025-02-05', type: 'LEAVE_WITHOUT_PAY', time: '' },
  //   { date: '2025-02-06', type: 'LEAVE_WITHOUT_PAY', time: '' },
  //   { date: '2025-02-07', type: 'EVENING', time: '15:00 - 18:00' },
  //   { date: '2025-02-08', type: 'EVENING', time: '15:00 - 18:00' },
  //   { date: '2025-02-12', type: 'EVENING', time: '15:00 - 18:00' },
  //   { date: '2025-02-06', type: 'OVERNIGHT', time: '' },
  //   { date: '2025-02-07', type: 'OVERNIGHT', time: '' },
  //   { date: '2025-02-08', type: 'OVERNIGHT', time: '' },
  // ],
});

// Sample pending requests
const pendingRequests = ref([
  // {
  //   id: 1,
  //   employeeId: 3,
  //   date: new Date('2025-03-27'),
  //   duration: '(1 day)',
  //   type: 'Regularization',
  //   requestedOn: new Date('2025-01-10'),
  //   reason: 'Power cut',
  //   status: 'Pending',
  //   details: {
  //     shift: '09:50 - 18:00',
  //     checkInTime: '--:--',
  //     checkOutTime: '--:--',
  //     adjustedCheckIn: '09:50 AM',
  //     adjustedCheckOut: '18:00 AM',
  //     note: 'There was a power outage in my area which caused internet connectivity issues.',
  //     attachments: ['power_outage_notice.pdf']
  //   }
  // },
  // {
  //   id: 2,
  //   employeeId: 2,
  //   date: new Date('2025-02-15'),
  //   duration: '(1 day)',
  //   type: 'WFH Request',
  //   requestedOn: new Date('2025-02-10'),
  //   reason: 'Family emergency',
  //   status: 'Pending',
  //   details: {
  //     shift: '11:00 - 15:00',
  //     note: 'Need to work from home due to a family emergency.',
  //   }
  // },
  // {
  //   id: 3,
  //   employeeId: 4,
  //   date: new Date('2025-02-20'),
  //   duration: '(1 day)',
  //   type: 'Outdoor Duty',
  //   requestedOn: new Date('2025-02-12'),
  //   reason: 'Client meeting',
  //   status: 'Pending',
  //   details: {
  //     shift: '15:00 - 23:30',
  //     location: 'Client Office - Downtown',
  //     note: 'Meeting with client to discuss project requirements.',
  //   }
  // },
  // {
  //   id: 4,
  //   employeeId: 1,
  //   date: new Date('2025-02-18'),
  //   duration: '(1 day)',
  //   type: 'Outdoor Site Request',
  //   requestedOn: new Date('2025-02-14'),
  //   reason: 'Site inspection',
  //   status: 'Pending',
  //   details: {
  //     shift: '09:00 - 18:00',
  //     location: 'Construction Site - North District',
  //     note: 'Need to inspect the construction progress.',
  //   }
  // },
]);

// Computed properties
const formattedMonth = computed(() => {
  return format(currentMonth.value, 'MMMM, yyyy');
});

const filteredEmployees = computed(() => {
  if (!searchQuery.value) return employees.value;

  const query = searchQuery.value.toLowerCase();
  return employees.value.filter(emp =>
    emp.name.toLowerCase().includes(query) ||
    emp.role.toLowerCase().includes(query)
  );
});

const calendarEvents = computed(() => {
  if (!selectedEmployee.value) return [];

  const employeeId = selectedEmployee.value.id;
  const shifts = employeeShifts.value[employeeId] || [];

  // Transform shifts into calendar events
  return shifts.map(shift => {
    const shiftInfo = shiftTypes[shift.type] || { name: shift.type, color: 'bg-gray-100' };

    return {
      date: new Date(shift.date),
      title: shiftInfo.name,
      time: shift.time,
      colorClass: shiftInfo.color,
      type: shift.type
    };
  });
});

// Methods
const previousMonth = () => {
  currentMonth.value = subMonths(currentMonth.value, 1);
};

const nextMonth = () => {
  currentMonth.value = addMonths(currentMonth.value, 1);
};

const setCurrentMonth = () => {
  currentMonth.value = new Date();
};

const selectEmployee = (employee) => {
  selectedEmployee.value = employee;
};

const handleDateSelect = (dateInfo) => {
  console.log('Selected date:', dateInfo);
  // Handle date selection - could show details, etc.
};

const formatDate = (date) => {
  return format(date, 'MMM dd, yyyy');
};

const approveRequest = (request) => {
  // In a real app, you would call an API to approve the request
  const index = pendingRequests.value.findIndex(r => r.id === request.id);


  const approve_requst = createResource({
    url: 'frappe.client.set_value',
    makeParams: () => ({
      doctype: request.doctype,
      name: request.id,  // assumes request has the document name
      fieldname: {
        docstatus: 1
      }
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log("Employee Shifts", res)
      if (index !== -1) {
        pendingRequests.value[index].status = 'Approved';
        // You might want to remove it from the pending list or update UI
        pendingRequests.value = pendingRequests.value.filter(r => r.id !== request.id);
      }
      showDetailModal.value = false;
      toast({
          title: 'Success',
          text: 'Request approved sucessfully',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-green-500',
        })
    },
    onError: (error) => {
      // console.log(error)
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    },
  })
}

// console.log(request)


const rejectRequest = (request) => {
  // In a real app, you would call an API to reject the request
  const index = pendingRequests.value.findIndex(r => r.id === request.id);

  console.log(request)

  const reject_requst = createResource({
    url: 'frappe.client.set_value',
    makeParams: () => ({
      doctype: request.doctype,
      name: request.id,  // assumes request has the document name
      fieldname: {
        workflow_state: "Rejected"
      }
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log("Employee Shifts", res)
      if (index !== -1) {
        pendingRequests.value[index].status = 'Rejected';
        // You might want to remove it from the pending list or update UI
        pendingRequests.value = pendingRequests.value.filter(r => r.id !== request.id);
      }
      showDetailModal.value = false;
      toast({
          title: 'Success',
          text: 'Request rejected sucessfully',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-green-500',
        })
    },
    onError: (error) => {
      // console.log(error)
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    },
  })
}


const viewRequestDetails = (request) => {
  selectedRequest.value = request;
  showDetailModal.value = true;
};
const scrollToRequests = () => {
  document.querySelector('.scrollToRequests').scrollIntoView({ behavior: 'smooth' });
};
function get_atttendance_request() {
  const attendance_requests = createResource({
    url: 'inspira.inspira.api.attendance.attendance.get_attendance_requests_reporting_to_user',
    makeParams: () => ({
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log("Res", res)
      pendingRequests.value = res
    },
    onError: (error) => {
      console.log(error)
    },
  })
}
function get_employee_list() {
  const employee_list = createResource({
    url: 'inspira.inspira.api.attendance.attendance.get_employees_reporting_to_user',
    makeParams: () => ({
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log("Employee List", res)
      employees.value = res
    },
    onError: (error) => {
      console.log(error)
    },
  })
}
function get_employee_shifts() {
  const employee_shifts = createResource({
    url: 'inspira.inspira.api.attendance.attendance.get_shift_data_for_reportees',
    makeParams: () => ({
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log("Employee Shifts", res)
      // employees.value = res
      employeeShifts.value = res
    },
    onError: (error) => {
      console.log(error)
    },
  })
}
onMounted(() => {
  get_atttendance_request()
  get_employee_list()
  get_employee_shifts()
})
</script>