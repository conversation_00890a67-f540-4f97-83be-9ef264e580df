<template>
  <div class="h-full">
    <!-- Header with date and status -->
    <div class="flex justify-between items-center mb-4 p-4 bg-[#DED8E1]">
      <div class="text-base font-medium text-[#574E60]">
        {{ formattedDate }}
      </div>
      <div class="flex gap-2">
        <span class="px-3 py-1 text-xs rounded-full" :class="getStatusClass(selectedDateData.status)">
          {{ getStatusText(selectedDateData.status) }}
        </span>
        <span v-if="isWeekend" class="px-3 py-1 text-xs bg-gray-200 text-gray-700 rounded-full">
          Weekly Off
        </span>
      </div>
    </div>

    <!-- Shift information grid -->
    <div class="grid grid-cols-1 p-4 bg-white rounded-md">

      <div class="flex flex-col gap-4">
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Shift:</span>
          <span class="font-medium text-sm">{{shift_time}}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Check in time:</span>
          <span class="font-medium text-sm">{{ selectedDateData.checkIn || '--:--' }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Check out time:</span>
          <span class="font-medium text-sm">{{ selectedDateData.checkOut || '--:--' }}</span>
        </div>
        <!-- <div class="flex justify-between">
          <span class="text-sm text-gray-500">Comp - off generation:</span>
          <span class="font-medium text-sm">--:--</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Overtime:</span>
          <span class="font-medium text-sm">--:--</span>
        </div> -->
      </div>
    </div>

    <!-- Clock-in times collapsible section -->
    <div class="border-t pt-4 p-4">
      <div class="flex justify-between items-center mb-4 cursor-pointer" @click="toggleClockTimes">
        <h3 class="font-medium text-sm">Clock-in Times</h3>
        <button class="text-gray-500">
          <ChevronDownIcon v-if="!showClockTimes" class="h-4 w-4" />
          <ChevronUpIcon v-else class="h-4 w-4" />
        </button>
      </div>

      <div v-if="showClockTimes" class="grid grid-cols-2 gap-4">
  
  <!-- Left Column: All Check-ins -->
  <div>
    <!-- <h3 class="font-semibold mb-2 text-green-600">Check-ins</h3> -->
    <div v-for="(checkin, index) in selectedDateCheckins.checkins" :key="'in' + index" class="mb-3">
      <div class="flex items-center">
        <div class="text-green-500 mr-2">
          <ArrowDownLeftIcon class="w-5 h-5" />
        </div>
        <div class="relative flex-1">
          <input type="text" :value="formatTimeForDisplay(checkin)" readonly
            class="w-full border rounded-md py-2 px-4 bg-white text-sm" />
          <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500">
            <ClockIcon class="h-4 w-4" />
          </span>
        </div>
      </div>
    </div>
  </div>

  <!-- Right Column: All Check-outs -->
  <div>
    <!-- <h3 class="font-semibold mb-2 text-red-600">Check-outs</h3> -->
    <div v-for="(checkout, index) in selectedDateCheckins.checkouts" :key="'out' + index" class="mb-3">
      <div class="flex items-center">
        <div class="text-red-500 mr-2">
          <ArrowUpRightIcon class="w-5 h-5" />
        </div>
        <div class="relative flex-1">
          <input type="text" :value="formatTimeForDisplay(checkout)" readonly
            class="w-full border rounded-md py-2 px-4 bg-white text-sm" />
          <span class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500">
            <ClockIcon class="h-4 w-4" />
          </span>
        </div>
      </div>
    </div>
  </div>

</div>

<!-- No records found -->
<div v-if="selectedDateCheckins.checkins.length === 0 && selectedDateCheckins.checkouts.length === 0" class="text-gray-500 text-sm mt-4">
  No check-in or check-out records found.
</div>


    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted ,watch} from 'vue';
import { ChevronDownIcon, ChevronUpIcon, ClockIcon, ArrowDownLeftIcon, ArrowUpRightIcon } from 'lucide-vue-next';
import { createResource } from 'frappe-ui';

const props = defineProps({
  selectedDateData: {
    type: Object,
    required: true
  }
});
const shift_time = ref('')
// UI state
const showClockTimes = ref(true);

// Toggle sections
const toggleClockTimes = () => {
  showClockTimes.value = !showClockTimes.value;
};

// Format date for display
const formattedDate = computed(() => {
  if (!props.selectedDateData.date) return '';

  const date = new Date(props.selectedDateData.date);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: '2-digit',
    year: 'numeric'
  });
});

// Check if selected date is a weekend
const isWeekend = computed(() => {
  if (!props.selectedDateData.date) return false;

  const date = new Date(props.selectedDateData.date);
  const day = date.getDay();
  return day === 0 || day === 6;
});

// Get status class for badge
const getStatusClass = (status) => {
  switch (status) {
    case 'present':
      return 'bg-green-100 text-green-800';
    case 'absent':
      return 'bg-red-100 text-red-800';
    case 'on leave':
      return 'bg-yellow-100 text-yellow-800';
    case 'holiday':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Get status text for badge
const getStatusText = (status) => {
  switch (status) {
    case 'present':
      return 'Present';
    case 'absent':
      return 'Absent';
    case 'holiday':
      return 'Holiday';
    default:
      return status;
  }
};

// Format time for display
// const formatTimeForDisplay = (time) => {
//   if (!time) return '--:--';

//   if (time.includes('AM') || time.includes('PM')) {
//     return time;
//   }

//   const [hours, minutes] = time.split(':').map(Number);

//   const period = hours >= 12 ? 'PM' : 'AM';
//   const hours12 = hours % 12 || 12;

//   const formattedHours = hours12.toString().padStart(2, '0');
//   const formattedMinutes = minutes.toString().padStart(2, '0');

//   return `${formattedHours}:${formattedMinutes} ${period}`;
// };
const formatTimeForDisplay = (time) => {
  if (!time) return '--:--';

  // If time is already in correct 24-hour format, just return it.
  if (time.includes('AM') || time.includes('PM')) {
    // Optionally, you can convert to 24-hour format here if needed.
    const date = new Date('1970-01-01T' + time);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    return `${hours}:${minutes}`;
  }

  const [hours, minutes] = time.split(':').map(Number);

  const formattedHours = hours.toString().padStart(2, '0');
  const formattedMinutes = minutes.toString().padStart(2, '0');
  // const period = hours >= 12 ? 'PM' : 'AM';
  // return `${formattedHours}:${formattedMinutes} ${period}`;
  return `${formattedHours}:${formattedMinutes}`;
};

const selectedDateCheckins = ref({
  checkins: [],
    checkouts: []
})
function get_checkin_data() {
  return createResource({
    url: 'inspira.inspira.api.attendance.attendance.get_checkin_data',
    makeParams: () => ({
      employee: window.emp_id,
      date: props.selectedDateData.date
    }),
    auto: true,
    onSuccess: (resp) => {
      console.log(resp);
      // You can update your reactive data here
      selectedDateCheckins.value.checkins = resp.checkins;
      selectedDateCheckins.value.checkouts = resp.checkouts;
      shift_time.value = `${resp.start_time}-${resp.end_time}` 
    },
    onError: (error) => {
      console.error('Error fetching check-in data:', error);
    }
  });
}
watch(() => props.selectedDateData.date, (newDate, oldDate) => {
  if (newDate && newDate !== oldDate) {
    get_checkin_data();
  }
});
onMounted(() => {
  console.log("Attendance component mounted with data:", props.selectedDateData);
  get_checkin_data()
});
</script>