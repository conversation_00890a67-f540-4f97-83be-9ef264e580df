<template>
  <div class="w-full">
    <div class="flex gap-4 items-center p-4 border-b">
      <h2 class="text-lg font-medium">My Requests</h2>
      <div class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs">
        {{ filteredRequests.length }}
      </div>
      <!-- <div class="flex gap-2">
          <div class="relative">
            <select 
              v-model="filterType" 
              class="border rounded-md px-3 py-1 pr-8 appearance-none bg-white text-sm"
            >
              <option value="all">All Requests</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <ChevronDownIcon class="w-4 h-4 text-gray-500" />
            </div>
          </div>
        </div> -->
    </div>

    <!-- Requests Table -->
    <div class="overflow-x-auto border rounded-md">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Request Type</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Requested On</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Reason</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status</th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(request, index) in filteredRequests" :key="index" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ request.date }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ request.type }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request.requestedOn }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ request.reason }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="{
                'bg-yellow-100 text-yellow-800': request.status === 'Pending',
                'bg-green-100 text-green-800': request.status === 'Approved',
                'bg-red-100 text-red-800': request.status === 'Rejected'
              }">
                {{ request.status }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <div class="flex justify-end gap-2">
                <!-- <button @click="cancelRequest(request)" class="text-gray-600 hover:text-red-600">
                  <XCircleIcon class="w-5 h-5" />
                </button> -->
                <button @click="viewRequestDetails(request)" class="text-gray-600 hover:text-gray-900">
                  <fillViewIcon class="w-4 h-4" />
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="filteredRequests.length === 0">
            <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
              No requests found
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Request Detail Modal -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-xl w-full max-h-[90vh] overflow-y-auto text-sm">
        <div class="p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">Request Details</h3>
            <button @click="showDetailModal = false" class="text-gray-500 hover:text-gray-700">
              <XIcon class="w-5 h-5" />
            </button>
          </div>

          <div class="space-y-4" v-if="selectedRequest">
            <!-- Request Type and Status -->
            <div class="flex justify-between items-center">
              <div class="text-xl font-medium">{{ selectedRequest.type }}</div>
              <span class="px-3 py-1 text-sm font-semibold rounded-full" :class="{
                'bg-yellow-100 text-yellow-800': selectedRequest.status === 'Pending',
                'bg-green-100 text-green-800': selectedRequest.status === 'Approved',
                'bg-red-100 text-red-800': selectedRequest.status === 'Rejected'
              }">
                {{ selectedRequest.status }}
              </span>
            </div>

            <!-- Date Information -->
            <div class="grid grid-cols-2 gap-4 border-t border-b py-3">
              <div>
                <div class="text-sm text-gray-500">Date</div>
                <div class="font-medium">{{ selectedRequest.date || `${selectedRequest.fromDate} to
                  ${selectedRequest.toDate}` }}</div>
              </div>
              <div>
                <div class="text-sm text-gray-500">Requested On</div>
                <div class="font-medium">{{ selectedRequest.requestedOn }}</div>
              </div>
            </div>

            <!-- Request Specific Details -->
            <div class="space-y-3">
              <!-- For Regularization -->
              <div v-if="selectedRequest.type === 'Regularization'" class="grid grid-cols-2 gap-4">
                <div>
                  <div class="text-sm text-gray-500">Original Check-in</div>
                  <div>{{ selectedRequest.checkInOriginal || '--:--' }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Adjusted Check-in</div>
                  <div>{{ selectedRequest.checkInAdjusted }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Original Check-out</div>
                  <div>{{ selectedRequest.checkOutOriginal || '--:--' }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Adjusted Check-out</div>
                  <div>{{ selectedRequest.checkOutAdjusted }}</div>
                </div>
              </div>

              <!-- For Outdoor Duty -->
              <div v-if="selectedRequest.type === 'Outdoor Duty'" class="grid grid-cols-2 gap-4">
                <div>
                  <div class="text-sm text-gray-500">Day Type</div>
                  <div>{{ selectedRequest.dayType }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Work Period</div>
                  <div>{{ selectedRequest.workPeriod }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Hours</div>
                  <div>{{ selectedRequest.hours }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Project</div>
                  <div>{{ selectedRequest.project }}</div>
                </div>
              </div>

              <!-- For Site Visit -->
              <div v-if="selectedRequest.type === 'Site Visit'" class="grid grid-cols-2 gap-4">
                <div>
                  <div class="text-sm text-gray-500">Leave Type</div>
                  <div>{{ selectedRequest.leaveType }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Days</div>
                  <div>{{ selectedRequest.days }}</div>
                </div>
                <div v-if="selectedRequest.dayType === 'Custom'">
                  <div class="text-sm text-gray-500">From Day Part</div>
                  <div>{{ selectedRequest.fromDayPart }}</div>
                </div>
                <div v-if="selectedRequest.dayType === 'Custom'">
                  <div class="text-sm text-gray-500">To Day Part</div>
                  <div>{{ selectedRequest.toDayPart }}</div>
                </div>
              </div>

              <!-- For Work From Home -->
              <div v-if="selectedRequest.type === 'Work from Home'" class="grid grid-cols-2 gap-4">
                <div>
                  <div class="text-sm text-gray-500">Day Type</div>
                  <div>{{ selectedRequest.dayType }}</div>
                </div>
                <div>
                  <div class="text-sm text-gray-500">Hours</div>
                  <div>{{ selectedRequest.hours }}</div>
                </div>
              </div>

              <!-- Note/Reason -->
              <div>
                <div class="text-sm text-gray-500">Note/Reason</div>
                <div class="bg-gray-50 p-3 rounded-md">{{ selectedRequest.note || selectedRequest.reason }}</div>
              </div>

              <!-- Notified People -->
              <div v-if="selectedRequest.notifyEmployees && selectedRequest.notifyEmployees.length > 0">
                <div class="text-sm text-gray-500">Notified</div>
                <div class="flex flex-wrap gap-2 mt-1">
                  <div v-for="(employee, i) in selectedRequest.notifyEmployees" :key="i"
                    class="bg-gray-100 px-2 py-1 rounded-md text-xs">
                    {{ typeof employee === 'string' ? employee : employee.label }}
                  </div>
                </div>
              </div>

              <!-- Attachment -->
              <div v-if="selectedRequest.attachment">
                <div class="text-sm text-gray-500">Attachment</div>
                <div class="flex items-center gap-2 mt-1">
                  <PaperclipIcon class="w-4 h-4 text-gray-500" />
                  <a href="#" class="text-blue-600 hover:underline">{{ selectedRequest.attachment }}</a>
                </div>
              </div>
            </div>

            <!-- Approval Information -->
            <div v-if="selectedRequest.status !== 'Pending'" class="border-t pt-3">
              <div class="text-sm text-gray-500">{{ selectedRequest.status === 'Approved' ? 'Approved' : 'Rejected' }}
                by</div>
              <div class="flex items-center gap-2 mt-1">
                <div class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <UserIcon class="w-4 h-4 text-gray-500" />
                </div>
                <div>
                  <div class="font-medium">{{ selectedRequest.approver || '' }}</div>
                  <div class="text-xs text-gray-500">{{ selectedRequest.approvedOn || '' }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end gap-4 mt-6">
            <button class="px-6 py-1 rounded-full bg-gray-100 text-gray-700" @click="cancelRequest(selectedRequest)" v-if="selectedRequest.status == 'Pending'">
              Cancel Request
            </button>
            <button @click="showDetailModal = false" class="px-6 py-1 rounded-full bg-gray-100 text-gray-700">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed ,onMounted} from 'vue';
import {
  ChevronDownIcon,
  CheckCircleIcon,
  XCircleIcon,
  MaximizeIcon,
  XIcon,
  PaperclipIcon,
  UserIcon
} from 'lucide-vue-next';
import fillViewIcon from '../../icons/fillViewIcon.vue'
import { sessionUser } from '../../../data/session'
import { DatePicker, Autocomplete, createResource,toast } from 'frappe-ui';

const employee_name = ref(window.emp_id)
const login_user = sessionUser()

// Mock data for requests
const requests = ref([
  // {
  //   id: 1,
  //   type: 'Regularization',
  //   date: 'Mar 27, 2025',
  //   requestedOn: 'Jan 10, 2025',
  //   reason: 'Power cut',
  //   status: 'Pending',
  //   checkInOriginal: '--:--',
  //   checkOutOriginal: '--:--',
  //   checkInAdjusted: '09:50 AM',
  //   checkOutAdjusted: '18:00 PM',
  //   note: 'There was a power outage in my area which caused me to miss the check-in time.',
  //   notifyEmployees: [
  //     { label: 'Waseem Ahmed', value: '<EMAIL>' },
  //     { label: 'Sandeep kakde', value: '<EMAIL>' }
  //   ]
  // },
  // {
  //   id: 2,
  //   type: 'Outdoor Duty',
  //   date: 'Mar 25, 2025',
  //   requestedOn: 'Jan 08, 2025',
  //   reason: 'Client meeting',
  //   status: 'Approved',
  //   dayType: 'Full Day',
  //   workPeriod: 'Morning',
  //   hours: '8',
  //   project: 'Project A',
  //   note: 'Meeting with client to discuss project requirements.',
  //   approver: 'John Manager',
  //   approvedOn: '2025-01-09',
  //   notifyEmployees: [
  //     { label: 'John Doe', value: '<EMAIL>' }
  //   ]
  // },
  // {
  //   id: 3,
  //   type: 'Site Visit',
  //   fromDate: 'Mar 20, 2025',
  //   toDate: 'Mar 22, 2025',
  //   requestedOn: 'Jan 05, 2025',
  //   reason: 'Site inspection',
  //   status: 'Rejected',
  //   days: 3,
  //   leaveType: 'Casual Leave',
  //   dayType: 'Full Day',
  //   note: 'Need to visit the construction site for inspection.',
  //   approver: 'Jane Manager',
  //   approvedOn: '2025-01-06',
  //   notifyEmployees: [
  //     { label: 'Jane Smith', value: '<EMAIL>' }
  //   ]
  // },
  // {
  //   id: 4,
  //   type: 'Work from Home',
  //   date: 'Mar 15, 2025',
  //   requestedOn: 'Jan 02, 2025',
  //   reason: 'Internet installation',
  //   status: 'Approved',
  //   dayType: 'Full Day',
  //   hours: '8',
  //   note: 'Need to be at home for internet installation.',
  //   approver: 'John Manager',
  //   approvedOn: '2025-01-03',
  //   notifyEmployees: []
  // }
]);
// function formatDate(dateStr) {
//   if (!dateStr) return '';
//   const date = new Date(dateStr);
//   return format(date, 'MMM dd, yyyy');
// }
const formatDate = (date) =>
  new Date(date).toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
// UI state
const filterType = ref('all');
const showDetailModal = ref(false);
const selectedRequest = ref(null);

// Filtered requests based on selected filter
const filteredRequests = computed(() => {
  if (filterType.value === 'all') {
    return requests.value;
  }
  return requests.value.filter(request => request.status.toLowerCase() === filterType.value);
});

// View request details
const viewRequestDetails = (request) => {
  selectedRequest.value = request;
  showDetailModal.value = true;
};

// Cancel a request
function cancelRequest(request){
  console.log(request)
  const delete_request = createResource({
    url:"frappe.client.delete",
    makeParams:()=>({
      doctype:request.doctype,
      name:request.id
    }),
    auto:true,
    onSuccess:(resp)=>{
      // console.log("Document deleted sucessfully")
      toast({
            title: 'Success',
            text: 'Request cancelled sucessfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
      showDetailModal.value = false
      // requests.value = requests.value.filter(r => r.id !== request.id);
    },
    onError:(error)=>{
      // console.log("Error while deleting doc",error)
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    }
  })
  // if (confirm(`Are you sure you want to cancel this ${request.type} request?`)) {
  //   // In a real application, you would call an API to cancel the request
  //   console.log('Cancelling request:', request.id);
  //   // For demo purposes, we'll just remove it from the list
  //   requests.value = requests.value.filter(r => r.id !== request.id);
  // }
};

function get_attandance_request() {
  let attendanceData = []
  let outdoorData = []
  let attendanceFetched = false
  let outdoorFetched = false

  const mergeAndSetRequests = () => {
    const merged = [...attendanceData, ...outdoorData].map((req, index) => ({
      id: req.name,
      type: req.duty_type || 'Regularization',
      date: req.from_date ? formatDate(req.from_date) : formatDate(req.date),
      fromDate: req.from_date ? formatDate(req.from_date) : formatDate(req.date),
      toDate: req.to_date ? formatDate(req.to_date) : formatDate(req.date),
      requestedOn: formatDate(req.creation),
      reason: req.explanation || req.reason || '',
      status: req.workflow_state === "Draft" ? "Pending" : req.workflow_state,
      checkInOriginal: req.in_time || '--:--',
      checkOutOriginal: req.out_time || '--:--',
      checkInAdjusted: '--:--',
      checkOutAdjusted: '--:--',
      note: req.explanation || '',
      approver: req.approver,
      approvedOn: req.approvedOn,
      notifyEmployees: [],
      doctype: req.doctype
    }))
    requests.value = merged
  }

  // Fetch Attendance Request
  createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'Attendance Request',
      fields: [
        'name', 'employee_name', 'employee',
        'from_date', 'to_date', 'reason', 'creation', 'docstatus',
        'explanation', 'duty_type', 'approver as approver', 'approved_on as approvedOn', 'workflow_state'
      ],
      filters: [['employee', '=', employee_name.value],['docstatus','!=',2]],
    }),
    auto: true,
    onSuccess: (resp) => {
      attendanceData = resp.map(r => ({ ...r, doctype: 'Attendance Request' }))
      attendanceFetched = true
      if (outdoorFetched) mergeAndSetRequests()
    },
    onError: (error) => {
      console.error('Error fetching Attendance Requests:', error)
    },
  })

  // Fetch Outdoor Duty Request
  createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'IDP Outdoor Duty Request',
      fields: [
        'name', 'employee_name', 'employee',
        'date', 'reason', 'creation', 'docstatus',
        'duty_type', 'approver as approver', 'approved_on as approvedOn', 'workflow_state', 'in_time', 'out_time'
      ],
      filters: [['employee', '=', employee_name.value], ['docstatus', '=', 0]],
    }),
    auto: true,
    onSuccess: (resp) => {
      outdoorData = resp.map(r => ({ ...r, doctype: 'IDP Outdoor Duty Request' }))
      outdoorFetched = true
      if (attendanceFetched) mergeAndSetRequests()
    },
    onError: (error) => {
      console.error('Error fetching Outdoor Duty Requests:', error)
    },
  })
}

onMounted(() => {
  // const employee = createResource({
  //   url: 'frappe.client.get_value',
  //   makeParams: () => ({
  //     doctype: 'Employee',
  //     // user_id: login_user,
  //     filters: {
  //       user_id: login_user,
  //       status: 'Active'
  //     },
  //     fieldname: ['name']
  //   }),
  //   auto: true,
  //   onSuccess: (res) => {
  //     employee_name.value = res.name
  //     // console.log("EMployee Name",employee_name.value)
  //     get_attandance_request()
  //   },
  //   onError: (error) => {
  //     console.log(error)
  //   },
  // })
  get_attandance_request()
  console.log(employee_name.value)
  
});
</script>
<style scoped>
button{
  border: 0px !important;
}
</style>