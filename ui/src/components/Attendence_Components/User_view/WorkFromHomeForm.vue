<template>
  <div class="border rounded-md">
    <div class="flex justify-between items-center mb-4 p-4 bg-[#DED8E1]">
      <h2 class="text-base font-medium text-[#574E60]">Work from Home Request</h2>
    </div>

    <div class="space-y-4 p-4">
      <!-- Reason for request -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Reason for Request</label>
        <textarea 
          v-model="reason" 
          class="w-full border rounded p-2 text-sm min-h-[80px]" 
          placeholder="Type Here"
        ></textarea>
      </div>

      <!-- Date selection -->
      <div class="bg-white rounded-md">
        <div class="grid grid-cols-3 gap-2 items-center">
          <div>
            <label class="text-sm text-gray-600 block mb-1">From</label>
            <DatePicker 
              v-model="fromDate" 
              variant="subtle" 
              placeholder="Select date" 
              :disabled="false" 
              @update:model-value="updateToDate"
            />
          </div>

          <div class="flex items-center justify-center mt-4">
            <div class="bg-gray-100 rounded px-2 py-1 text-sm">
              {{ wfhDuration }} days
            </div>
          </div>

          <div>
            <label class="text-sm text-gray-600 block mb-1">To</label>
            <DatePicker 
              v-model="toDate" 
              variant="subtle" 
              placeholder="Select date" 
              :disabled="!fromDate"
              :min-date="fromDate"
            />
          </div>
        </div>
      </div>

      <!-- Day type selection with warning -->
      <div class="flex gap-2 items-center">
        <div class="flex justify-end bg-[#EEEAF4] rounded-sm p-1">
          <button 
            type="button" 
            v-for="type in ['Full Day', 'Custom']" 
            :key="type" 
            class="px-4 py-1 rounded-sm text-xs"
            :class="{ 'bg-white shadow-xl': dayType === type }" 
            @click="dayType = type"
          >
            {{ type }}
          </button>
        </div>
        
        <div v-if="wfhDuration > 0" class="flex items-center gap-1 text-xs text-red-500">
          <AlertCircleIcon class="w-4 h-4" />
          You are requesting {{ wfhDuration }} WFH days
        </div>
      </div>

      <!-- Custom time selection for each day -->
      <div v-if="dayType === 'Custom' && dateRange.length > 0" class="grid grid-cols-2 gap-4">
        <div v-for="(date, index) in dateRange" :key="index">
          <div class="text-sm mb-1">From {{ formatDate(date) }}</div>
          <div class="relative">
            <select 
              v-model="daySelections[index]" 
              class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm"
            >
              <option v-for="option in dayOptions" :key="option" :value="option">{{ option }}</option>
            </select>
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <ChevronDownIcon class="w-4 h-4 text-gray-500" />
            </div>
          </div>
        </div>
      </div>

      <!-- Note field -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Note</label>
        <textarea 
          v-model="note" 
          class="w-full border rounded p-2 text-sm min-h-[80px]" 
          placeholder="Type Here"
        ></textarea>
      </div>

      <!-- Notify field with Autocomplete -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Notify</label>
        <Autocomplete 
          v-model="notifyEmployees" 
          :options="employeeOptions" 
          placeholder="Search Employee" 
          :multiple="true"
          class="w-full" 
          option-label="label" 
          option-value="value" 
        />
      </div>

      <!-- Form actions -->
      <div class="flex gap-4 justify-end pt-2">
        <button 
          type="button" 
          @click="clearForm" 
          class="px-6 py-2 rounded-full bg-gray-600 text-white"
        >
          Clear
        </button>
        <button 
          @click="submitRequest" 
          type="submit" 
          class="px-6 py-2 rounded-full bg-[#5D5464] text-white"
        >
          Request
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { ChevronDownIcon, AlertCircleIcon } from 'lucide-vue-next';
import { DatePicker, Autocomplete } from 'frappe-ui';

// Define emits
const emit = defineEmits(['submitRequest', 'cancel']);

// Form state
const reason = ref('');
const fromDate = ref(null);
const toDate = ref(null);
const dayType = ref('Full Day');
const note = ref('');
const notifyEmployees = ref([]);
const daySelections = ref([]);
const dayOptions = ['First Half', 'Second Half', 'Full Day'];

// Options
const employeeOptions = [
  { label: 'Waseem Ahmed', value: '<EMAIL>' },
  { label: 'Sandeep kakde', value: '<EMAIL>' },
  { label: 'John Doe', value: '<EMAIL>' },
  { label: 'Jane Smith', value: '<EMAIL>' },
];

// Calculate WFH duration
const wfhDuration = computed(() => {
  if (!fromDate.value || !toDate.value) return 0;

  // Ensure fromDate and toDate are valid Date objects
  const from = new Date(fromDate.value);
  const to = new Date(toDate.value);

  // Calculate difference in days
  const diffTime = Math.abs(to - from);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays + 1; // Include both start and end days
});

// Generate array of dates between fromDate and toDate
const dateRange = computed(() => {
  if (!fromDate.value || !toDate.value) return [];

  const from = new Date(fromDate.value);
  const to = new Date(toDate.value);
  const dates = [];
  
  const current = new Date(from);
  while (current <= to) {
    dates.push(new Date(current));
    current.setDate(current.getDate() + 1);
  }
  
  return dates;
});

// Format date for display
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};

// Update toDate when fromDate changes
const updateToDate = () => {
  if (fromDate.value && (!toDate.value || new Date(toDate.value) < new Date(fromDate.value))) {
    toDate.value = fromDate.value;
  }
};

// Initialize day selections when date range changes
watch(dateRange, (newRange) => {
  // Reset day selections array
  daySelections.value = [];
  
  // Initialize with default values
  newRange.forEach((_, index) => {
    if (index === 0) {
      daySelections.value.push('First Half');
    } else if (index === newRange.length - 1) {
      daySelections.value.push('Second Half');
    } else {
      daySelections.value.push('Full Day');
    }
  });
}, { immediate: true });

// Form validation
const isFormValid = computed(() => {
  return reason.value.trim() !== '' && 
         fromDate.value && 
         toDate.value;
});

/**
 * Submit the work from home request
 */
const submitRequest = () => {
  if (!isFormValid.value) {
    alert('Please fill all required fields');
    return;
  }

  const request = {
    reason: reason.value,
    fromDate: formatDate(fromDate.value),
    toDate: formatDate(toDate.value),
    duration: wfhDuration.value,
    dayType: dayType.value,
    daySelections: dayType.value === 'Custom' ? daySelections.value : [],
    note: note.value,
    notifyEmployees: notifyEmployees.value,
    requestedOn: formatDate(new Date()),
    status: 'Pending'
  };

  console.log('Work From Home Request:', request);
  emit('submitRequest', request);
  clearForm();
};

/**
 * Clear the form fields
 */
const clearForm = () => {
  reason.value = '';
  fromDate.value = null;
  toDate.value = null;
  dayType.value = 'Full Day';
  note.value = '';
  notifyEmployees.value = [];
  daySelections.value = [];
};
</script>