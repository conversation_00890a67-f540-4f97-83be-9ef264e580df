<template>
    <div class="border rounded-md">
      <div class="flex justify-between items-center mb-4 p-4 bg-[#DED8E1]">
        <h2 class="text-base font-medium text-[#574E60]">Site Duty Request</h2>
      </div>
  
      <div class="space-y-4 p-4">
        <!-- Date Range Selection -->
        <div class="border rounded-md p-4">
          <div class="grid grid-cols-3 gap-4 items-center">
            <div>
              <label class="text-sm text-gray-600 block mb-1">From</label>
              <DatePicker 
                v-model="fromDate" 
                variant="subtle" 
                placeholder="Select date" 
                :disabled="false" 
              />
            </div>
            
            <div class="flex justify-center items-center">
              <div class="border rounded-md px-3 py-2 text-center">
                <span class="text-sm">{{ calculateDays }} days</span>
              </div>
            </div>
            
            <div>
              <label class="text-sm text-gray-600 block mb-1">To</label>
              <DatePicker 
                v-model="toDate" 
                variant="subtle" 
                placeholder="Select date" 
                :disabled="false" 
              />
            </div>
          </div>
        </div>
  
        <!-- Leave Type Selection -->
        <div>
          <label class="text-sm text-gray-600 block mb-1">Select the type of leave you want to apply</label>
          <div class="relative">
            <select 
              v-model="leaveType" 
              class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm"
            >
              <option value="" disabled>Select</option>
              <option v-for="type in leaveTypes" :key="type" :value="type">{{ type }}</option>
            </select>
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <ChevronDownIcon class="w-4 h-4 text-gray-500" />
            </div>
          </div>
        </div>
  
        <!-- Day Type Selection -->
        <div>
          <div class="flex gap-2 mb-2">
            <button 
              type="button" 
              v-for="type in ['Full Day', 'Custom']" 
              :key="type" 
              class="px-4 py-1 rounded-md text-sm border"
              :class="{ 'bg-[#EEEAF4] border-[#5D5464]': dayType === type, 'bg-white': dayType !== type }" 
              @click="dayType = type"
            >
              {{ type }}
            </button>
          </div>
          
          <div v-if="calculateDays > 0" class="flex items-center gap-2 text-sm">
            <AlertCircleIcon class="w-4 h-4 text-red-500" />
            <span>You are requesting {{ calculateDays }} leave days</span>
          </div>
        </div>
  
        <!-- Half Day Selection (if Custom) -->
        <div v-if="dayType === 'Custom'" class="grid grid-cols-2 gap-4">
          <div>
            <div class="text-xs text-gray-500 mb-1">From {{ formatDate(fromDate) }}</div>
            <div class="relative">
              <select 
                v-model="fromDayPart" 
                class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm"
              >
                <option v-for="part in dayParts" :key="part" :value="part">{{ part }}</option>
              </select>
              <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <ChevronDownIcon class="w-4 h-4 text-gray-500" />
              </div>
            </div>
          </div>
          
          <div>
            <div class="text-xs text-gray-500 mb-1">From {{ formatDate(toDate) }}</div>
            <div class="relative">
              <select 
                v-model="toDayPart" 
                class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm"
              >
                <option v-for="part in dayParts" :key="part" :value="part">{{ part }}</option>
              </select>
              <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <ChevronDownIcon class="w-4 h-4 text-gray-500" />
              </div>
            </div>
          </div>
        </div>
  
        <!-- Note -->
        <div>
          <label class="text-sm text-gray-600 block mb-1">Note</label>
          <textarea 
            v-model="note" 
            class="w-full border rounded p-2 text-sm min-h-[80px]" 
            placeholder="Type Here"
          ></textarea>
        </div>
  
        <!-- Notify field with Autocomplete -->
        <div>
          <label class="text-sm text-gray-600 block mb-1">Notify</label>
          <Autocomplete 
            v-model="notifyEmployees" 
            :options="employeeOptions" 
            placeholder="Search Employee" 
            :multiple="true"
            class="w-full" 
            option-label="label" 
            option-value="value" 
          />
        </div>
  
        <!-- Form actions -->
        <div class="flex gap-4 justify-end pt-2">
          <button 
            type="button" 
            @click="clearForm" 
            class="px-6 py-2 rounded-full bg-gray-600 text-white"
          >
            Clear
          </button>
          <button 
            @click="submitRequest" 
            type="submit" 
            class="px-6 py-2 rounded-full bg-[#5D5464] text-white"
          >
            Request
          </button>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed } from 'vue';
  import { ChevronDownIcon, AlertCircleIcon } from 'lucide-vue-next';
  import { DatePicker, Autocomplete } from 'frappe-ui';
  
  // Define emits
  const emit = defineEmits(['submitRequest', 'cancel']);
  
  // Form state
  const fromDate = ref(null);
  const toDate = ref(null);
  const leaveType = ref('');
  const dayType = ref('Full Day');
  const fromDayPart = ref('Second Half');
  const toDayPart = ref('First Half');
  const note = ref('');
  const notifyEmployees = ref([]);
  
  // Options
  const leaveTypes = ['Casual Leave', 'Sick Leave', 'Earned Leave', 'Compensatory Off'];
  const dayParts = ['First Half', 'Second Half'];
  const employeeOptions = [
    { label: 'Waseem Ahmed', value: '<EMAIL>' },
    { label: 'Sandeep kakde', value: '<EMAIL>' },
    { label: 'John Doe', value: '<EMAIL>' },
    { label: 'Jane Smith', value: '<EMAIL>' },
  ];
  
  // Calculate days between dates
  const calculateDays = computed(() => {
    if (!fromDate.value || !toDate.value) return 0;
    
    const start = new Date(fromDate.value);
    const end = new Date(toDate.value);
    
    // Set time to midnight to avoid time zone issues
    start.setHours(0, 0, 0, 0);
    end.setHours(0, 0, 0, 0);
    
    // Calculate the difference in days
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // Include both start and end days
    
    return diffDays;
  });
  
  // Format date for display
  const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };
  
  // Form validation
  const isFormValid = computed(() => {
    return fromDate.value && 
           toDate.value && 
           leaveType.value && 
           note.value.trim() !== '';
  });
  
  /**
   * Submit the site visit request
   */
  const submitRequest = () => {
    if (!isFormValid.value) {
      alert('Please fill all required fields');
      return;
    }
  
    const request = {
      type: 'Site Visit',
      fromDate: formatDate(fromDate.value),
      toDate: formatDate(toDate.value),
      days: calculateDays.value,
      leaveType: leaveType.value,
      dayType: dayType.value,
      fromDayPart: dayType.value === 'Custom' ? fromDayPart.value : null,
      toDayPart: dayType.value === 'Custom' ? toDayPart.value : null,
      note: note.value,
      notifyEmployees: notifyEmployees.value,
      requestedOn: formatDate(new Date()),
      status: 'Pending'
    };
  
    console.log('Site Visit Request:', request);
    emit('submitRequest', request);
    clearForm();
  };
  
  /**
   * Clear the form fields
   */
  const clearForm = () => {
    fromDate.value = null;
    toDate.value = null;
    leaveType.value = '';
    dayType.value = 'Full Day';
    fromDayPart.value = 'Second Half';
    toDayPart.value = 'First Half';
    note.value = '';
    notifyEmployees.value = [];
  };
  </script>