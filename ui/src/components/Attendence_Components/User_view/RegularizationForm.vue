<template>
  <div class="h-full">
    <div class="flex justify-between items-center mb-2 p-4 bg-[#DED8E1]">
      <h2 class="text-base font-medium text-[#574E60]">Regularization Request</h2>
    </div>

    <div class="space-y-4 pl-4 pr-4">
      <!-- Shift information -->
      <!-- <div class="flex flex-col gap-4 text-sm mb-8">
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Shift:</span>
          <span class="font-medium text-sm">{{ shiftTime }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Check in time:</span>
          <span class="font-medium text-sm">{{ checkInTime || '--:--' }}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm text-gray-500">Check out time:</span>
          <span class="font-medium text-sm">{{ checkOutTime || '--:--' }}</span>
        </div>
      </div> -->

      <div>
        <label class="text-sm text-gray-600 block mb-1">Select Date</label>
        <DatePicker v-model="selectedDate" variant="subtle" placeholder="Select date" :disabled="false" />
      </div>

      <div class="flex gap-2 items-center">
        <div class="flex justify-end bg-[#EEEAF4] rounded-sm p-1">
          <button type="button" v-for="type in ['Full Day', 'Half day']" :key="type"
            class="px-4 py-1 rounded-sm text-xs" :class="{ 'bg-white shadow-xl': dayType === type }"
            @click="dayType = type">
            {{ type }}
          </button>
        </div>
      </div>

      <!-- Attendance Adjustment -->
      <!-- <div>
        <label class="text-sm text-gray-600 block mb-2">Attendance Adjustment</label>
        <div class="flex justify-between w-full">
          <div class="flex items-center gap-2">
            <div class="text-green-500">
              <ArrowDownLeftIcon class="w-5 h-5" />
            </div>
            <input type="time" v-model="checkInAdjusted" class="w-full border rounded p-2 text-sm" />
          </div>

          <div class="flex items-center gap-2">
            <div class="text-red-500">
              <ArrowUpRightIcon class="w-5 h-5" />
            </div>
            <input type="time" v-model="checkOutAdjusted" class="w-full border rounded p-2 text-sm" />
          </div>
        </div>
      </div> -->

      <!-- Note -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Note</label>
        <textarea v-model="note" class="w-full border rounded p-2 text-sm min-h-[80px]"
          placeholder="Type Here"></textarea>
      </div>

      <!-- Notify field with Autocomplete -->
      <!-- <div>
        <label class="text-sm text-gray-600 block mb-1">Notify</label>
        <Autocomplete v-model="notifyEmployees" :options="employeeOptions" placeholder="Search Employee"
          :multiple="true" class="w-full" option-label="label" option-value="value" />
      </div> -->

      <!-- Attachment -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Attachment</label>
        <div class="flex items-center gap-2">
          <button @click="triggerFileInput" class="px-4 py-2 rounded bg-[#EEEAF4] text-sm">
            Upload File
          </button>
          <span v-if="selectedFile" class="text-sm">{{ selectedFile.name }}</span>
          <input type="file" ref="fileInput" @change="handleFileChange" class="hidden" />
        </div>
      </div>

      <!-- Form actions -->
      <div class="flex gap-4 justify-end pt-2">
        <button type="button" @click="clearForm" class="px-6 py-2 rounded-full bg-gray-600 text-white">
          Clear
        </button>
        <button @click="submitRequest" type="submit" class="px-6 py-2 rounded-full bg-[#5D5464] text-white">
          Request
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ArrowDownLeftIcon, ArrowUpRightIcon } from 'lucide-vue-next';
import { DatePicker, Autocomplete, createResource ,toast} from 'frappe-ui';
import { sessionUser } from '../../../data/session'

// Define emits
const emit = defineEmits(['submitRequest', 'cancel']);

// Form state
const shiftTime = ref('09:50 - 18:00');
const checkInTime = ref('--:--');
const checkOutTime = ref('--:--');
const selectedDate = ref('')
const dayType = ref('Full Day');
// const checkInAdjusted = ref('09:50');
// const checkOutAdjusted = ref('18:00');
const note = ref('');
const notifyEmployees = ref([]);
const selectedFile = ref(null);
const fileInput = ref(null);
const employee_name = ref(window.emp_id)
const login_user = sessionUser()
// Employee options for autocomplete
const employeeOptions = ref([
  // { label: 'Waseem Ahmed', value: '<EMAIL>' },
  // { label: 'Sandeep kakde', value: '<EMAIL>' },
  // { label: 'John Doe', value: '<EMAIL>' },
  // { label: 'Jane Smith', value: '<EMAIL>' },
]);

// File handling
const triggerFileInput = () => {
  fileInput.value.click();
};

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    selectedFile.value = {
      file_obj: file,
      name: file.name,
      file_name: file.name,
      file_url: ''  // optional if not uploading to a URL directly
    };
  } else {
    selectedFile.value = null;
  }
};

// Form validation
const isFormValid = computed(() => {
  return note.value.trim() !== '';
});

/**
 * Submit the regularization request
 */
const submitRequest = () => {
  if (!isFormValid.value) {
    // alert('Please provide a reason for regularization');
    toast({
          title: 'Error',
          text: 'Please provide a reason for regularization',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
    return;
  }

  const request = {
    type: 'Regularization',
    checkInOriginal: checkInTime.value,
    checkOutOriginal: checkOutTime.value,
    // checkInAdjusted: checkInAdjusted.value,
    // checkOutAdjusted: checkOutAdjusted.value,
    note: note.value,
    // notifyEmployees: notifyEmployees.value,
    attachment: selectedFile.value ? selectedFile.value.name : null,
    requestedOn: new Date().toISOString(),
    status: 'Pending'
  };
  console.log(request, "request");

  const new_attendance_request = createResource({
    url: 'frappe.client.insert',
    makeParams: () => ({
      doc: {
        doctype: "Attendance Request",
        employee: employee_name.value,
        from_date: selectedDate.value || '',
        to_date: selectedDate.value || '',
        reason: "On Duty",
        explanation: note.value || '',
        // notify_users: notifyEmployees.value.map(emp => ({
        //   doctype: "IDP Notify User",
        //   parenttype: "Attendance Request",
        //   parentfield: "notify_users",
        //   user: emp.value
        // })),
        half_day: dayType.value === "Full Day" ? 0 : 1,
        half_day_date: dayType.value === "Full Day" ? "" : selectedDate.value,
      }
    }),
    auto: true,
    onSuccess: async(resp) => {
      // console.log(resp.name, selectedFile.value.file_obj)
      toast({
            title: 'Success',
            text: 'Regularization created sucessfully',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
      if (selectedFile.value) {
        const formData = new FormData();
        formData.append("file", selectedFile.value.file_obj);
        formData.append("file_name", selectedFile.value.name);
        formData.append("is_private", "1");
        formData.append("doctype", "Attendance Request");
        formData.append("docname", resp.name);
        const uploadResp = await fetch("/api/method/upload_file", {
            method: "POST",
            body: formData
          });
        if (uploadResp.ok) {
          emit('submitRequest', request);
          clearForm();
        }
       
      }else{
        emit('submitRequest', request);
        clearForm();
        toast({
          title: 'Error',
          text: 'Failed to upload file',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      }
    },
    onError: (error) => {
      // alert("Error: " + error.message);
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to create request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      // console.error("Attendance Request error", error);
    }
  });
};

const clearForm = () => {
  note.value = '';
  notifyEmployees.value = [];
  selectedFile.value = ref(null);
  if (fileInput.value) {
    fileInput.value.value = '';
  }
  selectedDate.value = '';
};

// const employee = createResource({
//   url: 'frappe.client.get_value',
//   makeParams: () => ({
//     doctype: 'Employee',
//     // user_id: login_user,
//     filters: {
//         user_id: login_user,
//         status: 'Active'
//       },
//     fieldname: ['name']
//     // value: section.name,
//   }),
//   auto: true,
//   onSuccess: (res) => {
//     employee_name.value = res.name
//     // console.log("EMployee Name",employee_name.value)
//   },
//   onError: (error) => {
//     console.log(error)
//   },
// })
function get_user() {
  const user = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'User',
      fields: ['name', 'full_name'],
      filters: [['enabled', '=', 1]],
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log(res)
      employeeOptions.value = res.map(user => ({
        label: user.full_name || user.name,
        value: user.name
      }));
    },
    onError: (error) => {
      console.log(error)
    },
  })
}
onMounted(() => {

  get_user()
})
</script>