<template>
  <div class="h-full">
    <div class="flex justify-between items-center mb-4 p-4 bg-[#DED8E1]">
      <h2 class="text-base font-medium text-[#574E60]">Request Outdoor Duty</h2>
    </div>

    <div class="space-y-4 p-4">
      <!-- Date selection -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Select Date</label>
        <DatePicker v-model="selectedDate" variant="subtle" placeholder="Select date" :disabled="false" />
      </div>

      <!-- Outdoor Duty selection -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Outdoor Duty Type</label>
        <div class="relative">
          <select v-model="OutdoorType" class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm">
            <option value="" disabled>Select</option>
            <option v-for="OutdoorType in OutdoorTypes" :key="OutdoorType" :value="OutdoorType">{{ OutdoorType }}
            </option>
          </select>
          <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <ChevronDownIcon class="w-4 h-4 text-gray-500" />
          </div>
        </div>
      </div>
      <!-- Project selection -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Project</label>
        <div class="relative">
          <select v-model="selectedProject" class="w-full border rounded p-2 pr-8 appearance-none bg-white text-sm">
            <option value="" disabled>Select</option>
            <option v-for="project in projects" :key="project" :value="project.value">{{ project.label }}</option>
          </select>
          <div class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
            <ChevronDownIcon class="w-4 h-4 text-gray-500" />
          </div>
        </div>
      </div>

      <div class="flex gap-2 items-center">
        <div class="flex justify-end bg-[#EEEAF4] rounded-sm p-1">
          <button type="button" v-for="type in ['Full Day', 'Custom']" :key="type" class="px-4 py-1 rounded-sm text-xs"
            :class="{ 'bg-white shadow-xl': dayType === type }" @click="dayType = type">
            {{ type }}
          </button>
        </div>
      </div>

      <div v-if="dayType === 'Custom'">
        <label class="text-sm text-gray-600 block mb-2">Enter Details</label>
        <div class="flex justify-between w-full">
          <div class="flex items-center gap-2">
            <div class="text-green-500">
              <ArrowDownLeftIcon class="w-5 h-5" />
            </div>
            <input type="time" v-model="checkInAdjusted" class="w-full border rounded p-2 text-sm" />
          </div>

          <div class="flex items-center gap-2">
            <div class="text-red-500">
              <ArrowUpRightIcon class="w-5 h-5" />
            </div>
            <input type="time" v-model="checkOutAdjusted" class="w-full border rounded p-2 text-sm" />
          </div>
        </div>
      </div>

      <!-- Reason for request -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Reason for Request</label>
        <textarea v-model="reason" class="w-full border rounded p-2 text-sm min-h-[80px]"
          placeholder="Enter Here"></textarea>
      </div>

      <!-- Notify field with Autocomplete -->
      <div>
        <label class="text-sm text-gray-600 block mb-1">Notify</label>
        <Autocomplete v-model="notifyEmployees" :options="employeeOptions" placeholder="Search Employee"
          :multiple="true" class="w-full" option-label="label" option-value="value" />
      </div>

      <!-- Form actions -->
      <div class="flex gap-4 justify-end pt-2">
        <button type="button" @click="clearForm" class="px-6 py-2 rounded-full bg-gray-600 text-white">
          Clear
        </button>
        <button @click="submitRequest" type="submit" class="px-6 py-2 rounded-full bg-[#5D5464] text-white">
          Request
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { ChevronDownIcon } from 'lucide-vue-next';
import { DatePicker, Autocomplete, createResource ,toast} from 'frappe-ui';
import { ArrowDownLeftIcon, ArrowUpRightIcon } from 'lucide-vue-next';
import { sessionUser } from '../../../data/session'

// Define emits
const emit = defineEmits(['submitRequest', 'cancel']);
const login_user = sessionUser()

// Form state
const selectedDate = ref(null);
const OutdoorType = ref('');
const selectedProject = ref('');
const dayType = ref('Full Day');
const checkInAdjusted = ref('10:00');
const checkOutAdjusted = ref('18:00');
const reason = ref('');
const notifyEmployees = ref([]);
const employee_name = ref(window.emp_id)

const OutdoorTypes = ref([
  // 'Site Visit', 'Vendor Visit', 'Client Visit'
]);
const projects = ref([
  // 'Project A', 'Project B', 'Project C', 'Project D'
]);
const employeeOptions = ref([]);

// Format date for display
const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
};

// Form validation
const isFormValid = computed(() => {
  return selectedDate.value &&
    selectedProject.value &&
    reason.value.trim() !== '';
});


const submitRequest = () => {
  // if (!isFormValid.value) {
  //   alert('Please fill all required fields');
  //   return;
  // }

  const request = {
    date: formatDate(selectedDate.value),
    dayType: dayType.value,
    // workPeriod: workPeriod.value,
    // hours: hours.value,
    project: selectedProject.value,
    reason: reason.value,
    notifyEmployees: notifyEmployees.value,
    requestedOn: formatDate(new Date()),
    status: 'Pending'
  };

  const new_outdoor_duty_request = createResource({
    url: 'frappe.client.insert',
    makeParams: () => ({
      doc: {
        doctype: "IDP Outdoor Duty Request",
        employee: employee_name.value,
        date: (selectedDate.value) || '',
        duty_type: OutdoorType.value || '', // UNWRAP all refs
        project: selectedProject.value || '',
        notify: notifyEmployees.value.map(emp => ({
          doctype: "IDP Notify User",
          parenttype: "IDP Outdoor Duty Request",
          parentfield: "notify",
          user: emp.value
        })),
        in_time: dayType.value != "Full Day"?checkInAdjusted.value:"",
        out_time: dayType.value != "Full Day"? checkOutAdjusted.value :"",
        reason: reason.value || ''
      }
    }),
    auto: true,
    onSuccess: (resp) => {
      // console.log("resp", resp);
      toast({
            title: 'Success',
            text: 'Requested for outdoor duty',
            icon: 'check-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
      emit('submitRequest', request);// if using emit
    },
    onError: (error) => {
      // alert("Error: " + error.message);
      toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to cancel request',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        })
      // console.log("error", error);
    }
  });
  // console.log('Outdoor Duty Request:', request);
  // emit('submitRequest', request);
  clearForm();
};

const clearForm = () => {
  selectedDate.value = null;
  dayType.value = 'Full Day';
  selectedProject.value = '';
  reason.value = '';
  notifyEmployees.value = [];
  OutdoorType.value = '';
  selectedProject.value = ''
};


function get_duty_type() {
  const duty_type = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'IDP Duty Type',
      fields: ['name'],
      // filters: [['enabled', '=', 1]], // Uncomment if you want to filter only enabled
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log(res)
      OutdoorTypes.value = res.map(duty => duty.name) // Extract only the name
    },
    onError: (error) => {
      console.error('Failed to fetch duty types:', error)
    },
  })
}

function get_user() {
  const user = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'User',
      fields: ['name', 'full_name'],
      filters: [['enabled', '=', 1]],
    }),
    auto: true,
    onSuccess: (res) => {
      // console.log(res)
      employeeOptions.value = res.map(user => ({
        label: user.full_name || user.name,
        value: user.name
      }));
    },
    onError: (error) => {
      console.log(error)
    },
  })
}

function get_project() {
  const user = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'IDP Project',
      fields: ['name', "project_name"],
      filters: [
        ['IDP Project User', 'user', '=', login_user]
      ],
    }),
    auto: true,
    onSuccess: (res) => {
      projects.value = res.map(project => ({
        label: project.project_name,
        value: project.name
      }));
    },
    onError: (error) => {
      console.log(error)
    },
  })
}
onMounted(() => {
  get_duty_type()
  get_user()
  get_project()
})

</script>