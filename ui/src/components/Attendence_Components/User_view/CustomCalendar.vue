<template>
  <div class="attendance-calendar">
    <div class="calendar-grid">
      <!-- Days of week header -->
      <div class="grid grid-cols-7 text-center border-b">
        <div v-for="day in daysOfWeek" :key="day" class="py-2 text-xs font-medium text-gray-500">
          {{ day }}
        </div>
      </div>

      <!-- Calendar days -->
      <div class="grid grid-cols-7">
        <div v-for="(day, index) in calendarDays" :key="index" :class="[
          'border-r border-b min-h-[120px] relative',
          day.isCurrentMonth ? '' : 'bg-gray-50 text-gray-400',

          index % 7 === 6 ? 'border-r-0' : ''
        ]" @click="selectDay(day)">

          <div :class="[
            'flex justify-between items-center p-2',
            getBackgroundClass(day)
          ]">
            <span class="text-xs text-gray-700">{{ day.dayNumber }}</span>
            <span v-if="day.isToday" class="w-2 h-2 rounded-full bg-blue-500 ml-auto"></span>
          </div>

          <!-- Attendance data (only for days with data) -->
          <div v-if="day.isCurrentMonth && day.hasData && day.checkIn" class="text-sm space-y-1 p-3">
            <!-- Check-in time -->
            <div class="flex">
              <span class="font-medium">{{ day.checkIn }}</span>
              <span class="ml-1 text-gray-500 text-[10px]">Check-in</span>
            </div>

            <!-- Check-out time -->
            <div class="flex">
              <span class="font-medium">{{ day.checkOut }}</span>
              <span class="ml-1 text-gray-500 text-[10px]">Check-out</span>
            </div>

            <!-- Hours with status badge -->
            <div class="flex items-center">
              <span class="font-medium">{{ day.hours }} hrs</span>
              <span v-if="day.onTimeStatus" :class="[
                'ml-1 px-1 py-0.5 text-[10px] rounded',
                getStatusBadgeClass(day.onTimeStatus)
              ]">
                {{ day.onTimeStatus }}
              </span>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Legend -->
    <div class="flex items-center gap-4 text-xs p-2">
      <div class="flex items-center gap-1">
        <span class="w-3 h-3 rounded-full bg-green-200"></span>
        <span>Present</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="w-3 h-3 rounded-full bg-red-200"></span>
        <span>Absent</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="w-3 h-3 rounded-full bg-gray-200"></span>
        <span>Holiday</span>
      </div>
      <div class="flex items-center gap-1">
        <span class="w-3 h-3 rounded-full bg-yellow-200"></span>
        <span>Approved Leaves</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';

const props = defineProps({
  events: {
    type: Array,
    default: () => []
  },
  selectedDate: {
    type: [Date, String, null],
    default: null
  },
  currentMonth: {
    type: Date,
    default: () => new Date()
  }
});

const emit = defineEmits(['date-select']);

// Calendar state
const currentDate = ref(new Date());
const selectedDay = ref(null);
const calendarDays = ref([]);
const daysOfWeek = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];



// Get background class based on day status
const getBackgroundClass = (day) => {
  if (!day.isCurrentMonth || !day.hasData) return '';

  if (day.status === 'absent') return 'bg-red-200';
  if (day.status === 'holiday') return 'bg-gray-200';
  if (day.status === 'present') return 'bg-green-200';
  if (day.status === 'on leave') return 'bg-yellow-200';
  else return 'bg-red-200'
  return '';
};

// Get status badge class
const getStatusBadgeClass = (status) => {
  // if (status === 'On Time') return 'bg-green-100 text-green-800';
  // if (status === 'Late Entry') return 'bg-yellow-100 text-yellow-800';
  // if (status === 'Early Exit') return 'bg-yellow-100 text-yellow-800';
  return 'bg-purple-100 text-purple-800';
};

// Select a day
const selectDay = (day) => {
  if (!day.isCurrentMonth) return;

  // Deselect previously selected day
  if (selectedDay.value) {
    selectedDay.value.isSelected = false;
  }

  // Select new day
  day.isSelected = true;
  selectedDay.value = day;

  // Emit event with selected date and data
  emit('date-select', {
    date: day.date,
    data: {
      checkIn: day.checkIn,
      checkOut: day.checkOut,
      hours: day.hours,
      status: day.status,
      onTimeStatus: day.onTimeStatus
    }
  });
};

// Update the findEventForDate function:
const findEventForDate = (date) => {
  return props.events.find(event => {
    const eventDate = new Date(event.fromDate.split(' ')[0]); // Get just the date part
    return eventDate.toDateString() === date.toDateString();
  });
};

// Generate calendar days
const generateCalendarDays = () => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();

  // Get first day of month
  const firstDay = new Date(year, month, 1);
  const firstDayOfWeek = firstDay.getDay();

  // Get last day of month
  const lastDay = new Date(year, month + 1, 0);
  const daysInMonth = lastDay.getDate();

  // Get days from previous month
  const prevMonthLastDay = new Date(year, month, 0).getDate();
  const prevMonthDays = [];

  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i;
    const date = new Date(year, month - 1, day);

    prevMonthDays.push({
      date,
      dayNumber: day,
      isCurrentMonth: false,
      isToday: false,
      isSelected: false,
      hasData: false
    });
  }

  // Get days from current month
  const currentMonthDays = [];
  const today = new Date();

  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day);
    const isToday = date.toDateString() === today.toDateString();

    // Find event data for this date
    const event = findEventForDate(date);
    const hasData = !!event;

    let status, onTimeStatus, checkIn, checkOut, hours;

    if (hasData) {
      status = event.status.toLowerCase();
      onTimeStatus = event.onTimeStatus 
      checkIn = event.Checkin;
      checkOut = event.Checkout;
      hours = event.OnTime;
    } else {
      // Default values for days without data
      const dayOfWeek = date.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        // Weekend
        status = 'holiday';
        onTimeStatus = '';
      } else {
        status = '';
        onTimeStatus = '';
      }
      checkIn = null;
      checkOut = null;
      hours = null;
    }

    currentMonthDays.push({
      date,
      dayNumber: day,
      isCurrentMonth: true,
      isToday,
      isSelected: false,
      hasData,
      status,
      checkIn,
      checkOut,
      hours,
      onTimeStatus
    });
  }

  // Get days from next month
  const nextMonthDays = [];
  const totalDaysShown = prevMonthDays.length + currentMonthDays.length;
  const daysToAdd = 42 - totalDaysShown; // 6 rows of 7 days

  for (let day = 1; day <= daysToAdd; day++) {
    const date = new Date(year, month + 1, day);

    nextMonthDays.push({
      date,
      dayNumber: day,
      isCurrentMonth: false,
      isToday: false,
      isSelected: false,
      hasData: false
    });
  }

  // Combine all days
  calendarDays.value = [...prevMonthDays, ...currentMonthDays, ...nextMonthDays];

  // If there was a selected date, try to find and select it in the new calendar
  if (props.selectedDate) {
    const selectedDate = new Date(props.selectedDate);
    const selectedMonth = selectedDate.getMonth();
    const selectedYear = selectedDate.getFullYear();

    // Only try to select if the month and year match
    if (selectedMonth === month && selectedYear === year) {
      const selectedDay = calendarDays.value.find(day =>
        day.isCurrentMonth && day.date.getDate() === selectedDate.getDate()
      );

      if (selectedDay) {
        selectDay(selectedDay);
      }
    }
  }
};

// Watch for changes in events
watch(() => props.events, () => {
  generateCalendarDays();
}, { deep: true });

// Watch for changes in the selected date prop
watch(() => props.selectedDate, (newDate) => {
  if (newDate) {
    const date = new Date(newDate);

    // Update current date to match the month of the selected date
    if (date.getMonth() !== currentDate.value.getMonth() ||
      date.getFullYear() !== currentDate.value.getFullYear()) {
      currentDate.value = new Date(date.getFullYear(), date.getMonth(), 1);
      generateCalendarDays();
    }
  }
});

watch(() => props.currentMonth, (newMonth) => {
  currentDate.value = new Date(newMonth);
  generateCalendarDays();
}, { immediate: true });

// Initialize
onMounted(() => {
  generateCalendarDays();
});
</script>