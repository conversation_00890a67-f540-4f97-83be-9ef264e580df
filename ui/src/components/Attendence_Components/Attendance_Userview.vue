<template>
  <div class="w-full">
    <div class="flex justify-between items-center">
      <div class="flex items-center">
        <div class="flex items-center gap-2">
          <button class="p-2 rounded hover:bg-gray-100" @click="prevMonth">
            <span class="text-sm text-gray-600">&#10094;</span>
          </button>
          <h2 class="text-lg font-medium w-32 text-center">{{ currentMonthDisplay }}</h2>
          <button class="p-2 rounded hover:bg-gray-100" @click="nextMonth">
            <span class="text-sm text-gray-600">&#10095;</span>
          </button>
        </div>
        <button
          class="ml-4 px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
          @click="setCurrentMonth">
          This month
        </button>
        <button
          class="ml-4 px-3 py-1 text-sm bg-white border border-gray-200 rounded-full text-purple-600 hover:bg-gray-50"
          @click="scrollToRequests">
          My Requests
        </button>
      </div>

      <div class="flex items-center gap-4">
        <div class="relative">
          <button @click="toggleDropdown"
            class="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-md text-sm font-medium">
            {{ selectedRequestType }}
            <ChevronDownIcon class="w-4 h-4 text-gray-500" />
          </button>

          <div v-if="showDropdown" class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg z-10 border">
            <div class="py-1">
              <button v-for="type in requestTypes" :key="type" @click="selectRequestType(type)"
                class="block w-full text-left px-4 py-2 text-sm hover:bg-gray-100">
                {{ type }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="flex justify-between gap-4 items-stretch">
      <div class="flex-grow border rounded-sm">
        <CustomCalendar :events="eventList" :selectedDate="selectedDate" :currentMonth="currentDate"
          @date-select="handleDateSelect" />
      </div>

      <div class="w-96 border rounded-sm">
        <div v-if="selectedDate" class="mb-4">
          <OverviewForm v-if="selectedRequestType === 'Overview'" :selectedDateData="selectedDateData" />
          <OutdoorDutyForm v-if="selectedRequestType === 'Outdoor Duty'" @submitRequest="handleRequestSubmit" />
          <RegularizationForm v-if="selectedRequestType === 'Regularization'" @submitRequest="handleRequestSubmit" />
        </div>
        <div v-else class="flex flex-col items-center text-md text-gray-500 mt-44">
          <FeatherIcon :name="'coffee'" class="h-8 w-8" />
          <span>Select a date to view details</span>
        </div>
      </div>
    </div>

    <!-- Requests Table Section -->
    <div class="mt-8 border-t pt-4 scrollToRequests">
      <RequestsTable />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import AttendenceIcon from "../icons/AttendenceIcon.vue";
import OverviewForm from "./User_view/OverviewForm.vue";
import OutdoorDutyForm from "./User_view/OutdoorDutyForm.vue";
import WorkFromHomeForm from "./User_view/WorkFromHomeForm.vue";
import RegularizationForm from "./User_view/RegularizationForm.vue";
import SiteVisitForm from "./User_view/SiteVisitForm.vue";
import RequestsTable from "./User_view/RequestsTable.vue";
import CustomCalendar from "./User_view/CustomCalendar.vue";
import { ChevronDownIcon, AlertCircleIcon } from 'lucide-vue-next';
import { FeatherIcon ,createResource} from 'frappe-ui'

const allAttendanceData = ref([]);
const mockData = {
  month: new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' }),
  days: []
  // month: "March 2025",
  // days: [
  //   {
  //     date: "2025-04-03",
  //     checkIn: "09:00",
  //     checkOut: "18:00",
  //     hours: 8,
  //     onTimeStatus: "On Time",
  //     status: "present"
  //   },
  //   {
  //     date: "2025-04-07",
  //     checkIn: "09:30",
  //     checkOut: "18:30",
  //     hours: 8,
  //     onTimeStatus: "Late",
  //     status: "present"
  //   },
  //   {
  //     date: "2025-04-08",
  //     checkIn: "",
  //     checkOut: "",
  //     hours: 0,
  //     onTimeStatus: "",
  //     status: "holiday"
  //   },
  //   {
  //     date: "2025-04-15",
  //     checkIn: "",
  //     checkOut: "",
  //     hours: 0,
  //     onTimeStatus: "",
  //     status: "absent"
  //   },
  // ],
};
const requestTypes = ref(['Overview', 'Outdoor Duty', 'Regularization']);
const selectedRequestType = ref('Overview');
const showDropdown = ref(false);

const currentDate = ref(new Date());
const selectedDate = ref(null);
const selectedDateData = ref(null);
const selectedDateStatus = ref('');

const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value;
};

const selectRequestType = (type) => {
  selectedRequestType.value = type;
  showDropdown.value = false;
};

// const handleDateSelect = (data) => {
//   const selectedDateLocal = formatDateToLocal(data.date);
//   selectedDate.value = selectedDateLocal;
//   const selectedDayData = mockData.value.days.find(day => day.date === selectedDateLocal);
//   selectedDateData.value = selectedDayData || {
//     date: selectedDateLocal,
//     checkIn: '',
//     checkOut: '',
//     hours: 0,
//     onTimeStatus: '',
//     status: 'No Record'
//   };
//   selectedRequestType.value = 'Overview';
// };
const handleDateSelect = (data) => {
  const selectedDateLocal = formatDateToLocal(data.date);
  selectedDate.value = selectedDateLocal;
  const selectedDayData = mockData.value.days.find(day => day.date === selectedDateLocal);
  selectedDateData.value = selectedDayData || {
    date: selectedDateLocal,
    checkIn: '',
    checkOut: '',
    hours: 0,
    onTimeStatus: '',
    status: 'No Record'
  };

  selectedRequestType.value = 'Overview';
};
const formatDateToLocal = (date) => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const eventList = ref([]);

const getMonthData = (monthYear) => {
  const monthData = allAttendanceData.value.find(data => data.month === monthYear);
  return monthData ? monthData : { month: monthYear, days: [] };
};

const updateCurrentMonthData = () => {
  const currentMonthYear = currentDate.value.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  mockData.value = getMonthData(currentMonthYear);
  updateEventList();
};

const updateEventList = () => {
  eventList.value = mockData.value.days.map(day => ({
    Checkin: day.checkIn || day.status =='holiday'? day.checkIn:"--:--",
    Checkout: day.checkOut ||day.status =='holiday'? day.checkOut:"--:--",
    OnTime: day.hours,
    onTimeStatus: day.onTimeStatus || "",
    status: day.status,
    fromDate: `${day.date} 09:00:00`,
    toDate: `${day.date} 18:00:00`,
  }));
};

// onMounted(() => {
  
//   const get_allrecords = createResource({
//     url:"inspira.inspira.api.attendance.attendance.get_all_employee_attendance",
//     makeParams:()=>({
//     }),
//     auto:true,
//     onSuccess:(resp)=>{
//       console.log("resp",resp)
//       mockData.value = resp
//       // showDetailModal.value = false
//       // requests.value = requests.value.filter(r => r.id !== request.id);
//     },
//     onError:(error)=>{
//       console.log("Error while deleting doc",error)
//     }
//   })
//   updateEventList();

//   const today = new Date();
//   handleDateSelect({ date: today });
// });

onMounted(() => {
  const get_allrecords = createResource({
    url: "inspira.inspira.api.attendance.attendance.get_all_employee_attendance",
    makeParams: () => ({}),
    auto: true,
    onSuccess: (resp) => {
      console.log("API Response:", resp);
      
      allAttendanceData.value = resp || [];
      updateCurrentMonthData();
      
      const today = new Date();
      handleDateSelect({ date: today });
    },
    onError: (error) => {
      console.log("Error while fetching attendance data", error);
      allAttendanceData.value = [];
      updateCurrentMonthData();
    }
  });
});
const currentMonthDisplay = computed(() => {
  return currentDate.value.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
});

const prevMonth = () => {
  const date = new Date(currentDate.value);
  date.setMonth(date.getMonth() - 1);
  currentDate.value = date;
  updateCurrentMonthData();
  selectedDate.value = null;
};

const nextMonth = () => {
  const date = new Date(currentDate.value);
  date.setMonth(date.getMonth() + 1);
  currentDate.value = date;
  updateCurrentMonthData();
  selectedDate.value = null;
};

const setCurrentMonth = () => {
  currentDate.value = new Date();
  updateCurrentMonthData()
};

// Handle form submissions
const handleRequestSubmit = (requestData) => {
  console.log('Request submitted:', requestData);
  // Here you would typically send the data to your backend API
  // For now, we'll just show a success message
  // alert(`${requestData.type} request submitted successfully!`);
};
const scrollToRequests = () => {
  document.querySelector('.scrollToRequests').scrollIntoView({ behavior: 'smooth' });
};
</script>

<style scoped>
::v-deep .w-full {
  background-color: #fff;
}

::v-deep .w-full button {
  background-color: #fff;
  border: 1px solid rgb(187, 186, 186);
}
</style>