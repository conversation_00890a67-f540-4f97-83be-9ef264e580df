<template>
  <div class="w-full">
    <!-- Leave header with icon and tabs -->
    <div class="flex gap-5 border-b border-gray-300 mt-3 px-4 items-center">
      <div class="flex gap-2 items-center pb-1">
        <LeaveIcons />
        <h1 class="text-[#434343] text-[17px] font-normal font-roboto leading-none">Leave</h1>
      </div>
      <button v-for="tab in mainTabs" :key="tab" @click="activeMainTab = tab"
        class="pb-1 px-1 text-[#79747E] text-[14px] font-[600] relative transition-all" :class="[
          activeMainTab === tab
            ? 'font-medium border-b border-gray-600'
            : 'hover:text-gray-900'
        ]">
        {{ tab }}
      </button>
    </div>

    <!-- Tab Content for Leave Management -->
    <div class="pt-2 w-full">
      <Transition name="slide" mode="out-in">
        <!-- User View -->
        <div v-if="activeMainTab === 'User'" key="user-leave">
          <UserLeaveView />
        </div>
        
        <!-- Team/Manager View -->
        <div v-else-if="activeMainTab === 'Team'" key="team-leave">
          <ManagerLeaveView />
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import LeaveIcons from './icons/LeaveIcons.vue';
import UserLeaveView from './Leave_Components/UserLeaveView.vue';
import ManagerLeaveView from './Leave_Components/ManagerLeaveView.vue';
const is_manager = window.is_manager

// Main tabs for Leave Management
// const mainTabs = ['User', 'Team'];
const mainTabs = is_manager ? ['User', 'Team'] : ['User'];

const activeMainTab = ref(localStorage.getItem('activeLeaveMainTab') || 'User');

// Save active tab to localStorage
watch(activeMainTab, (newVal) => {
  localStorage.setItem('activeLeaveMainTab', newVal);
});
</script>

<style scoped>
.slide-enter-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-100px);
}
</style>