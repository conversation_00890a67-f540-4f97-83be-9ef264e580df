<template>
  <div v-if="isLoading" class="fixed inset-0 z-50 bg-white bg-opacity-50 flex justify-center items-center">
    <div class="flex items-center gap-2">
      <FeatherIcon :name="'loader'" class="h-8 w-8 text-purple-600 animate-spin" />
      <span class="text-sm text-purple-600">Almost There ...</span>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import FeatherIcon from 'frappe-ui/src/components/FeatherIcon.vue';

const isLoading = ref(false)

function setLoading(state) {
  isLoading.value = state
}

defineExpose({ setLoading })
</script>