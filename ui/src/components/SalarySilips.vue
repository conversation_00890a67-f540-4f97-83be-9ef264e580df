<template>
    <div class="w-full flex justify-between items-center border-b border-gray-300 mt-3 px-4 mb-3">
        <div class="flex gap-2 items-center pb-1">
            <FileTextIcon />
            <h1 class="text-[#434343] text-[17px] font-normal font-roboto leading-none">Salary Slip</h1>
        </div>
        <div class="pb-1">
            <button v-if="selectedMonth && selectedYear" @click="handleDownload" :disabled="isLoading || isDownloading"
                class="w-full text-sm flex items-center justify-center gap-2 bg-gray-200 hover:bg-gray-300 disabled:bg-gray-50 disabled:cursor-not-allowed text-gray-700 disabled:text-gray-400 px-4 py-1 rounded-md transition-colors">
                <DownloadIcon v-if="!isDownloading" class="w-4 h-4" />
                <LoaderIcon v-else class="w-4 h-4 animate-spin " />
                {{ downloadButtonText }}
            </button>
        </div>
    </div>

    <div class="flex justify-between gap-4 w-full h-screen">
        <div class="w-[20%] bg-white shadow-sm border-gray-200 p-6">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
                <select v-model="selectedYear" @change="handleYearChange"
                    class="w-full text-sm p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    :disabled="isLoading">
                    <option value="">Select Year</option>
                    <option v-for="year in availableYears" :key="year" :value="year">
                        {{ year }}
                    </option>
                </select>
            </div>

            <!-- Month Selection -->
            <div class="mb-6">
                <h2 class="text-sm font-medium text-gray-700 mb-3">Salary Slips</h2>
                <div v-if="selectedYear">
                    <div v-for="month in availableMonths" :key="month.value" @click="handleMonthSelect(month)" :class="[
                        'p-2 rounded-md cursor-pointer transition-colors',
                        selectedMonth?.value === month.value
                            ? 'bg-purple-50 border border-purple-200'
                            : 'hover:bg-gray-50',
                        isLoading ? 'opacity-50 cursor-not-allowed' : ''
                    ]">
                        <div class="text-sm font-medium text-gray-900">
                            {{ month.name }} {{ selectedYear }}
                        </div>
                    </div>
                </div>
                <div v-else class="text-sm text-gray-500 italic">
                    Please select a year first
                </div>
            </div>
        </div>

        <div class="w-[80%] p-2 shadow-sm  overflow-auto">
            <div class="w-full h-full flex justify-center">

                <!-- Loading State -->
                <div v-if="isLoading" class="text-center mt-32">
                    <LoaderIcon class="w-8 h-8 animate-spin mx-auto mb-4 text-purple-500" />
                    <p class="text-lg">Loading salary slip...</p>
                </div>

                <!-- No Selection State -->
                <div v-else-if="!selectedMonth || !selectedYear" class="text-center mt-32">
                    <FileTextIcon class="mx-auto mb-4 opacity-50" />
                    <h2 class="text-2xl font-semibold mb-2 text-gray-700">Salary Slip</h2>
                    <p class="text-gray-500">Choose a year and month to view the salary slip</p>
                </div>

                <!-- PDF Preview -->
                <div v-else class="bg-white rounded-lg shadow-2xl w-full max-h-full">
                    <!-- PDF Header -->
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-200 flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <FileTextIcon class="w-4 h-4 text-gray-600" />
                            <span class="text-sm font-medium text-gray-700">Salary Slip</span>
                        </div>
                        <div class="text-sm text-gray-500">
                            {{ selectedMonth.name }} {{ selectedYear }}
                        </div>
                    </div>

                    <!-- PDF Content -->
                    <div class="h-[600px] overflow-auto bg-white">
                        <iframe :src="pdfUrl" class="w-full h-full border-0" frameborder="0" @load="handlePdfLoad">
                        </iframe>
                        <!-- :src="`${pdfUrl}#zoom=50&toolbar=0`" -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch ,watchEffect } from 'vue'
import { FileText as FileTextIcon, Download as DownloadIcon, Loader as LoaderIcon } from 'lucide-vue-next'
import { createResource } from 'frappe-ui'

// Reactive data
// const selectedYear = ref('')
const selectedMonth = ref(null)
const isLoading = ref(false)
const isDownloading = ref(false)
const pdfUrl = ref('')
const pdfLoading = ref(false)
const currentYear = new Date().getFullYear()
const startYear = 2000
const availableYears = ref([])
const selectedYear = ref(currentYear.toString())
for (let year = currentYear; year >= startYear; year--) {
  availableYears.value.push(year.toString())
}
const availableMonths = ref([
    { name: 'January', value: 'january' },
    { name: 'February', value: 'february' },
    { name: 'March', value: 'march' },
    { name: 'April', value: 'april' },
    { name: 'May', value: 'may' },
    { name: 'June', value: 'june' },
    { name: 'July', value: 'july' },
    { name: 'August', value: 'august' },
    { name: 'September', value: 'september' },
    { name: 'October', value: 'october' },
    { name: 'November', value: 'november' },
    { name: 'December', value: 'december' }
])

const handlePdfLoad = () => {
    pdfLoading.value = false
}

const downloadButtonText = computed(() => {
    if (isDownloading.value) {
        return 'Downloading...'
    }
    if (selectedMonth.value && selectedYear.value) {
        return `${selectedMonth.value.name} ${selectedYear.value} Payslip`
    }
    return 'Download Payslip'
})


const handleYearChange = () => {
    selectedMonth.value = null
}

const handleMonthSelect = async (month) => {
    if (isLoading.value) return
    isLoading.value = true
    selectedMonth.value = month
    await new Promise(resolve => setTimeout(resolve, 1000))

    isLoading.value = false
}

const handleDownload = async () => {
    if (!selectedMonth.value || !selectedYear.value || isDownloading.value) return
    isDownloading.value = true
    try {
        const response = await fetch(pdfUrl.value)
        const blob = await response.blob()
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `salary-slip-${selectedMonth.value.value}-${selectedYear.value}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)
    } catch (error) {
        console.error('Download failed:', error)
        alert('Download failed. Please try again.')
    } finally {
        isDownloading.value = false
    }
}


watch([selectedYear, selectedMonth], () => {
    if (!selectedYear.value) {
        selectedMonth.value = null
    }
    
})
//
// const fetchSalarySlip = async () => {
//     const month = selectedMonth.value?.name;
//     const year = selectedYear.value;
//     const emp_id = window.emp_id;

//     if (!month || !year || !emp_id) {
//         console.warn("Missing required fields");
//         return;
//     }

//     pdfUrl.value = null;
//     isLoading.value = true;

//     try {
//         const response = await fetch('/api/method/inspira.inspira.api.salary_slip.salary_slip.salary_slip', {
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json',
//             },
//             body: JSON.stringify({
//                 month,
//                 year,
//                 emp_id
//             }),
//         });

//         const data = await response.json();

//         if (data?.message?.pdf_base64) {
//             const byteCharacters = atob(data.message.pdf_base64);
//             const byteArray = new Uint8Array(
//                 Array.from(byteCharacters).map(char => char.charCodeAt(0))
//             );
//             const blob = new Blob([byteArray], { type: 'application/pdf' });
//             pdfUrl.value = URL.createObjectURL(blob);
//         } else {
//             console.warn("PDF not received");
//         }
//     } catch (error) {
//         console.error("Error fetching salary slip:", error);
//     } finally {
//         isLoading.value = false;
//     }
// };

const salarySlipResource = createResource({
    url: '/api/method/inspira.inspira.api.salary_slip.salary_slip.salary_slip',
    method: 'POST',
    onError(error) {
        console.error('Error fetching salary slip:', error);
    },
});
// watch([selectedYear, selectedMonth], ([newYear, newMonth]) => {
//     console.log(newMonth)
//     if (newYear && newMonth) {
//         fetchSalarySlip();
//     }
// });

watch([selectedYear, selectedMonth], async ([newYear, newMonth]) => {
    if (newYear && newMonth) {
        pdfUrl.value = null;
        isLoading.value = true;

        try {
            const response = await salarySlipResource.submit({
                month: newMonth.name,
                year: newYear,
                emp_id: window.emp_id
            });
            // console.log(response.pdf_base64)
            const base64 = response.pdf_base64;
            if (base64) {
                const byteCharacters = atob(base64);
                const byteArray = new Uint8Array(
                    Array.from(byteCharacters).map(c => c.charCodeAt(0))
                );
                const blob = new Blob([byteArray], { type: 'application/pdf' });
                pdfUrl.value = URL.createObjectURL(blob);
            } else {
                console.warn('PDF not received');
            }
        } catch (err) {
            console.error('Salary slip fetch failed:', err);
        } finally {
            isLoading.value = false;
        }
    }
});
watchEffect(() => {
    isLoading.value = salarySlipResource.loading;
});
</script>