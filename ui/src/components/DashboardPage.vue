<template>
  <div class="w-full h-full flex flex-col gap-4">
    <Header_Info :username="userData.name" :activeView="activeView" @toggle-view="toggleView" />
    <Transition name="slide" mode="out-in">
      <UserView v-if="activeView === 'User'" />
      <TeamView v-else />
    </Transition>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import Header_Info from '../components/Dashboard_Components/Header_Info.vue';
import UserView from '../components/Dashboard_Components/UserView.vue';
import TeamView from '../components/Dashboard_Components/TeamView.vue';

const activeView = ref('User');
const userData = ref({
  name: window.user_name,
});
const toggleView = (view) => {
  activeView.value = view;
};
</script>
<style scoped>
.slide-enter-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-100px);
}
</style>