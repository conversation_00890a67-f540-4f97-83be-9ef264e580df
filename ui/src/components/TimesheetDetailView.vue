<template>
  <div class="p-4 flex flex-col">
    <!-- Header with employee selector and back button -->
    <div class="flex items-center justify-between">
      <div class="flex items-center gap-2">
        <button @click="close" class="p-2 rounded-full hover:bg-gray-100">
          <FeatherIcon :name="'arrow-left'" class="h-5 w-5 text-gray-600" />
        </button>

        <!-- Employee Selector using Autocomplete -->
        <div class="w-64">
          <Autocomplete placeholder="Select Employee" :options="employeeOptions" v-model="selectedEmployeeId"
            @change="handleEmployeeChange">
            <template #option="{ option }">
              <div class="flex items-center gap-2">
                <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                  <span class="text-sm">{{ getInitials(option.label) }}</span>
                </div>
                <span>{{ option.label }}</span>
              </div>
            </template>
            <template #selected="{ option }">
              <div class="flex items-center gap-2">
                <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center text-purple-600">
                  <span class="text-sm">{{ getInitials(option.label) }}</span>
                </div>
                <span>{{ option.label }}</span>
              </div>
            </template>
          </Autocomplete>
        </div>
      </div>
    </div>

    <!-- Week Range Display (Read-only) -->
    <div class="mb-6 flex justify-between items-center p-2">
      <div class="flex items-center gap-4 text-left">
        <FeatherIcon :name="'calendar'" class="h-4 w-4 text-gray-600" />
        <span class="text-sm">{{ formattedDateRange }}</span>
      </div>
      <div class="text-sm">
        <div v-if="timesheetStatus === 'Approved'" class="text-green-500 bg-green-100 px-2 py-1 rounded">
          Approved
        </div>
        <div v-else-if="timesheetStatus === 'Rejected'" class="text-red-500 bg-red-100 px-2 py-1 rounded">
          Rejected
        </div>
        <div v-else class="flex gap-2">
          <button @click="approveTimesheet" class="px-1 py-1 text-xs text-[#83AA97] rounded hover:bg-green-100">
            <ApproveIcon />
          </button>
          <button @click="showRejectDialog" class="px-1 py-1 text-xs text-red-700 rounded hover:bg-red-100">
            <RejectIcon />
          </button>
        </div>
      </div>
    </div>

    <!-- Timesheet Detail Table -->
    <div v-if="employee" class="w-full border-2 rounded-lg overflow-x-auto">
      <table class="w-full min-w-[1000px] border-collapse">
        <thead>
          <tr class="border-b">
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Project
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Milestone
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Remaining <br> Budgeted <br> Hours
            </th>
            <!-- Days of the week -->
            <template v-for="(day, index) in weekDays" :key="index">
              <th class="p-1 border-r text-sm text-center font-medium text-gray-600">
                <div class="flex flex-col justify-between gap-1">
                  <div>{{ day.dayName }}</div>
                  <div class="text-xs text-gray-500">
                    {{ day.date }}
                  </div>
                  <div class="text-xs text-gray-500 mb-1">
                    {{ calculateDayTotal(day) }}
                  </div>
                  <div>
                    <hr :class="getDayStatusClass(day)" class="h-0.5 rounded" />
                  </div>
                </div>
              </th>
            </template>
            <th class="p-3 border-r text-sm text-center font-medium text-gray-600">
              Total
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in timesheetDetails" :key="rowIndex" class="border-b hover:bg-gray-50"
            :class="{ 'bg-gray-50': rowIndex % 2 === 0 }">
            <!-- Project -->
            <td class="p-3 border-r text-sm">
              {{ row.project_name }}
            </td>

            <!-- Milestone -->
            <td class="p-3 border-r text-sm">
              {{ row.milestone }}
            </td>

            <!-- Budgeted Hours -->
            <td class="p-3 border-r text-sm">
              <div class="flex items-center gap-2">
                {{ row.budgeted_hours }} hours
                <span v-if="getBudgetStatus(row) === 'success'" class="text-green-500">
                  <GreenVector />
                </span>
                <span v-else-if="getBudgetStatus(row) === 'warning'" class="text-red-500">
                  <RedVector />
                </span>
              </div>
            </td>

            <!-- Daily Hours -->
            <td v-for="(day, dayIndex) in weekDays" :key="dayIndex" class="p-3 border-r text-sm text-center">
              {{ row[day.dayName.toLowerCase()] || '–' }}
            </td>

            <!-- Total -->
            <td class="p-3 border-r text-sm text-center">
              {{ row.total_hours }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div v-if="timesheetStatus === 'Rejected'"
      class="w-full flex flex-col bg-gray-50 mt-8 p-4 rounded-xl text-gray-700">
      <!-- <span>Rejected by: {{ timeSheet.rejected_by }}</span> -->
      <span class="text-sm">Rejected Reason: {{ rejectedReason }}</span>
    </div>

    <Dialog v-model="showRejectionDialog">
      <template #body-title>
        <h3>Reject Timesheet</h3>
      </template>
      <template #body-content>
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-1">
            Reason for rejecting {{ employee?.name }}'s timesheet
          </label>
          <textarea v-model="rejectionReason" class="w-full border rounded p-2 text-sm" rows="3"
            placeholder="Enter reason for rejection..."></textarea>
        </div>
      </template>
      <template #actions>
        <Button variant="solid" @click="rejectTimesheet">
          Submit Rejection
        </Button>
        <Button class="ml-2" @click="showRejectionDialog = false">
          Cancel
        </Button>
      </template>
    </Dialog>

    <!-- Empty State - No Employee Selected -->
    <div v-if="!employee"
      class="flex flex-col items-center shadow-2xl rounded-md p-12 w-100 h-auto mb-12 mt-12 text-gray-500">
      <FeatherIcon :name="'user'" class="h-16 w-12" /> <br />
      <p>Please select an employee to view their timesheet</p>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-4 rounded-lg shadow-lg">
        <div class="flex items-center gap-2">
          <FeatherIcon :name="'loader'" class="h-5 w-5 text-purple-600 animate-spin" />
          <span>Loading timesheet data...</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { FeatherIcon, toast, Autocomplete, Dialog, Button, createResource, createListResource } from 'frappe-ui'
import { formatDate } from '../utils/format'
import GreenVector from '../components/icons/GreenVector.vue'
import RedVector from '../components/icons/RedVector.vue'
import ApproveIcon from './icons/ApproveIcon.vue'
import RejectIcon from './icons/RejectIcon.vue'
import {
  frappeGetDoc,
  frappeSetValue,
  frappeApplyWorkflow,
} from '../utils/frappeAPI'

const props = defineProps({
  employee: Object,
  weekRange: Object,
  weekDays: Array
});

const emit = defineEmits(['close', 'update:employee'])

// State variables
const timesheetDetails = ref([])
const isLoading = ref(false)
const employees = ref([])
const selectedEmployeeId = ref(null)
const rejectedReason = ref('')
// const rejectedBy = ref('')

// Computed properties
const formattedDateRange = computed(() => {
  if (!props.weekRange.start || !props.weekRange.end) return 'No date range selected'

  const start = formatDate(props.weekRange.start, 'MMM DD, YYYY')
  const end = formatDate(props.weekRange.end, 'MMM DD, YYYY')
  return `${start} - ${end}`
})

const employeeOptions = computed(() => {
  return employees.value.map(emp => ({
    value: emp.id,
    label: emp.name
  }))
})

// Methods
const close = () => {
  emit('close')
}

const handleEmployeeChange = () => {
  if (selectedEmployeeId.value) {
    const selectedEmp = employees.value.find(emp => emp.id === selectedEmployeeId.value)
    if (selectedEmp) {
      emit('update:employee', selectedEmp)
    }
  }
}

// Calculate total hours for each day across all timesheet rows
const calculateDayTotal = (day) => {
  let totalMinutes = 0
  for (const row of timesheetDetails.value) {
    totalMinutes += parseTime(row[day.dayName.toLowerCase()])
  }
  return formatTime(totalMinutes)
}

const fetchEmployees = async () => {
  try {
    const getReportees = createResource({
      url: 'inspira.inspira.api.hrms.hr_utils.get_reportees',
      makeParams: () => ({
        manager_hr_id: window.emp_id
      }),
      auto: true,
      onSuccess: (data) => {
        data.forEach(d => {
          employees.value.push({
            id: d.name,
            name: d.employee_name
          })
        })
      },
      onError: (error) => {
        console.error('Failed to fetch tasks:', error);
        toast({
          title: 'Error',
          text: error.messages?.[0] || 'Failed to fetch tasks',
          icon: 'alert-circle',
          position: 'bottom-right',
          iconClasses: 'text-red-500',
        });
      },
    })

    // Set the selected employee in the autocomplete
    if (props.employee) {
      selectedEmployeeId.value = props.employee.id
    }
  } catch (error) {
    console.error('Error fetching employees:', error)
    toast({
      title: 'Error',
      text: 'Failed to fetch employees',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
  }
}


let updateMilestones = async (row) => {
  let milestones = []
  row.forEach(r => {
    milestones.push(r.milestone)
  })
  createListResource({
    doctype: 'IDP Task',
    fields: ['name as id', 'subject as name'],
    orderBy: 'creation desc',
    filters: [["IDP Task", "name", "in", milestones]],
    start: 0,
    auto: 1,
    pageLength: 5,
    onSuccess(data) {
      row.forEach(r => {
        data.forEach(d => {
          if (r.milestone == d.id) {
            r.milestone = d.name
          }
        })
      })
    },
  })
}
const fetchTimesheetDetails = async () => {
  if (!props.employee) return

  isLoading.value = true

  try {
    // await new Promise(resolve => setTimeout(resolve, 500))
    const getDocParams = {
      doctype: 'IDP Timesheet',
      filters: {
        start_date: formatDate(props.weekRange.start, 'YYYY-MM-DD'),
        end_date: formatDate(props.weekRange.end, 'YYYY-MM-DD'),
        employee: props.employee.id,
        docstatus: ['!=', 2],
      },
    }

    const successFunc = async (data) => {
      // console.log("data kaha aara yaar",data.rejection_reason);
      rejectedReason.value = data?.rejection_reason;
      timesheetDetails.value = data.weekly_timesheets
      updateMilestones(timesheetDetails.value)

    }


    frappeGetDoc({
      getDocParams: getDocParams,
      successFunc: successFunc,
    })

    // timesheetDetails.value = [
    //   {
    //     project: 'Project A',
    //     milestone: 'Milestone A',
    //     budgetedHours: 50,
    //     hours: ['1h 1m', '–', '–', '–', '–', '–', '–'],
    //   },
    //   {
    //     project: 'Project A',
    //     milestone: 'Milestone B',
    //     budgetedHours: 30,
    //     hours: ['1h 1m', '–', '–', '–', '–', '–', '–'],
    //   },
    //   {
    //     project: 'Project A',
    //     milestone: 'Milestone C',
    //     budgetedHours: -20,
    //     hours: ['1h 1m', '–', '–', '–', '–', '–', '–'],
    //   }
    // ]
  } catch (error) {
    console.error('Error fetching timesheet details:', error)
    toast({
      title: 'Error',
      text: 'Failed to fetch timesheet details',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
  } finally {
    isLoading.value = false
  }
}

const getInitials = (name) => {
  if (!name) return ''
  return name
    .split(' ')
    .map(part => part.charAt(0))
    .join('')
    .toUpperCase()
}

// Get CSS class for day status indicator based on total hours
const getDayStatusClass = (day) => {
  let totalMinutes = 0
  for (const row of timesheetDetails.value) {
    totalMinutes += parseTime(row[day.dayName.toLowerCase()] ? row[day.dayName.toLowerCase()] : 0)
  }
  const totalHours = totalMinutes / 60

  if (totalHours === 0) return 'bg-red-500'
  if (totalHours < 8) return 'bg-yellow-500'
  return 'bg-green-500'
}

const getBudgetStatus = (row) => {
  return row.budgeted_hours > 0 ? 'success' : 'warning'
}

const parseTime = (timeStr) => {
  if (!timeStr || timeStr === '–') return 0

  let hours = 0
  let minutes = 0

  if (timeStr.includes('h')) {
    const parts = timeStr.split('h')
    hours = parseInt(parts[0]) || 0

    if (parts[1] && parts[1].includes('m')) {
      minutes = parseInt(parts[1].replace('m', '').trim()) || 0
    }
  } else if (timeStr.includes(':')) {
    const [h, m] = timeStr.split(':')
    hours = parseInt(h) || 0
    minutes = parseInt(m) || 0
  }

  return hours * 60 + minutes
}

const formatTime = (minutes) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h ${mins}m`
}

const calculateRowTotal = (hours) => {
  return formatTime(hours)
}

onMounted(() => {
  fetchEmployees()

  if (props.employee) {
    fetchTimesheetDetails()
  }
})

// Watch for changes in employee prop
watch(() => props.employee, (newEmployee) => {
  if (newEmployee) {
    selectedEmployeeId.value = newEmployee.id
    fetchTimesheetDetails()
  } else {
    timesheetDetails.value = []
  }
}, { immediate: true })



const timesheetStatus = ref('')
const showRejectionDialog = ref(false)
const rejectionReason = ref('')

const approveTimesheet = () => {
  const applyWorkflowParams = {
    docname: timesheetDetails.value[0].parent,
    doctype: 'IDP Timesheet',
    action: 'Approve',
  }

  const successFunc = (data) => {
    timesheetStatus.value = data?.workflow_state
  }
  const successMsg = `Timesheet for ${props.employee.name} approved`
  const errorMsg = 'Error while approving timesheet'

  frappeApplyWorkflow({
    applyWorkflowParams: applyWorkflowParams,
    successFunc: successFunc,
    successMsg: successMsg,
    errorMsg: errorMsg,
  })
}

const showRejectDialog = () => {
  rejectionReason.value = ''
  showRejectionDialog.value = true
}

const rejectTimesheet = () => {
  if (!rejectionReason.value.trim()) {
    toast({
      title: 'Error',
      text: 'Please enter a rejection reason',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
    return
  }

  const applyWorkflowParams = {
    docname: timesheetDetails.value[0].parent,
    doctype: 'IDP Timesheet',
    action: 'Reject',
  }

  const frappeApplyWorkflowSuccessFunc = (data) => {
    timesheetStatus.value = data?.workflow_state
  }
  const successMsg = `Timesheet for ${props.employee.name} rejected`
  const errorMsg = 'Error while rejecting timesheet'


  const setValueParams = {
    name: timesheetDetails.value[0].parent,
    doctype: 'IDP Timesheet',
    fieldname: 'rejection_reason',
    value: rejectionReason.value.trim(),
  }

  const frappeSetValueSuccessFunc = async (data) => {
    frappeApplyWorkflow({
      applyWorkflowParams: applyWorkflowParams,
      successFunc: frappeApplyWorkflowSuccessFunc,
      successMsg: successMsg,
      errorMsg: errorMsg,
    })

    showRejectionDialog.value = false
  }


  frappeSetValue({
    setValueParams: setValueParams,
    successFunc: frappeSetValueSuccessFunc,
  })

}
watch(() => props.employee, (newEmployee) => {
  if (newEmployee) {
    timesheetStatus.value = newEmployee.workflow_state || ''
  }
}, { immediate: true })
</script>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

::v-deep .w-full button {
  background-color: #fff;
  border: 1px solid rgb(229, 228, 228);
}
</style>