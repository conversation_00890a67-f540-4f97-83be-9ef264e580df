<template>
  <div class="p-4 flex flex-col">
    <div class="flex items-center justify-between">
      <div class="w-6/12 flex items-center gap-2">
        <WeekRangePicker v-model="selectedWeekRange" @update:modelValue="handleWeekRangeChange" />
        <button @click="selectCurrentWeek"
          class="text-sm px-3 py-1.5 bg-white border rounded text-[#65558F] font-medium shadow hover:bg-gray-50">
          This Week
        </button>
      </div>
      <div class="text-sm text-gray-700">
        <span class="px-3 py-1 rounded-full text-sm font-medium inline-flex items-center gap-2" :class="{
          'bg-gray-100 text-gray-800': timeSheet.workflow_state === STATUSES.DRAFT,
          'bg-blue-100 text-blue-800': timeSheet.workflow_state === STATUSES.PENDING,
          'bg-green-100 text-green-800': timeSheet.workflow_state === STATUSES.APPROVED,
          'bg-red-100 text-red-800': timeSheet.workflow_state === STATUSES.REJECTED
        }">
          <component :is="getStatusIcon(timeSheet.workflow_state)" class="w-4 h-4" />
          {{ timeSheet.workflow_state || STATUSES.DRAFT }}
        </span>
      </div>
    </div>

    <div v-if="selectedWeekRange.start && selectedWeekRange.end" class="w-full mt-6 border-2 rounded-lg">
      <table class="w-full border-collapse">
        <!-- Table Header -->
        <thead>
          <tr class="border-b">
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Project
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Milestone
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600">
              Remaining Budgeted Hours
            </th>
            <template v-for="(day, index) in weekDays" :key="index">
              <th class="p-1 border-r text-sm text-center font-medium text-gray-600">
                <div class="flex flex-col justify-between gap-1">
                  <div>{{ day.name }}</div>
                  <div class="text-xs text-gray-500">
                    {{ calculateDayTotal(index) }}
                  </div>
                  <div>
                    <hr :class="getDayStatusClass(index)" class="h-0.5 rounded" />
                  </div>
                </div>
              </th>
            </template>
            <th class="p-3 border-r text-sm text-center font-medium text-gray-600">
              Total
            </th>
            <th class="p-3 border-r text-sm text-left font-medium text-gray-600"></th>
          </tr>
        </thead>

        <!-- Table Body -->
        <tbody>
          <tr v-for="(row, rowIndex) in timeSheet.weeklyTimesheets" :key="rowIndex" class="border-b">
            <td class="p-3 border-r text-sm">
              <Link class="text-sm text-ink-gray-8" v-model="row.project" :doctype="'IDP Project'"
                :placeholder="'Select'" :disabled="!isTimesheetEditable"
                @update:modelValue="(_) => handleProjectChange(row)" />
            </td>
            <td class="p-3 border-r text-sm">
              <Link class="text-sm text-ink-gray-8" v-model="row.milestone" :placeholder="'Select'"
                :doctype="'IDP Task'" :showDesc="true" :disabled="!isTimesheetEditable"
                @update:modelValue="(_) => getBudgetedHoursByProject(row)" :filters="[
                  ['project', '=', row.project],
                  ['type', '=', 'Milestone'],
                ]" />
            </td>
            <td class="p-3 border-r text-sm">
              <div class="flex items-center gap-2">
                {{ row.budgetedHours || 0 }} hours
                <span v-if="getRowStatus(row) === 'success'" class="text-green-500">
                  <GreenVector />
                </span>
                <span v-else-if="getRowStatus(row) === 'warning'" class="text-red-500">
                  <RedVector />
                </span>
              </div>
            </td>
            <td v-for="(day, dayIndex) in weekDays" :key="dayIndex" class="p-1 border-r text-sm">
              <input type="text" v-model="row.hours[dayIndex]" :disabled="!isTimesheetEditable"
                @input="validateHourInput(row.hours, dayIndex)" @blur="formatHourInput(row.hours, dayIndex)"
                placeholder="00:00" />
            </td>
            <td class="p-3 border-r text-sm text-center">
              {{ calculateRowTotal(row.hours) }}
            </td>
            <td class="p-3 border-r text-sm text-center">
              <button @click="removeTimeSheetRow(rowIndex)" :disabled="!isTimesheetEditable"
                :class="{ 'opacity-50 cursor-not-allowed': !isTimesheetEditable }" class="text-red-500">
                <RemoveIcons />
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Bottom Controls -->
      <div class="flex justify-between gap-4 items-center bg-[#F3F1F6] p-2 rounded-b-lg">
        <div class="flex gap-2">
          <button @click="addTimeSheetRow" :disabled="!isTimesheetEditable"
            :class="{ 'opacity-50 cursor-not-allowed': !isTimesheetEditable }"
            class="flex items-center text-[14px] gap-2 px-4 py-1 bg-white border rounded-full text-[#65558F] font-medium shadow-xl">
            <AddSectionIcon />
            Add Timesheet Row
          </button>

          <button @click="copyLastWeek" :disabled="!isTimesheetEditable"
            :class="{ 'opacity-50 cursor-not-allowed': !isTimesheetEditable }"
            class="flex items-center text-[14px] gap-2 px-4 py-1 bg-white border rounded-full text-[#65558F] font-medium shadow-xl">
            <CopyAllIcon />
            Copy Last Week
          </button>
        </div>

        <div class="flex gap-2">
          <button @click="saveTimeSheet" :disabled="!isTimesheetEditable"
            :class="{ 'opacity-50 cursor-not-allowed': !isTimesheetEditable }"
            class="ml-auto text-[14px] px-4 py-1 bg-white text-[#65558F] font-medium shadow-xl rounded-lg">
            Save
          </button>
          <button @click="submitTimeSheet" :disabled="!isTimesheetEditable"
            :class="{ 'opacity-50 cursor-not-allowed': !isTimesheetEditable }"
            class="ml-auto text-[14px] px-4 py-1 bg-white text-[#65558F] font-medium shadow-xl rounded-lg">
            Submit
          </button>
        </div>
      </div>
    </div>

    <div v-else
      class="flex flex-col items-center shadow-2xl rounded-md p-12 w-100 h-auto cursor-pointer mb-12 mt-12 text-gray-500">
      <FeatherIcon :name="'clock'" class="h-16 w-12" /> <br />
      <p>Please select a week range to fill the time sheet</p>
    </div>


    <div v-if="timeSheet.workflow_state === STATUSES.REJECTED"
      class="w-full flex flex-col bg-gray-50 mt-8 p-4 rounded-xl text-gray-700">
      <span>Rejected by: {{ timeSheet.rejected_by }}</span>
      <span class="text-sm">Reason: {{ timeSheet.rejection_reason }}</span>
    </div>

    <Dialog v-model="showSubmitConfirm">
      <template #body-title>
        <h3>Confirm Submission</h3>
      </template>
      <template #body-content>
        <div class="mb-4">
          <p class="text-gray-700">Are you sure you want to submit this timesheet?</p>
          <p class="text-sm text-gray-500 mt-2">Once submitted, you won't be able to make changes.
          </p>
        </div>
      </template>
      <template #actions>
        <Button variant="solid" theme="gray" @click="savesubmitTimeSheet">
          Submit
        </Button>
        <Button class="ml-2" @click="showSubmitConfirm = false">
          Cancel
        </Button>
      </template>
    </Dialog>

  </div>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { FeatherIcon, toast, Dialog, Button } from 'frappe-ui'
import Link from '@/components/Link.vue'
import WeekRangePicker from './WeekRangePicker.vue'
import RemoveIcons from '../components/icons/RemoveIcons.vue'
import CopyAllIcon from './icons/CopyAllIcon.vue'
import AddSectionIcon from '../components/icons/AddSectionIcon.vue'
import GreenVector from '../components/icons/GreenVector.vue'
import RedVector from '../components/icons/RedVector.vue'
import { formatDate } from '../utils/format'
import {
  frappeGetDoc,
  frappeInsertDoc,
  frappeGetValue,
  frappeSetValue,
  frappeApplyWorkflow,
} from '../utils/frappeAPI'
import { Check, X, MoveUpRight, CalendarClock } from 'lucide-vue-next'

const selectedWeekRange = ref({ start: null, end: null })
const timeSheet = ref({})
const weekDays = ref([])
const errorShown = ref(false)

const days = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
]

const STATUSES = {
  DRAFT: 'Not Submitted',
  PENDING: 'Sent For Approval',
  APPROVED: 'Approved',
  REJECTED: 'Rejected'
}

const getStatusIcon = (status) => {
  switch (status) {
    case STATUSES.APPROVED:
      return Check
    case STATUSES.REJECTED:
      return X
    case STATUSES.PENDING:
      return MoveUpRight
    case STATUSES.DRAFT:
    default:
      return CalendarClock
  }
}

const isTimesheetEditable = computed(() => {
  return timeSheet.value.workflow_state === STATUSES.DRAFT ||
    timeSheet.value.workflow_state === STATUSES.REJECTED ||
    !timeSheet.value.workflow_state
})

// Populate week days based on selected range
const updateWeekDays = () => {
  if (!selectedWeekRange.value.start || !selectedWeekRange.value.end) return

  const days = []
  const startDate = new Date(selectedWeekRange.value.start)
  const endDate = new Date(selectedWeekRange.value.end)

  // Loop through each day of the week
  const currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    const dayName = currentDate.toLocaleDateString('en-US', {
      weekday: 'short',
    })
    const monthName = currentDate.toLocaleDateString('en-US', {
      month: 'short',
    })
    const date = currentDate.getDate()

    days.push({
      name: `${dayName}, ${monthName} ${date}`,
      date: formatDate(currentDate, 'YYYY-MM-DD'),
      hours: '0h',
    })

    currentDate.setDate(currentDate.getDate() + 1)
  }

  weekDays.value = days
}

// Handle week range change
const handleWeekRangeChange = () => {
  errorShown.value = false
  updateWeekDays()

  // Clear timeSheet when date range changes
  timeSheet.value = { weeklyTimesheets: [] }

  // Fetch timesheet using API
  const successFunc = async (data) => {
    await updateTimeSheetValues(data)
  }

  const errorFunc = (error) => {
    // Timesheet not found then add 1 row
    if (!errorShown.value && error.toString().includes('DoesNotExistError')) {
      addTimeSheetRow()
      errorShown.value = true
      toast({
        title: 'Error',
        text: 'Timesheet not found',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
    }
  }

  const getDocParams = {
    doctype: 'IDP Timesheet',
    filters: {
      start_date: formatDate(selectedWeekRange.value.start, 'YYYY-MM-DD'),
      end_date: formatDate(selectedWeekRange.value.end, 'YYYY-MM-DD'),
      employee: window.emp_id,
      docstatus: ['!=', 2],
    },
  }
  frappeGetDoc({
    getDocParams: getDocParams,
    successFunc: successFunc,
    errorFunc: errorFunc,
  })
}

const handleProjectChange = (row) => {
  row.milestone = null
  row.budgetedHours = 0
}

const getBudgetedHoursByProject = async (row) => {
  if (!row.project || !row.milestone) return 0

  if (!window.designation) {
    toast({
      title: 'Error',
      text: 'Designation not set for the user, unable to fetch budgeted hours',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
    return
  }

  const getValueParams = {
    doctype: 'IDP Designation Project Effort Mapping',
    fieldname: 'pending_no_of_hours',
    filters: {
      parent: row.project,
      designation: window.designation,
    },
    parent: 'IDP Project',
  }

  return new Promise((resolve) => {
    frappeGetValue({
      getValueParams: getValueParams,
      successFunc: (data) => {
        if (data.pending_no_of_hours) {
          row.budgetedHours = data.pending_no_of_hours
        }
        resolve(row.budgetedHours)
      },
      errorFunc: () => {
        resolve(0)
      },
    })
  })
}

const parseTime = (timeStr) => {
  if (!timeStr || timeStr === '') return 0

  let hours = 0
  let minutes = 0

  if (timeStr.includes(':')) {
    const [h, m] = timeStr.split(':')
    hours = parseInt(h) || 0
    minutes = parseInt(m) || 0
  } else {
    hours = parseInt(timeStr) || 0
  }

  return hours * 60 + minutes
}

const formatTime = (minutes) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h ${mins}m`
}

const calculateRowTotal = (hours) => {
  let totalMinutes = 0
  for (const timeStr of hours) {
    totalMinutes += parseTime(timeStr)
  }
  return formatTime(totalMinutes)
}

const calculateDayTotal = (dayIndex) => {
  let totalMinutes = 0
  for (const row of timeSheet.value.weeklyTimesheets) {
    totalMinutes += parseTime(row.hours[dayIndex])
  }
  return formatTime(totalMinutes)
}

const calculateWeekTotal = () => {
  let totalMinutes = 0
  for (const row of timeSheet.value.weeklyTimesheets) {
    for (const timeStr of row.hours) {
      totalMinutes += parseTime(timeStr)
    }
  }
  return formatTime(totalMinutes)
}

// Input validation
const validateHourInput = (hours, dayIndex) => {
  let input = hours[dayIndex]
  input = input.replace(/[^0-9:]/g, '')
  if (input === '') {
    hours[dayIndex] = ''
    return
  }
  if (!input.includes(':') && input.length >= 2) {
    const hoursPart = input.slice(0, 2)
    input = `${hoursPart}:${input.slice(2)}`
  }
  const colonCount = (input.match(/:/g) || []).length
  if (colonCount > 1) {
    const parts = input.split(':')
    input = `${parts[0]}:${parts[1]}`
  }
  let [h, m] = input.includes(':') ? input.split(':') : [input, '']
  h = h.slice(0, 2)
  let hoursNum = parseInt(h) || 0
  if (hoursNum > 23) {
    h = '23'
    hoursNum = 23
  }
  if (input.includes(':')) {
    m = m.slice(0, 2)
    let minsNum = parseInt(m) || 0
    if (minsNum > 59) {
      m = '59'
      minsNum = 59
    }
    input = `${h}:${m}`
  } else {
    input = h
  }

  hours[dayIndex] = input
}
const validateDailyTotals = () => {
  const dayTotals = weekDays.value.map((_, dayIndex) => {
    return timeSheet.value.weeklyTimesheets.reduce((total, row) => {
      const [h, m] = (row.hours[dayIndex] || '0:00').split(':').map(Number)
      return total + h * 60 + (m || 0)
    }, 0)
  })

  return dayTotals.some(total => total > 1439) // 1439 minutes = 23:59
}

const formatHourInput = (hours, dayIndex) => {
  let input = hours[dayIndex]

  if (!input || input === '') {
    hours[dayIndex] = '0:00' // Default value when empty
    return
  }

  // Handle incomplete inputs
  if (input.endsWith(':')) {
    input = input + '00' // Complete partial input
  } else if (!input.includes(':')) {
    input = input + ':00' // Add minutes if missing
  }

  let [h, m] = input.split(':')
  h = Math.min(parseInt(h) || 0, 24)
  m = Math.min(parseInt(m) || 0, 59).toString().padStart(2, '0')

  hours[dayIndex] = `${h}:${m}`
}


const getRowStatus = (row) => {
  // if (!row.project || !row.milestone) return 'warning'
  // let totalMinutes = 0
  // for (const timeStr of row.hours) {
  //   totalMinutes += parseTime(timeStr)
  // }
  // const totalHours = totalMinutes / 60
  return row.budgetedHours > 0 ? 'success' : 'warning'
}

const getDayStatusClass = (dayIndex) => {
  let totalMinutes = 0
  for (const row of timeSheet.value.weeklyTimesheets) {
    totalMinutes += parseTime(row.hours[dayIndex])
  }
  const totalHours = totalMinutes / 60

  if (totalHours === 0) return 'bg-red-500'
  if (totalHours < 8) return 'bg-yellow-500'
  return 'bg-green-500'
}

const updateDayTotals = () => {
  for (let i = 0; i < weekDays.value.length; i++) {
    const total = calculateDayTotal(i)
    weekDays.value[i].hours = total
  }
}

watch(
  timeSheet,
  () => {
    updateDayTotals()
  },
  { deep: true }
)

// Actions
const addTimeSheetRow = () => {
  timeSheet.value.weeklyTimesheets.push({
    name: '',
    project: '',
    milestone: '',
    hours: Array(weekDays.value.length).fill('00:00'),
  })
}

const removeTimeSheetRow = (index) => {
  timeSheet.value.weeklyTimesheets.splice(index, 1)
}

const copyLastWeek = async () => {
  errorShown.value = false

  const prevWeekStartDate = new Date(selectedWeekRange.value.start)
  prevWeekStartDate.setDate(prevWeekStartDate.getDate() - 7)

  const prevWeekEndDate = new Date(selectedWeekRange.value.end)
  prevWeekEndDate.setDate(prevWeekEndDate.getDate() - 7)

  timeSheet.value = { weeklyTimesheets: [] }
  const successFunc = async (data) => {
    await updateTimeSheetValues(data, true)
  }

  const errorFunc = (error) => {
    // Timesheet not found then add 1 row
    if (!errorShown.value && error.toString().includes('DoesNotExistError')) {
      addTimeSheetRow()
      errorShown.value = true
      toast({
        title: 'Error',
        text: 'Timesheet not found',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
    }
  }

  const getDocParams = {
    doctype: 'IDP Timesheet',
    filters: {
      start_date: formatDate(prevWeekStartDate, 'YYYY-MM-DD'),
      end_date: formatDate(prevWeekEndDate, 'YYYY-MM-DD'),
      employee: window.emp_id,
      docstatus: ['!=', 2],
    },
  }

  frappeGetDoc({
    getDocParams: getDocParams,
    successFunc: successFunc,
    errorFunc: errorFunc,
  })
}

const saveTimeSheet = () => {

  if (validateDailyTotals()) {
    toast({
      title: 'Daily Limit Exceeded',
      text: 'Total time across projects cannot exceed 23:59 for any day',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500'
    })
    return
  }

  let error = ''
  timeSheet.value.weeklyTimesheets.forEach((tm) => {
    if (!tm.project || !tm.milestone) {
      error = 'Project and Milestone are mandatory fields.'
    }
    let isRowZero = true
    // At least one hour should be non-zero
    tm.hours.forEach((hour) => {
      if (hour !== '00:00') {
        isRowZero = false
      }
    })
    if (isRowZero) {
      // If Error has value then adding space.
      error += error ? ' Hours cannot be zero.' : 'Hours cannot be zero.'
    }
  })

  if (error) {
    toast({
      title: 'Error',
      text: error,
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
    return
  }

  if (!timeSheet.value.name) {
    createTimeSheet()
  } else {
    updateTimeSheet()
  }
}

const createTimeSheet = () => {
  if (!window.emp_id) {
    toast({
      title: 'Error',
      text: 'Employee mapping not found for your user.',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
    return
  }
  const insertDocParams = {
    doc: {
      doctype: 'IDP Timesheet',
      employee: window.emp_id,
      weekly_timesheets: getWeeklyTimeSheetObj(),
    },
  }

  const successFunc = async (data) => {
    timeSheet.value = { weeklyTimesheets: [] }
    await updateTimeSheetValues(data)
  }

  const successMsg = 'Timesheet created successfully'
  const errorMsg = 'Error while saving timesheet'

  frappeInsertDoc({
    insertDocParams: insertDocParams,
    successFunc: successFunc,
    successMsg: successMsg,
    errorMsg: errorMsg,
  })
}

const updateTimeSheet = () => {
  const setValueParams = {
    name: timeSheet.value.name,
    doctype: 'IDP Timesheet',
    fieldname: 'weekly_timesheets',
    value: getWeeklyTimeSheetObj(),
  }

  const successFunc = async (data) => {
    timeSheet.value = { weeklyTimesheets: [] }
    await updateTimeSheetValues(data)
  }

  const successMsg = 'Timesheet saved successfully'
  const errorMsg = 'Error while saving timesheet'

  frappeSetValue({
    setValueParams: setValueParams,
    successFunc: successFunc,
    successMsg: successMsg,
    errorMsg: errorMsg,
  })
}

const getWeeklyTimeSheetObj = () => {
  const startDate = formatDate(selectedWeekRange.value.start, 'YYYY-MM-DD')
  const endDate = formatDate(selectedWeekRange.value.end, 'YYYY-MM-DD')
  return timeSheet.value.weeklyTimesheets.map((row) => {
    const mappedHours = days.reduce((acc, day, index) => {
      acc[day] = row.hours[index] + ':00' || '00:00:00'
      return acc
    }, {})
    return {
      name: row.name,
      project: row.project,
      milestone: row.milestone,
      from_date: startDate,
      to_date: endDate,
      ...mappedHours,
      total: calculateRowTotal(row.hours),
    }
  })
}

const updateTimeSheetValues = async (data, copyLastWeek = false) => {

  if (!copyLastWeek) {
    timeSheet.value.workflow_state = data?.workflow_state || STATUSES.REJECTED
    timeSheet.value.rejected_by = data?.rejected_by || "NA"
    timeSheet.value.rejection_reason = data?.rejection_reason || "NA"
    timeSheet.value.name = data?.name
  }

  // Clear existing rows
  timeSheet.value.weeklyTimesheets = []

  for (const row of data?.weekly_timesheets || []) {
    const ts_row = {
      name: row.name,
      project: row.project,
      milestone: row.milestone,
      budgetedHours: row.budgeted_hours,
      hours: days.map((day) =>
        String(row[day]).split(':').slice(0, 2).join(':')
      ), // Removing seconds from time 12:00:00 => 12:00
    }

    await getBudgetedHoursByProject(ts_row)
    // timeSheet.value.name = data.name
    timeSheet.value.weeklyTimesheets.push(ts_row)
  }
}

const getCurrentWeekRange = () => {
  const today = new Date()
  const dayOfWeek = today.getDay()
  const diff = today.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1)
  const startDate = new Date(today.setDate(diff))
  const endDate = new Date(startDate)
  endDate.setDate(startDate.getDate() + 6)

  return {
    start: startDate,
    end: endDate
  }
}

// Initialize with current week
onMounted(() => {
  const currentWeek = getCurrentWeekRange()
  const newStart = currentWeek.start.toDateString()
  const currentStart = selectedWeekRange.value.start?.toDateString() || ''

  if (newStart !== currentStart) {
    selectedWeekRange.value = {
      start: new Date(currentWeek.start),
      end: new Date(currentWeek.end)
    }
  }
})

const selectCurrentWeek = () => {
  const currentWeek = getCurrentWeekRange()
  const newStart = currentWeek.start.toDateString()
  const currentStart = selectedWeekRange.value.start?.toDateString() || ''

  if (newStart !== currentStart) {
    selectedWeekRange.value = {
      start: new Date(currentWeek.start),
      end: new Date(currentWeek.end)
    }
  }
}

const showSubmitConfirm = ref(false)

const submitTimeSheet = () => {
  showSubmitConfirm.value = true
}

const savesubmitTimeSheet = async () => {
  // First validate the timesheet
  if (validateDailyTotals()) {
    toast({
      title: 'Daily Limit Exceeded',
      text: 'Total time across projects cannot exceed 23:59 for any day',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500'
    })
    return
  }

  let error = ''
  timeSheet.value.weeklyTimesheets.forEach((tm) => {
    if (!tm.project || !tm.milestone) {
      error = 'Project and Milestone are mandatory fields.'
    }
    let isRowZero = true
    // At least one hour should be non-zero
    tm.hours.forEach((hour) => {
      if (hour !== '00:00') {
        isRowZero = false
      }
    })
    if (isRowZero) {
      // If Error has value then adding space.
      error += error ? ' Hours cannot be zero.' : 'Hours cannot be zero.'
    }
  })

  if (error) {
    toast({
      title: 'Error',
      text: error,
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
    return
  }

  try {
    // Save the timesheet first
    if (!timeSheet.value.name) {
      await createTimeSheetForSubmit()
    } else {
      await updateTimeSheetForSubmit()
    }
    
    // After successful save, apply workflow action directly
    await confirmSubmit()
  } catch (error) {
    toast({
      title: 'Error',
      text: 'Failed to save timesheet before submission',
      icon: 'alert-circle',
      position: 'bottom-right',
      iconClasses: 'text-red-500',
    })
  }
}

// Updated confirmSubmit function to return a Promise and handle workflow
const confirmSubmit = async () => {
  return new Promise((resolve, reject) => {
    const applyWorkflowParams = {
      docname: timeSheet.value.name,
      doctype: 'IDP Timesheet',
      action: 'Submit',
    }

    const successFunc = (data) => {
      timeSheet.value.workflow_state = data?.workflow_state || STATUSES.REJECTED
      timeSheet.value.rejected_by = data?.rejected_by || "Manager Name"
      timeSheet.value.rejection_reason = data?.rejection_reason || "Incorrect hours logged"
      showSubmitConfirm.value = false
      toast({
        title: 'Success',
        text: 'Timesheet submitted successfully',
        icon: 'check',
        iconClasses: 'text-green-500'
      })
      resolve(data)
    }

    const errorFunc = (error) => {
      toast({
        title: 'Error',
        text: 'Error while submitting timesheet',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500'
      })
      reject(error)
    }

    const successMsg = 'Timesheet submitted successfully'
    const errorMsg = 'Error while submitting timesheet'

    frappeApplyWorkflow({
      applyWorkflowParams: applyWorkflowParams,
      successFunc: successFunc,
      errorFunc: errorFunc,
      successMsg: successMsg,
      errorMsg: errorMsg,
    })
  })
}
const createTimeSheetForSubmit = () => {
  return new Promise((resolve, reject) => {
    if (!window.emp_id) {
      toast({
        title: 'Error',
        text: 'Employee mapping not found for your user.',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
      reject(new Error('Employee mapping not found'))
      return
    }

    const insertDocParams = {
      doc: {
        doctype: 'IDP Timesheet',
        employee: window.emp_id,
        weekly_timesheets: getWeeklyTimeSheetObj(),
      },
    }

    const successFunc = async (data) => {
      timeSheet.value = { weeklyTimesheets: [] }
      await updateTimeSheetValues(data)
      resolve(data)
    }

    const errorFunc = (error) => {
      toast({
        title: 'Error',
        text: 'Error while saving timesheet',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
      reject(error)
    }

    frappeInsertDoc({
      insertDocParams: insertDocParams,
      successFunc: successFunc,
      errorFunc: errorFunc,
    })
  })
}

const updateTimeSheetForSubmit = () => {
  return new Promise((resolve, reject) => {
    const setValueParams = {
      name: timeSheet.value.name,
      doctype: 'IDP Timesheet',
      fieldname: 'weekly_timesheets',
      value: getWeeklyTimeSheetObj(),
    }

    const successFunc = async (data) => {
      timeSheet.value = { weeklyTimesheets: [] }
      await updateTimeSheetValues(data)
      resolve(data)
    }

    const errorFunc = (error) => {
      toast({
        title: 'Error',
        text: 'Error while saving timesheet',
        icon: 'alert-circle',
        position: 'bottom-right',
        iconClasses: 'text-red-500',
      })
      reject(error)
    }

    frappeSetValue({
      setValueParams: setValueParams,
      successFunc: successFunc,
      errorFunc: errorFunc,
    })
  })
}

// const confirmSubmit = async () => {
//   const applyWorkflowParams = {
//     docname: timeSheet.value.name,
//     doctype: 'IDP Timesheet',
//     action: 'Submit',
//   }

//   const successFunc = (data) => {
//     timeSheet.value.workflow_state = data?.workflow_state || STATUSES.REJECTED
//     timeSheet.value.rejected_by = data?.rejected_by || "Manager Name"
//     timeSheet.value.rejection_reason = data?.rejection_reason || "Incorrect hours logged"
//   }

//   const successMsg = 'Timesheet submitted successfully'
//   const errorMsg = 'Error while submitting timesheet'

//   frappeApplyWorkflow({
//     applyWorkflowParams: applyWorkflowParams,
//     successFunc: successFunc,
//     successMsg: successMsg,
//     errorMsg: errorMsg,
//   })
//   showSubmitConfirm.value = false
//   try {
//     toast({
//       title: 'Success',
//       text: 'Timesheet submitted successfully',
//       icon: 'check',
//       iconClasses: 'text-green-500'
//     })
//   } catch (error) {
//     toast({
//       title: 'Error',
//       text: error.message,
//       icon: 'x',
//       iconClasses: 'text-red-500'
//     })
//   }
// }

</script>

<style scoped>
::v-deep .w-full button {
  background-color: #fff;
}
</style>

<style scoped>
td:first-child,
th:first-child {
  /* Project column */
  width: 150px;
}

td:nth-child(2),
th:nth-child(2) {
  /* Milestone column */
  width: 150px;
}

td:last-child,
th:last-child {
  /* Action column */
  width: 40px;
}

td:first-child,
td:nth-child(2) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

input {
  border: none !important;
  background: transparent;
  box-shadow: none !important;
  color: #6b7280;
  width: 100%;
  text-align: center;
  padding: 0.25rem;
}

table {
  table-layout: fixed;
  width: 100%;
}
</style>