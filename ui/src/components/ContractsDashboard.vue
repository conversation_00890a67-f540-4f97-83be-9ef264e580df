<template>
  <div class="w-full bg-[#FDFCFE]">
    <!-- Main sections container -->
    <div class="grid grid-cols-1 md:grid-cols-8 gap-4">
      <!-- Contract Info Section -->
      <div class="border-b col-span-3 ">
        <h2 class="text-lg bg-white p-2 border-b">Contract Info</h2>
        <ContractInfoCard :contract="contract" :ContractLink="ContractLink" class="min-h-[250px] max-h-[250px]" />
      </div>

      <!-- Finance Section -->
      <div class="border-b col-span-3 ">
        <h2 class="text-lg bg-white p-2 border-b">Finance</h2>
        <FinanceCard :finance="finance" class="min-h-[250px] max-h-[250px] overflow-auto" />
      </div>

      <!-- Client Info Section -->
      <div class="border-b col-span-2">
        <div class="flex justify-between items-center bg-white border-b pl-2 pr-2">
          <h2 class="text-lg">Client Info</h2>
          <div class="flex bg-gray-300 rounded-full p-0.5 mb-1">
            <button @click="activeClientTab = 'contact'" :class="[
              'px-3 py-1.5 text-sm font-medium',
              activeClientTab === 'contact'
                ? 'text-purple-700 bg-white rounded-full'
                : 'text-gray-700 hover:text-gray-900'
            ]">
              Contact
            </button>
            <button @click="activeClientTab = 'billing'" :class="[
              'px-3 py-1.5 text-sm font-medium',
              activeClientTab === 'billing'
                ? 'text-purple-700 bg-white rounded-full'
                : 'text-gray-700 hover:text-gray-900'
            ]">
              Billing
            </button>
          </div>
        </div>
        <ClientInfoCard :client="client" :activeTab="activeClientTab"
          class="min-h-[250px] max-h-[250px] overflow-auto" />
      </div>
    </div>

    <!-- Milestones Section -->
    <div class="mb-6">
      <h2 class="text-lg bg-white p-2 border-b">Milestones</h2>
      <MilestonesTable :milestones="milestones" />
    </div>

    <!-- Management Sections -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Variable Management Section -->
      <div>
        <h2 class="text-lg bg-white p-2 border-b">Variable Management</h2>
        <VariableManagementTable :variables="variables" />
      </div>

      <!-- Resource Management Section -->
      <div>
        <h2 class="text-lg bg-white p-2 border-b">Resource Management</h2>
        <ResourceManagementTable :resources="resources" />
      </div>
    </div>

    <!-- AreasManagement Section -->
    <div class="mb-6">
      <h2 class="text-lg bg-white p-2 border-b">Areas</h2>
      <AreasManagement :area="area" />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router'
import { createResource } from 'frappe-ui';
import ContractInfoCard from './Contract_Components/ContractInfoCard.vue';
import FinanceCard from './Contract_Components/FinanceCard.vue';
import ClientInfoCard from './Contract_Components/ClientInfoCard.vue';
import MilestonesTable from './Contract_Components/MilestonesTable.vue';
import VariableManagementTable from './Contract_Components/VariableManagementTable.vue';
import ResourceManagementTable from './Contract_Components/ResourceManagementTable.vue';
import AreasManagement from './Contract_Components/AreasManagement.vue';

const route = useRoute()
let projectName = route.params.name
let projectId = ref(route.query.id)

const isLoading = ref(true);
const activeClientTab = ref('contact')

// Contract data
const contract = ref({
  // id: 'TSA-3423t',
  // status: 'Active Not Confirmed',
  // type: 'Delivery',
  // squareFeet: 7000,
  // category: 'Interior',
  // classification: 'Bungalow',
  // subClassification: 'Residential',
  // startDate: '16 Jan 2024',
  // endDate: '30 Jun 2025',
  // projectFee: '₹ 4,00,00,000',
  // totalReceived: '₹ 4,00,00,000',
  // balance: '₹ 0',
  // percentage: '100 %'
});
const ContractLink = ref({
  // Link: 'https://www.google.com/'
})

// Finance data
const finance = ref({
  // costIncurred: 76,
  // projectFee: '₹1,20,00,000',
  // feeReceived: '₹80,00,000',
  // percentageReceived: '66.66%',
  // balanceAmount: '₹40,00,000',
  // timesheetCost: '₹40,00,000',
  // profitability: '35%',
  // status: 'In Budget'
});

// Client data
// const client = ref({
//   name: 'Sara Jay Kumar',
//   email: '<EMAIL>',
//   address: 'Manor House 273, Arthur Lane, Cantonment Road, Mumbai- 400024',
//   phone: '+91 2228083032'
// });

// Client data
const client = ref({
  contact: {
    name: 'Sara Jay Kumar',
    email: '<EMAIL>',
    address: 'Manor House 273, Arthur Lane, Cantonment Road, Mumbai- 400024',
    phone: '+91 2228083032'
  },
  billing: {
    name: 'Waseem Ahmed',
    email: '<EMAIL>',
    address: 'Waseem House 273, Aurangabad, Maharashtra- 431001',
    phone: '+91 9595088029'
  }
});

// Milestones data
const milestones = ref([
  // {
  //   milestone: 'Consultation',
  //   feePercentage: '10%',
  //   feeAmount: '₹6,50,000',
  //   completionPercentage: '10%',
  //   contractEndDate: '02 Mar 2024',
  //   plannedEndDate: '02 Mar 2024',
  //   actualEndDate: '02 Mar 2024',
  //   clientApprovalDate: '02 Mar 2024',
  //   invoiceDate: '04 Mar 2024',
  //   paymentReceived: '₹ 6,50,000',
  //   pendingPayment: '₹ 5,95,000'
  // },
  // {
  //   milestone: 'Concept Development',
  //   feePercentage: '15%',
  //   feeAmount: '₹9,75,000',
  //   completionPercentage: '70%',
  //   contractEndDate: '02 Mar 2024',
  //   plannedEndDate: '02 Mar 2024',
  //   actualEndDate: '02 Mar 2024',
  //   clientApprovalDate: '02 Mar 2024',
  //   invoiceDate: '04 Mar 2024',
  //   paymentReceived: '₹ 6,50,000',
  //   pendingPayment: '₹3,25,000'
  // },
  // {
  //   milestone: 'Design Presentation & Approval',
  //   feePercentage: '10%',
  //   feeAmount: '₹6,50,000',
  //   completionPercentage: '70%',
  //   contractEndDate: '04 Mar 2024',
  //   plannedEndDate: '04 Mar 2024',
  //   actualEndDate: '02 Mar 2024',
  //   clientApprovalDate: '02 Mar 2024',
  //   invoiceDate: '04 Mar 2024',
  //   paymentReceived: '₹ 4,55,000',
  //   pendingPayment: '₹ 1,95,000'
  // },
  // {
  //   milestone: 'Space Planning & Layout Development',
  //   feePercentage: '15%',
  //   feeAmount: '₹9,75,000',
  //   completionPercentage: '20%',
  //   contractEndDate: '04 Mar 2024',
  //   plannedEndDate: '04 Mar 2024',
  //   actualEndDate: '02 Mar 2024',
  //   clientApprovalDate: '02 Mar 2024',
  //   invoiceDate: '04 Mar 2024',
  //   paymentReceived: '₹ 2,50,000',
  //   pendingPayment: '₹ 3,50,000'
  // }
]);

// Variable Management data
const variables = ref([
  // {
  //   name: 'Kiran Mandal',
  //   designation: 'Senior Associate',
  //   variablePercentage: '100 %',
  //   variableCategory: 'Finder\'s Fee',
  //   variableAmount: '₹ 10,00,000',
  //   status: 'Due'
  // }
]);

// Resource Management data
const resources = ref([
  {
    category: 'Internal Team',
    name: 'Kiran Mandal',
    servicesOffered: 'Design',
    email: '<EMAIL>'
  }
]);
// area Management data
const area = ref([
  //{
  //   floor: '10th Floor',
  //   area: 'Balcony Located on the top',
  //   Squarefeet: '430 sq ft'
  // },
]);

watch(
  () => route.query.id,
  (newId) => {
    if (newId) {
      projectId.value = newId
      fetchDashboardData()
    }
  }
);

watch(
  () => route.params.name,
  (newName) => {
    projectName = newName
  }
);


onMounted(() => {
  if (projectId.value) {
    fetchDashboardData();
  }
});

const dashboardResource = createResource({
  url: 'inspira.inspira.api.projects.contracts.get_contract_dashboard_data',
  makeParams: () => ({
    project_id: projectId.value
  }),
  onSuccess: (data) => {
    contract.value = data.contract || {};
    finance.value = data.finance || {};
    client.value = data.client || {};
    milestones.value = data.milestones || [];
    variables.value = data.variables || [];
    resources.value = data.resources || [];
    isLoading.value = false;
    area.value = data.areas || [];
    ContractLink.value = data.ContractLink
  },
  onError: (error) => {
    console.error('Error fetching contract dashboard data:', error);
    isLoading.value = false;
  }
});

// Fetch dashboard data
const fetchDashboardData = () => {
  if (!projectId.value) return;

  isLoading.value = true;
  dashboardResource.submit();
};
</script>
<style scoped>
.overflow-auto {
  scrollbar-width: thin;
}
</style>