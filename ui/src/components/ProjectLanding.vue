<template>
    <div class="w-full">
        <!-- Header -->
        <h1 class="text-3xl font-light text-gray-900 mb-6">Projects</h1>

        <!-- Search and Filters -->
        <div class="mb-4">
            <div class="relative mb-4">
                <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-900 h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <input type="text" placeholder="Search projects..."
                    class="w-full pl-10 pr-4 py-1 border border-gray-300 rounded bg-[#F7F7FD] text-md font-normal 
                    focus:outline-none focus:ring-0 focus:border-gray-300"
                    v-model="searchQuery" @input="debounceSearch">
            </div>

            <div class="w-full flex items-center gap-4">
                <div class="w-48">
                    <Autocomplete v-model="statusFilter" :placeholder="'Select Status'"
                        :showOptions="true" :options="statusMap"  @update:modelValue="fetchProjects">
                    </Autocomplete>
                    <!-- <select class="custom-select" v-model="statusFilter" @change="fetchProjects">
                        <option value="">Status</option>
                        <option value="Open">On Track</option>
                        <option value="On Hold">On Hold</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                    </select>
                    <TrailingDropdown class="custom-dropdown-icon" /> -->
                </div>

                <div class="w-48">
                    <Autocomplete v-model="memberFilter" :placeholder="'Select Member'"
                        :showOptions="true" :options="members" @update:modelValue="fetchProjects">
                    </Autocomplete>
                    <!-- <select class="custom-select" v-model="memberFilter" @change="fetchProjects">
                        <option value="">Members</option>
                        <option v-for="member in members" :key="member.value" :value="member.value">
                            {{ member.label }}
                        </option>
                    </select>
                    <TrailingDropdown class="custom-dropdown-icon" /> -->
                </div>

                <!-- <div class="ml-auto relative">
                    <button 
                        class="px-4 py-2 border border-gray-300 rounded-lg flex items-center gap-2 hover:bg-gray-50"
                        @click="toggleSortOptions">
                        <UpDownIcon />
                        Sort
                        <TrailingDropdown />
                    </button>
                    <div v-if="showSortOptions" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                        <div class="py-1">
                            <button @click="sortProjects('name')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                Name
                            </button>
                            <button @click="sortProjects('progress')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                Progress
                            </button>
                            <button @click="sortProjects('daysLeft')" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                Days Left
                            </button>
                        </div>
                    </div>
                </div> -->
            </div>
        </div>

        <div class="border p-2 border-gray-200 mb-4">
            <!-- Tabs -->
            <div class="border-b border-gray-200 mb-4">
                <nav class="flex gap-4">
                    <button v-for="tab in tabs" :key="tab.name" @click="setCurrentTab(tab.name)" :class="[
                        'px-4 py-2 text-sm font-medium',
                        currentTab === tab.name
                            ? 'border-b border-gray-900 '
                            : 'text-gray-700 hover:text-gray-900'
                    ]">
                        {{ tab.name }} ({{ tab.count }})
                    </button>
                </nav>
            </div>

            <!-- Loading State -->
            <div v-if="isLoading" class="flex justify-center items-center py-10">
                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-gray-900"></div>
            </div>

            <!-- Projects Table -->
            <div v-else class="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-[#ECE6F0]">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">Members</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">Progress</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">Start Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">End Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-900 tracking-wider border-r border-gray-100">Days Left</th>
                        </tr>
                    </thead>
                    <tbody class="bg-[#F7F7FD]">
                        <tr v-for="project in projects" :key="project.id" class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap cursor-pointe " @click="navigateToProject(project.name, project.id)">
                                <div class="text-sm font-medium text-gray-900 w-40 truncate">{{ project.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowra">
                                <span :class="[
                                    'px-6 py-2 text-xs font-medium rounded',
                                    getStatusClass(project.status)
                                ]">
                                    {{ project.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex -space-x-2">
                                    <div v-for="(assignee, index) in project.assignees.slice(0, 10)" :key="index"
                                        class="w-8 h-8 rounded-full bg-[#B1B2D8] flex items-center justify-center text-xs font-medium text-[#171837] ring-2 ring-white">
                                        <!-- {{ getInitialsFromFullName((assignee)) }} -->
                                        <Tooltip :text="assignee.label" :hover-delay="0" :placement="'top'">
                                            <span>
                                                {{ getInitialsFromFullName(assignee) }}
                                            </span>
                                        </Tooltip>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="w-48">
                                    <div class="text-sm text-gray-900 mb-1">{{ project.progress }}%</div>
                                    <div class="w-full bg-gray-200 rounded-full h-1">
                                        <div class="bg-[#5A5B7E] h-1 rounded-full"
                                            :style="{ width: `${project.progress}%` }"></div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-[#171837]">{{ formatDateDDMMMYYYY(project.start_date) }}</td>
                            <td class="px-6 py-4 text-sm text-[#171837]">{{ formatDateDDMMMYYYY(project.end_date) }}</td>
                            <td class="px-6 py-4 text-sm text-[#171837]">{{ project.days_left }}</td>
                        </tr>
                        <tr v-if="projects.length === 0">
                            <td colspan="7" class="px-6 py-10 text-center text-gray-500">
                                No projects found matching your criteria
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import TrailingDropdown from './icons/TrailingDropdown.vue'
import UpDownIcon from './icons/UpDownIcon.vue'
import { createResource, createListResource, Autocomplete, Tooltip } from 'frappe-ui'

const router = useRouter()
const searchQuery = ref('')
const statusFilter = ref('')
const memberFilter = ref('')
const currentTab = ref('Active')
const isLoading = ref(true)
const showSortOptions = ref(false)
const sortField = ref('name')
const sortDirection = ref('asc')

// Map tab names to status values and vice versa
const tabToStatusMap = {
    'Active': 'Open',
    'Complete': 'Completed',
    'On hold': 'On Hold',
    'Dropped': 'Cancelled'
}

const statusToTabMap = {
    'Open': 'Active',
    'Completed': 'Complete',
    'On Hold': 'On hold',
    'Cancelled': 'Dropped'
}

const tabs = ref([
    { name: 'Active', count: 0 },
    { name: 'Complete', count: 0 },
    { name: 'On hold', count: 0 },
    { name: 'Dropped', count: 0 }
])

const projects = ref([])
const members = ref([])

const statusMap = [
    {'label': 'Open', 'value': 'Open'},
    {'label': 'Completed', 'value': 'Completed'},
    {'label': 'On Hold', 'value': 'On Hold'},
    {'label': 'Cancelled', 'value': 'Cancelled'}
]

// Watch for changes in statusFilter and update currentTab accordingly
watch(statusFilter, (newValue) => {
    if (newValue && newValue.value) {
        const tabName = statusToTabMap[newValue.value]
        if (tabName && tabName !== currentTab.value) {
            currentTab.value = tabName
            // No need to call fetchProjects here as it will be triggered by the statusFilter change
        }
    } else {
        currentTab.value = 'Active'
        statusFilter.value = statusMap.find(s => s.value === 'Open')
    }
})

// Fetch members for filter dropdown
const fetchMembers = () => {
    createListResource({
        doctype: 'User',
        fields: ['name as value', 'full_name as label'],
        orderBy: 'full_name asc',
        start: 0,
        pageLength: 500000,
        auto: true,
        onSuccess: (data) => {
            members.value = data
        }
    })
}

// Fetch projects data
const fetchProjects = () => {
    isLoading.value = true
    
    // Prepare filters
    const filters = {
        status: statusFilter.value?.value || tabToStatusMap[currentTab.value] || 'Open',
        search: searchQuery.value,
        member: memberFilter.value?.value
    }
    
    createResource({
        url: 'inspira.inspira.api.projects.projects_landing.get_projects_data',
        params: {
            filters: JSON.stringify(filters)
        },
        auto: true,
        onSuccess: (data) => {
            projects.value = data.projects
            tabs.value = data.tabs
            sortProjects(sortField.value)
            isLoading.value = false
        },
        onError: (error) => {
            console.error('Error fetching projects:', error)
            isLoading.value = false
        }
    })
}

// Set current tab and fetch projects
const setCurrentTab = (tabName) => {
    currentTab.value = tabName
    
    // Update statusFilter to match the selected tab
    const status = tabToStatusMap[tabName]
    if (status) {
        // Find the matching status object in statusMap
        const statusObj = statusMap.find(s => s.value === status)
        if (statusObj) {
            statusFilter.value = statusObj
        }
    }
    
    fetchProjects()
}

// Toggle sort options dropdown
const toggleSortOptions = () => {
    showSortOptions.value = !showSortOptions.value
}

// Sort projects by field
const sortProjects = (field) => {
    sortField.value = field
    
    // If clicking the same field, toggle direction
    if (field === sortField.value) {
        sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
    } else {
        sortDirection.value = 'asc'
    }
    
    projects.value.sort((a, b) => {
        let comparison = 0
        
        if (field === 'name') {
            comparison = a.name.localeCompare(b.name)
        } else if (field === 'progress') {
            comparison = a.progress - b.progress
        } else if (field === 'daysLeft') {
            comparison = a.days_left - b.days_left
        }
        
        return sortDirection.value === 'asc' ? comparison : -comparison
    })
    
    showSortOptions.value = false
}

// Get status class for styling
const getStatusClass = (status) => {
    const statusClasses = {
        'Open': 'bg-green-100 text-green-800',
        'On Hold': 'bg-yellow-100 text-yellow-800',
        'Completed': 'bg-blue-100 text-blue-800',
        'Cancelled': 'bg-red-100 text-red-800'
    }
    
    return statusClasses[status] || 'bg-gray-100 text-gray-800'
}

// Navigate to project detail page
const navigateToProject = (projectName, projectId) => {
    router.push(`/ProjectView/${projectName}?id=${projectId}`)
}

// Debounce search input
let searchTimeout
const debounceSearch = () => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
        fetchProjects()
    }, 300)
}

// Initialize
onMounted(() => {
    fetchMembers()
    
    // Set initial statusFilter based on currentTab
    const initialStatus = tabToStatusMap[currentTab.value]
    if (initialStatus) {
        const statusObj = statusMap.find(s => s.value === initialStatus)
        if (statusObj) {
            statusFilter.value = statusObj
        }
    }
    
    fetchProjects()
    
    // Close sort dropdown when clicking outside
    document.addEventListener('click', (event) => {
        if (!event.target.closest('.custom-dropdown-icon') && !event.target.closest('.custom-select')) {
            showSortOptions.value = false
        }
    })
})

const getInitialsFromFullName = (assignee) => {
    console.log(assignee)
    // assignee  = JSON.parse(assignee)
    let name = assignee?.label
    if (!name) return ''

    return name
        .split(' ')
        .filter((word) => word.length)
        .map((word) => word[0].toUpperCase())
        .join('')
}

const formatDateDDMMMYYYY = (dateString) => {
  if (!dateString) return ''
  const [day, month, year] = dateString.split('-').map(Number)
  const date = new Date(year, month - 1, day)
  if (isNaN(date.getTime())) return dateString 
  const formattedDay = date.getDate().toString().padStart(2, '0')
  const formattedMonth = date.toLocaleString('default', { month: 'short' })
  const formattedYear = date.getFullYear()
  return `${formattedDay}-${formattedMonth}-${formattedYear}`
}
</script>
<style scoped>
.custom-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: transparent;
    border: 1px solid #d1d5db;
    padding: 0.5rem 2rem 0.5rem 1rem;
    border-radius: 0.5rem;
    width: 100%;
    cursor: pointer;
    outline: none;
}

.custom-select:focus {
    outline: none;
    border-color: #d1d5db;
    box-shadow: none;
}

.custom-dropdown-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #6b7280;
}
</style>
