<template>
  <div class="bg-white shadow-sm">
    <!-- Tab Content -->
    <div class="p-4">
      <!-- Contact Tab -->
      <div v-if="activeTab === 'contact'" class="space-y-4 text-sm">
        <!-- Contact Name -->
        <div>
          <span class="text-gray-600 text-sm">Contact Name</span>
          <div class="font-normal text-gray-900">{{ client.contact.name }}</div>
        </div>

        <!-- Email -->
        <div>
          <span class="text-gray-600 text-sm">Email</span>
          <div class="font-normal text-gray-900">{{ client.contact.email }}</div>
        </div>

        <!-- Address -->
        <div>
          <span class="text-gray-600 text-sm">Address</span>
          <div class="font-normal text-gray-900">{{ client.contact.address }}</div>
        </div>

        <!-- Phone -->
        <div>
          <span class="text-gray-600 text-sm">Phone</span>
          <div class="font-normal text-gray-900">{{ client.contact.phone }}</div>
        </div>
      </div>

      <!-- Billing Tab -->
      <div v-if="activeTab === 'billing'" class="space-y-4 text-sm">
        <!-- Billing Name -->
        <div>
          <span class="text-gray-600 text-sm">Billing Name</span>
          <div class="font-normal text-gray-900">{{ client.billing.name }}</div>
        </div>

        <!-- Billing Email -->
        <div>
          <span class="text-gray-600 text-sm">Billing Email</span>
          <div class="font-normal text-gray-900">{{ client.billing.email }}</div>
        </div>

        <!-- Billing Address -->
        <div>
          <span class="text-gray-600 text-sm">Billing Address</span>
          <div class="font-normal text-gray-900">{{ client.billing.address }}</div>
        </div>

        <!-- Phone -->
        <div>
          <span class="text-gray-600 text-sm">Phone</span>
          <div class="font-normal text-gray-900">{{ client.billing.phone }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define props
const props = defineProps({
  client: {
    type: Object,
    required: true
  },
  activeTab: {
    type: String,
    default: 'contact'
  }
});
</script>