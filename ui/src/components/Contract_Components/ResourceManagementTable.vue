<template>
  <div class="overflow-auto relative h-56">
    <table class="min-w-full bg-white border rounded-lg">
      <thead>
        <tr class="bg-[#ECE6F0] text-gray-700 text-xs sticky top-0 z-10">
          <th
            class="sticky left-0 bg-[#ECE6F0] z-10 py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">
            Category</th>
          <th
            class="sticky left-[100px] bg-[#ECE6F0] z-10 py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">
            Name</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Services offered</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Email</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Phone</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Salary Cost</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Seat Cost</th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="resources && resources.length > 0" v-for="(resource, index) in resources" :key="index" class="text-xs bg-[#F7F7FD]">
          <td class="sticky left-0 bg-[#F7F7FD] z-0 py-3 px-4 border-b whitespace-nowrap">{{ resource.category }}</td>
          <td class="sticky left-[100px] bg-[#F7F7FD] z-0 py-3 px-4 border-b whitespace-nowrap">{{ resource.name }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ resource.servicesOffered }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ resource.email }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">-</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">-</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">-</td>
        </tr>
        <tr v-else>
          <td colspan="6" class="py-12 text-center">
            <div class="text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="text-sm font-medium text-gray-500 mb-1">No Data Found</h3>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
// Define props
const props = defineProps({
  resources: {
    type: Array,
    required: true
  }
});
</script>