<template>
  <div class="overflow-auto relative max-h-56">
    <table class="min-w-full bg-white border rounded-lg">
      <thead>
        <tr class="bg-[#ECE6F0] text-gray-700 text-xs sticky top-[-0.5%] z-10">
          <th
            class="sticky left-0 bg-[#ECE6F0] z-10 py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap"
            style="min-width: 180px">Delivery milestone</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Fee %</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Fee Amount</th>
          <th title="Percentage of milestone tasks that are marked “Done” in the Project Planner"
            class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Completion %</th>
          <th
            title="Official contract end date at milestone level recorded when the signed contract is logged in the system"
            class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Contract End Date</th>
          <th title="Maximum Planned End date among all tasks in the Project Planner of this milestone irrespective of status
" class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Planned End Date</th>
          <th title="When all tasks status in planner of this milestone is marked done, the maximum date from the list is picked
" class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Actual End Date</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Client Approval Date</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Status</th>
          <th title="Date the related invoice was issued (pulled from Finance Module) "
            class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Invoice Date</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Payment Received</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Pending Payment</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Payment Date</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Cost Incurred</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Profitability %</th>
        </tr>
      </thead>
      <tbody>
        <tr v-if="milestones && milestones.length > 0" v-for="(milestone, index) in milestones" :key="index"
          class="text-xs bg-[#F7F7FD]">
          <td class="sticky left-0 bg-[#F7F7FD] z-0 py-3 px-4 border-b whitespace-nowrap" style="min-width: 180px">
            {{ milestone.milestone }}
          </td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.feePercentage || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.feeAmount || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.completionPercentage || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ formatDateDDMMMYYYY(milestone.contractEndDate) || '-' }}
          </td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ formatDateDDMMMYYYY(milestone.plannedEndDate) || '-' }}
          </td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ formatDateDDMMMYYYY(milestone.actualEndDate) || '-' }}
          </td>
          <td class="py-1 px-4 border-b whitespace-nowrap relative">
            <div class="relative">
              <div v-if="!milestone.clientApprovalDate"
                class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
                <CalendarIcon class="h-5 w-5 text-gray-400" />
              </div>
              <DatePicker :value="milestone.clientApprovalDate" placeholder=""
                :formatter="(date) => formatDate(date, 'DD-MMM-YYYY')"
                @change="(date) => updateClientApprovalDate(index, date)" />
            </div>
          </td>
          <td class="py-3 px-4 border-b whitespace-nowrap flex items-center justify-center">
            <span class="w-full text-center px-2 py-1 rounded-md text-xs"
              :style="{ backgroundColor: milestone.paymentStatusColor }">
              {{ milestone.paymentStatus || '-' }}
            </span>
          </td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ formatDateDDMMMYYYY(milestone.invoiceDate) || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.paymentReceived || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.pendingPayment || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ formatDateDDMMMYYYY(milestone.paymentDate) || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.costIncurred || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ milestone.profitabilityPercentage || '-' }}</td>
        </tr>
        <tr v-else>
          <td colspan="10" class="py-12 text-center">
            <div class="text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="text-sm font-medium text-gray-500 mb-1">No Data Found</h3>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { DatePicker } from 'frappe-ui'
import CalendarIcon from '../icons/CalendarIcon.vue'
import {
  frappeSetValue,
} from '../../utils/frappeAPI'
import { formatDate } from '../../utils/format'


const props = defineProps({
  milestones: {
    type: Array,
    required: true,
    default: () => [],
  },
})

const emit = defineEmits(['update-client-approval-date'])

const updateClientApprovalDate = (index, formattedDate) => {
  // if (formattedDate) {
  // We should allow resetting of date as well
  const milestone = props.milestones[index]
  const setValueParams = {
    name: milestone.id,
    doctype: 'IDP Milestones',
    fieldname: 'client_approval_date',
    value: formattedDate,
  }
  const successFunc = async (data) => {
    props.milestones[index].clientApprovalDate = formatDate(formattedDate, 'DD-MMM-YYYY')
    props.milestones[index].paymentStatus = props.milestones[index].paymentStatus == "Not Due"?"Due":props.milestones[index]
  }
  const successMsg = 'Client approval date updated successfully'
  const errorMsg = 'Error while updating client approval date'
  frappeSetValue({
    setValueParams: setValueParams,
    successFunc: successFunc,
    successMsg: successMsg,
    errorMsg: errorMsg,
  })
  // }
  console.log('Updated client approval date:', formattedDate)
}
const formatDateDDMMMYYYY = (dateString) => {
  if (!dateString) return ''
  const [day, month, year] = dateString.split('-').map(Number)
  const date = new Date(year, month - 1, day)
  if (isNaN(date.getTime())) return dateString
  const formattedDay = date.getDate().toString().padStart(2, '0')
  const formattedMonth = date.toLocaleString('default', { month: 'short' })
  const formattedYear = date.getFullYear()
  return `${formattedDay}-${formattedMonth}-${formattedYear}`
}
</script>

<style scoped>
.overflow-auto {
  scrollbar-width: thin;
}

::v-deep .w-full {
  background-color: transparent;
  border: transparent;
  font-size: 12px;
  line-height: 1.15;
  letter-spacing: 0.02em;
  font-weight: 400;
  color: black;
}
</style>