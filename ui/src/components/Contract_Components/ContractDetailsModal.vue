<template>
    <Transition name="modal">
      <div v-if="isOpen" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end " @click.self="close">
        <!-- Modal Container -->
        <div 
          class="fixed inset-y-0 right-0 w-full max-w-lg bg-white shadow-lg transform transition-transform duration-100 ease-in-out"
          :class="isOpen ? 'translate-x-0' : 'translate-x-full'"
        >
          <!-- Modal Content -->
          <div class="h-full flex flex-col">
            <!-- Header -->
            <div class="bg-gray-100 p-4">
              <h2 class="text-lg font-medium">Contract Details</h2>
            </div>
  
            <!-- Body -->
            <div class="flex-1 p-6 overflow-y-auto">
              <div class="space-y-6">
                <!-- Contract ID and Status -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Contract ID</label>
                    <input 
                      type="text" 
                      :value="contract.id" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Status</label>
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium bg-green-100 text-green-800">
                        {{ contract.status }}
                      </span>
                  </div>
                </div>
  
                <!-- Type and Category -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Type</label>
                    <input 
                      type="text" 
                      :value="contract.type" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Category</label>
                    <input 
                      type="text" 
                      :value="contract.category" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                </div>
  
                <!-- Classification and Sub-classification -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Classification</label>
                    <input 
                      type="text" 
                      :value="contract.classification" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Sub - classification</label>
                    <input 
                      type="text" 
                      :value="contract.subClassification" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 "
                    >
                  </div>
                </div>
  
                <!-- Start Date and End Date -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Start Date</label>
                    <input 
                      type="text" 
                      :value="contract.startDate" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">End Date</label>
                    <input 
                      type="text" 
                      :value="contract.endDate" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                </div>
  
                <!-- Square feet and Project fee -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Square feet</label>
                    <input 
                      type="text" 
                      :value="contract.squareFeet" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Project fee</label>
                    <input 
                      type="text" 
                      :value="contract.projectFee" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                </div>
  
                <!-- Total Received, Balance, and Percentage -->
                <div class="grid grid-cols-3 gap-4">
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Total Received</label>
                    <input 
                      type="text" 
                      :value="contract.totalReceived" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Balance</label>
                    <input 
                      type="text" 
                      :value="contract.balance" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                  <div>
                    <label class="block text-sm text-gray-600 mb-1">Percentage</label>
                    <input 
                      type="text" 
                      :value="contract.percentage" 
                      readonly
                      class="w-full p-2 border rounded-md bg-gray-50 text-sm"
                    >
                  </div>
                </div>
              </div>
            </div>
  
            <!-- Footer -->
            <div class="p-4 border-t">
              <button
                @click="close"
                class="w-full bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors text-sm"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </template>
  
  <script setup>
  defineProps({
    contract: {
      type: Object,
      required: true
    },
    isOpen: {
      type: Boolean,
      required: true
    }
  });
  
  const emit = defineEmits(['close']);
  
  const close = () => {
    emit('close');
  };
  </script>
  
  <style scoped>
  .modal-enter-active,
  .modal-leave-active {
    transition: opacity 0.3s ease;
  }
  
  .modal-enter-from,
  .modal-leave-to {
    opacity: 0;
  }
  
  .modal-enter-active .transform,
  .modal-leave-active .transform {
    transition: transform 0.3s ease-in-out;
  }
  
  .modal-enter-from .transform,
  .modal-leave-to .transform {
    transform: translateX(100%);
  }
  </style>
  
  