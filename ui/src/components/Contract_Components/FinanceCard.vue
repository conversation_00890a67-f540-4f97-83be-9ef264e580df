<template>
  <div class="flex bg-white shadow-sm p-4">
    <div class="flex justify-center">
      <div class="w-48 h-48 mt-3">
        <apexchart type="radialBar" height="192" :options="chartOptions" :series="[finance.costIncurred]"></apexchart>
      </div>
    </div>

    <!-- Finance Details -->
    <div class="space-y-4 text-sm w-full">
      <div class="flex justify-between">
        <span class="text-gray-600">Project Fee:</span>
        <span class="font-normal">{{ finance.projectFee }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Fee Received:</span>
        <span class="font-normal">{{ finance.feeReceived }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Percentage Received:</span>
        <span class="font-normal">{{ finance.percentageReceived }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Balance Amount:</span>
        <span class="font-normal">{{ finance.balanceAmount }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Timesheet Cost:</span>
        <span class="font-normal">{{ finance.timesheetCost }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-600">Profitability :</span>
        <span class="font-normal">{{ finance.profitability }}</span>
      </div>
      <div class="flex justify-between items-center">
        <span class="text-gray-600">Status:</span>
        <span :class="{
          'bg-green-100 text-green-800': finance.status === 'In Budget',
          'bg-red-100 text-red-800': finance.status === 'Cost Exceeded',
          'bg-yellow-100 text-yellow-800': finance.status === 'Alert'
        }" class="px-3 py-1 rounded-full text-xs">
          {{ finance.status }}
        </span>
      </div>

    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
// Define props
const props = defineProps({
  finance: {
    type: Object,
    required: true
  }
});

// ApexCharts options
const chartOptions = computed(() => {
  return {
    chart: {
      type: 'radialBar',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      }
    },
    plotOptions: {
      radialBar: {
        hollow: {
          size: '60%'
        },
        track: {
          background: '#f1f5f9' // Tailwind slate-100
        },
        dataLabels: {
          name: {
            fontSize: '14px',
            color: '#64748b', // Tailwind slate-500
            offsetY: 0
          },
          value: {
            fontSize: '24px',
            fontWeight: 400,
            color: '#000000',
            formatter: function (val) {
              return val + '';
            }
          }
        }
      }
    },
    colors: ['#B7B6DB'], // Tailwind purple-500
    labels: ['Cost Incurred'],
    stroke: {
      lineCap: 'round'
    }
  };
});
</script>