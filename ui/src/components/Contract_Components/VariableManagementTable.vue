<template>
  <div class="overflow-auto relative h-56">
    <table class="min-w-full bg-white border rounded-lg">
      <thead>
        <tr class="bg-[#ECE6F0] text-gray-700 text-xs sticky top-0 z-10">
          <th
            class="sticky left-0 z-10 bg-[#ECE6F0] py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">
            Name</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">
            Designation</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Variable %</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Variable Category</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Variable Amount</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Status</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Payout Date</th>
          <th class="py-3 px-4 text-left border-b border-r border-gray-100 whitespace-nowrap">Milestone Name</th>
        </tr>
      </thead>
      <tbody>
        <!-- Data rows -->
        <tr v-if="variables && variables.length > 0" v-for="(variable, index) in variables" :key="index"
          class="text-xs bg-[#F7F7FD]">
          <td class="sticky left-0 z-0 bg-[#F7F7FD] py-3 px-4 border-b whitespace-nowrap">{{ variable.name }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ variable.designation
            }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ variable.variablePercentage }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ variable.variableCategory }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ variable.variableAmount }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">
            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs whitespace-nowrap">
              {{ variable.status }}
            </span>
          </td>
          <td class="py-3 px-4 border-b whitespace-nowrap">{{ formatDateDDMMMYYYY('') || '-' }}</td>
          <td class="py-3 px-4 border-b whitespace-nowrap">-</td>
        </tr>

        <!-- No data message row -->
        <tr v-else>
          <td colspan="6" class="py-9 text-center">
            <div class="text-gray-500">
              <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 class="text-sm font-medium text-gray-500 mb-1">No Data Found</h3>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
// Define props
const props = defineProps({
  variables: {
    type: Array,
    required: true
  }
});
const formatDateDDMMMYYYY = (dateString) => {
  if (!dateString) return ''
  const [day, month, year] = dateString.split('-').map(Number)
  const date = new Date(year, month - 1, day)
  if (isNaN(date.getTime())) return dateString 
  const formattedDay = date.getDate().toString().padStart(2, '0')
  const formattedMonth = date.toLocaleString('default', { month: 'short' })
  const formattedYear = date.getFullYear()
  return `${formattedDay}-${formattedMonth}-${formattedYear}`
}
</script>