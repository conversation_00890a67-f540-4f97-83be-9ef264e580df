<template>
  <div class="bg-white shadow-sm p-4 mb-2">
    <ContractDetailsModal :contract="contract" :is-open="isModalOpen" @close="isModalOpen = false" />
    <!-- Contract ID and Status -->
    <div class="flex items-center justify-between mb-2 border rounded-lg p-2">
      <div class="flex items-center">
        <button @click="openContractLink" class="p-1 mr-2 hover:scale-125">
          <LinkIcon />
        </button>

        <!-- <span>Contract {{ contract.id }}</span> -->
        <!-- <button @click="isModalOpen = true" class=" hover:text-purple-600 transition-colors">
          Contract {{ contract.id }}
        </button> -->
        <button>
          {{ contract.id }}
        </button>
      </div>
      <div class="flex items-center gap-2 px-3 py-1 rounded-full text-sm" :style="{ color: contract.statusColor }">
        <span> {{ contract.status }} </span>
        <span class="w-1 h-5 mr-2 rounded" :style="{ backgroundColor: contract.statusColor }"></span>
      </div>

    </div>

    <!-- Contract Details -->
    <div class="grid grid-cols-5 text-xs border rounded-lg overflow-auto mb-2 p-2 shadow-lg">
      <!-- Headers -->
      <div class=" p-1 border-r">Type</div>
      <div class=" p-1 border-r">Square feet</div>
      <div class=" p-1 border-r">Category</div>
      <div class=" p-1 border-r">Classification</div>
      <div class=" p-1">Sub-Class</div>

      <!-- Values -->
      <div class="p-1 font-medium border-r">{{ contract.type }}</div>
      <div class="p-1 font-medium border-r">{{ contract.squareFeet }}</div>
      <div class="p-1 font-medium border-r">{{ contract.category }}</div>
      <div class="p-1 font-medium border-r">{{ contract.classification }}</div>
      <div class="p-1 font-medium">{{ contract.subClassification }}</div>
    </div>

    <!-- Contract Dates -->
    <div class="space-y-2">
      <!-- Start Date -->
      <div class="flex items-center border rounded-lg p-3 bg-[#FEF7FF]">
        <div class="w-1 h-5 bg-blue-600 mr-2 rounded"></div>
        <span class="text-sm text-gray-700 mr-2">Contract Start Date</span>
        <span class="ml-auto text-sm">{{ formatDateDDMMMYYYY(contract.startDate) }}</span>
      </div>

      <!-- End Date -->
      <div class="flex items-center border rounded-lg p-3 bg-[#FEF7FF]">
        <div class="w-1 h-5 bg-red-500 mr-2 rounded"></div>
        <span class="text-sm text-gray-700 mr-2">Contract End Date</span>
        <span class="ml-auto text-sm">{{ formatDateDDMMMYYYY(contract.endDate) }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import LinkIcon from '../icons/LinkIcon.vue'
import ContractDetailsModal from '../Contract_Components/ContractDetailsModal.vue';

const isModalOpen = ref(false);

const props = defineProps({
  contract: {
    type: Object,
    required: true
  },
  ContractLink: {
    type: Object,
    required: true
  }
});

function openContractLink() {
  window.open(props.ContractLink.Link, '_blank', 'noopener');
}
const formatDateDDMMMYYYY = (dateString) => {
  if (!dateString) return ''
  const [day, month, year] = dateString.split('-').map(Number)
  const date = new Date(year, month - 1, day)
  if (isNaN(date.getTime())) return dateString 
  const formattedDay = date.getDate().toString().padStart(2, '0')
  const formattedMonth = date.toLocaleString('default', { month: 'short' })
  const formattedYear = date.getFullYear()
  return `${formattedDay}-${formattedMonth}-${formattedYear}`
}
</script>