<template>
  <div class="bg-white rounded-lg border border-gray-200">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Monthly Expense</h3>
    </div>
    <div class="p-4">
      <apexchart
        type="area"
        height="300"
        :options="chartOptions"
        :series="chartData.series"
      ></apexchart>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  chartData: {
    type: Object,
    required: true
  }
})

const chartOptions = computed(() => {
  return {
    chart: {
      type: 'area',
      fontFamily: 'inherit',
      toolbar: {
        show: false
      },
      zoom: {
        enabled: false
      }
    },
    colors: ['#8B5CF6'],
    fill: {
      type: 'gradient',
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.4,
        opacityTo: 0.1,
        stops: [0, 100]
      }
    },
    stroke: {
      curve: 'smooth',
      width: 2
    },
    grid: {
      show: true,
      borderColor: '#f1f5f9',
      strokeDashArray: 3
    },
    xaxis: {
      categories: props.chartData.categories,
      axisBorder: {
        show: false
      },
      axisTicks: {
        show: false
      },
      labels: {
        style: {
          colors: '#64748b',
          fontSize: '12px'
        }
      }
    },
    yaxis: {
      labels: {
        style: {
          colors: '#64748b',
          fontSize: '12px'
        },
        formatter: function(val) {
          return val.toLocaleString()
        }
      }
    },
    dataLabels: {
      enabled: false
    },
    tooltip: {
      enabled: true,
      theme: 'light',
      y: {
        formatter: function(val) {
          return '₹' + val.toLocaleString()
        }
      }
    },
    legend: {
      show: false
    }
  }
})
</script>