<template>
  <div class="bg-white border border-gray-200">
    <div class="p-2 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Advances History</h3>
    </div>
    
    <!-- Tabs -->
    <div class="flex border-b border-gray-200">
      <button 
        v-for="tab in advances.tabs" 
        :key="tab"
        @click="advances.activeTab = tab"
        class="px-4 py-3 text-sm font-medium relative"
        :class="[
          advances.activeTab === tab 
            ? 'text-gray-900 border-b-2 border-gray-900' 
            : 'text-gray-500 hover:text-gray-700'
        ]"
      >
        {{ tab }}
      </button>
    </div>
    
    <!-- Items List -->
    <div class="p-2 space-y-2">
      <div 
        v-for="item in advances.items" 
        :key="item.id"
        class="flex items-start gap-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors"
      >
        <div class="flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center" :class="item.iconColor">
          <Plane v-if="item.icon === 'plane'" class="w-5 h-5" />
          <Utensils v-else-if="item.icon === 'utensils'" class="w-5 h-5" />
        </div>
        <div class="flex-1 min-w-0">
          <div class="text-sm font-medium text-blue-600 mb-1">{{ item.title }}</div>
          <div class="text-sm text-gray-600 mb-1">{{ item.description }}</div>
          <div class="text-xs text-gray-500">{{ item.duration }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Plane, Utensils } from 'lucide-vue-next'

defineProps({
  advances: {
    type: Object,
    required: true
  }
})
</script>