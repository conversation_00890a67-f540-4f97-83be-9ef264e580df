<template>
  <div class="w-full bg-white border border-gray-200 h-fit">
    <div class="p-2 border-b bg-[#E6E0E9]">
      <h3 class="text-md text-gray-900">Expenses & Advances</h3>
    </div>

    <div class="p-4 space-y-4">
      <!-- Category -->
      <div>
        <label class="block text-sm text-gray-700 mb-2">Category</label>
        <div class="relative">
          <select v-model="formData.category"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none">
            <option>Expense Claim</option>
            <option>Advances Request</option>
          </select>
        </div>
      </div>

      <!-- Purpose -->
      <div v-if="formData.category === 'Advances Request'">
        <label class="block text-sm font-medium text-gray-700 mb-2">Purpose</label>
        <textarea v-model="formData.purpose" rows="2"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
          placeholder="Enter purpose"></textarea>
      </div>

      <!-- Expense Category and Date - Only for Expenses Claim -->
      <div v-if="formData.category === 'Expense Claim'" class="grid grid-cols-2 gap-3">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Expense Category</label>
          <!-- <input v-model="formData.expenseCategory" type="text"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
            placeholder="Category" /> -->
          <Autocomplete v-model="formData.expenseCategory" :options="claimTypeOptions" option-label="label"
            option-value="value" placeholder="Expense Category" class="w-full notifier" />

        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Expense Date</label>
          <input type="date" v-model="formData.expenseDate"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
            placeholder="Date" />
        </div>
      </div>

      <!-- Project and Amount - Always visible -->
      <div class="grid grid-cols-2 gap-3">
        <!-- <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
          <select v-model="formData.project"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none">
            <option>Project 1</option>
            <option>Project 1</option>
          </select>
        </div> -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
          <Autocomplete v-model="formData.project" :options="projectOptions" option-label="label" option-value="value"
            placeholder="Search Project" class="w-full notifier" />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Amount</label>
          <input v-model="formData.amount" type="text"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
            placeholder="Amount" />
        </div>
      </div>

      <!-- Note -->
      <div v-if="formData.category != 'Advances Request'">
        <label class="block text-sm font-medium text-gray-700 mb-2">Note</label>
        <textarea v-model="formData.note" rows="3"
          class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent appearance-none"
          placeholder="Type Here"></textarea>
      </div>

      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Notify</label>
        <!-- <Autocomplete v-model="notify" placeholder="Search Employee" :multiple="true" class="w-full notifier"
          option-label="label" option-value="value" /> -->
        <Autocomplete v-model="formData.notify" placeholder="Search Employee" :multiple="true" class="w-full notifier"
          :options="notifyOptions" option-label="label" option-value="value" />

      </div>

      <!-- Receipt - Only for Expenses Claim -->
      <!-- <div v-if="formData.category === 'Expense Claim'">
        <label class="block text-sm font-medium text-gray-700 mb-2">Attach Receipt</label>
        <input type="file" class="py-2 text-gray-700 text-sm">
        </input>
      </div> -->

      <div v-if="formData.category === 'Expense Claim'">
        <input type="file" @change="handleFileUpload" class="hidden" ref="fileInput" />
        <button @click="$refs.fileInput.click()" class="bg-[#E6E0E9] p-2 rounded-sm text-sm">
          Attach Receipt
        </button>
        <p class="text-xs text-gray-500 mt-1" v-if="formData.attachedFile">
          {{ formData.attachedFile.name }}
        </p>
      </div>

    </div>

    <!-- Action Buttons -->
    <div class="p-4 border-t border-gray-200 flex gap-3 justify-end">
      <button @click="$emit('save')"
        class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-600 transition-colors">
        Save
      </button>
      <!-- <button @click="$emit('submit')"
        class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-700 transition-colors">
        Submit
      </button>
      <button @click="$emit('close')"
        class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-800 transition-colors">
        Close
      </button> -->
    </div>
  </div>
</template>

<script setup>
import { ChevronDown } from 'lucide-vue-next'
import { DatePicker, Autocomplete, createResource, toast } from 'frappe-ui';
import { ref, onMounted } from 'vue';
import { sessionUser } from '../../../data/session'

defineProps({
  formData: {
    type: Object,
    required: true
  }
})
const notify = ref([])
const notifyOptions = ref([])
const login_user = sessionUser()
const projectOptions = ref([])
const claimTypeOptions = ref([])
defineEmits(['save', 'submit', 'close'])

const handleFileUpload = (event) => {
  const file = event.target.files[0]
  if (file) {
    formData.attachedFile = file
  }
}


function get_user() {
  const user = createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'User',
      fields: ['name', 'full_name'],
      filters: [['enabled', '=', 1]],
      limit_page_length: 100
    }),
    auto: true,
    onSuccess: (res) => {
      notifyOptions.value = res.map(user => ({
        label: user.full_name || user.name,
        value: user.name
      }))
    },
    onError: (error) => {
      console.error('Failed to load users:', error)
    },
  })
}
function get_project() {
  createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'IDP Project',
      fields: ['name', 'project_name'],
      filters: [
        ['IDP Project User', 'user', '=', login_user]
      ]
    }),
    auto: true,
    onSuccess: (res) => {
      projectOptions.value = res.map(project => ({
        label: project.project_name || project.name,
        value: project.name
      }))
    },
    onError: (err) => {
      console.error('Project fetch failed:', err)
    }
  })
}
function claim_types() {
  createResource({
    url: 'frappe.client.get_list',
    makeParams: () => ({
      doctype: 'Expense Claim Type',
      fields: ['name', 'expense_type']
    }),
    auto: true,
    onSuccess: (res) => {
      claimTypeOptions.value = res.map(type => ({
        label: type.expense_type || type.name,
        value: type.name
      }))
    },
    onError: (err) => {
      console.error('Failed to fetch claim types:', err)
    }
  })
}

onMounted(() => {
  get_user()
  get_project()
  claim_types()
})
</script>
<style scoped>
::v-deep .notifier button {
  background-color: transparent;
  border: 1px solid rgb(226 226 226);
  border-radius: 0.25rem;
}
</style>