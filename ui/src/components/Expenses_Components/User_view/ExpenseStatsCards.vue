<template>
  <div class="w-full flex flex-col gap-2">
    <div class="w-full flex justify-between gap-4">
      <!-- Deadline -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600">Deadline for Expense Submissions</div>
        <div class="text-lg font-medium text-gray-600">{{ stats.submissionDeadline }}</div>
      </div>
      <!-- Requested Advance Amount -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600 mb-1">Requested Advance Amount</div>
        <div class="text-2xl font-medium text-gray-600">
          ₹ {{ stats.requestedAdvanceAmount }}
          <span class="text-sm font-normal text-gray-500">INR</span>
        </div>
      </div>
    </div>

    <div class="w-full flex justify-between gap-4">
      <!-- Approved Expense Amount -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600 mb-1">Approved Expense Amount</div>
        <div class="text-2xl font-medium text-gray-600">
          ₹ {{ stats.approvedExpenseAmount }}
          <span class="text-sm font-normal text-gray-500">INR</span>
        </div>
      </div>

      <!-- Issued Advance Amount -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600 mb-1">Issued Advance Amount</div>
        <div class="text-2xl font-medium text-gray-600">
          ₹ {{ stats.issuedAdvanceAmount }}
          <span class="text-sm font-normal text-gray-500">INR</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  stats: {
    type: Object,
    required: true
  }
})
</script>