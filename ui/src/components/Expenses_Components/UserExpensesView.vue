<template>
  <div class="flex gap-4 p-2 bg-[#F8F7F9]">
    <!-- Main Content Area -->
    <div class="flex-1">
      <!-- Stats Cards -->
      <ExpenseStatsCards :stats="expenseStats" />

      <!-- History Sections -->
      <div class="grid grid-cols-2 gap-4 mt-4">
        <AdvancesHistory :advances="advancesData" />
        <ExpenseHistory :expenses="expensesData" />
      </div>

      <!-- Monthly Expense Chart -->
      <!-- <MonthlyExpenseChart :chartData="monthlyExpenseData" class="mt-4" /> -->
    </div>

    <!-- Right Sidebar Form -->
    <div class="w-96">
      <ExpenseForm :formData="expenseFormData" @save="handleSave" @submit="handleSubmit" @close="handleClose" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted,watch } from 'vue'
import ExpenseStatsCards from './User_view/ExpenseStatsCards.vue'
import AdvancesHistory from './User_view/AdvancesHistory.vue'
import ExpenseHistory from './User_view/ExpenseHistory.vue'
import MonthlyExpenseChart from './User_view/MonthlyExpenseChart.vue'
import ExpenseForm from './User_view/ExpenseForm.vue'
import { createResource ,toast} from 'frappe-ui'

// Expense Stats Data
const expenseStats = reactive({
  requestedAdvanceAmount: 0,
  issuedAdvanceAmount: 0,
  approvedExpenseAmount: 0,
  submissionDeadline: ''
})
const expenseApprover = ref(null)
// Advances History Data
const advancesData = reactive({
  activeTab: 'Drafts',
  tabs: ['Drafts', 'Approved', 'Rejected'],
  items: [
    // {
    //   id: 1,
    //   title: 'Jun 1st',
    //   description: 'Conveyance Expenses',
    //   duration: '1 day',
    //   icon: 'plane',
    //   iconColor: 'bg-yellow-100 text-yellow-600'
    // },
    // {
    //   id: 2,
    //   title: 'Jun 5th - 12th 2025',
    //   description: 'Client side meeting',
    //   duration: '4 days',
    //   icon: 'plane',
    //   iconColor: 'bg-purple-100 text-purple-600'
    // },
    // {
    //   id: 3,
    //   title: 'Jun 11th',
    //   description: 'Client Lunch',
    //   duration: '1 day',
    //   icon: 'utensils',
    //   iconColor: 'bg-purple-100 text-purple-600'
    // }
  ],
  allItems: {
    Drafts: [],
    Approved: [],
    Rejected: []
  }
})

// Expense History Data
const expensesData = reactive({
  activeTab: 'Drafts',
  tabs: ['Drafts', 'Approved', 'Rejected'],
  items: [
    // {
    //   id: 1,
    //   title: 'Jun 1st',
    //   description: 'Conveyance Expenses',
    //   duration: '1 day',
    //   icon: 'plane',
    //   iconColor: 'bg-yellow-100 text-yellow-600'
    // },
    // {
    //   id: 2,
    //   title: 'Jun 4th - 8th 2025',
    //   description: 'Client side meeting',
    //   duration: '4 days',
    //   icon: 'plane',
    //   iconColor: 'bg-purple-100 text-purple-600'
    // },
    // {
    //   id: 3,
    //   title: 'Jun 11th',
    //   description: 'Client Lunch',
    //   duration: '1 day',
    //   icon: 'utensils',
    //   iconColor: 'bg-purple-100 text-purple-600'
    // }
  ],
  allItems: {
    Drafts: [],
    Approved: [],
    Rejected: []
  }
})

// Monthly Expense Chart Data
// const monthlyExpenseData = reactive({
//   series: [{
//     name: 'Monthly Expenses',
//     data: [1000, 800, 1200, 900, 1500, 1100, 1300, 1000, 1600, 1200, 1400, 1100, 1800, 1300, 1500, 1200, 1700, 1400, 1900, 1500, 1600, 1300, 1100, 1400, 1200, 1500, 1300, 1600, 1400, 2000]
//   }],
//   categories: Array.from({ length: 30 }, (_, i) => i + 1)
// })
const monthlyExpenseData = reactive({
  series: [{
    name: 'Monthly Expenses',
    data: []
  }],
  categories: []
})

// Expense Form Data
const expenseFormData = reactive({
  category: 'Expense Claim',
  purpose: null,
  project: null,
  amount:null,
  expenseCategory: null,
  expenseDate: null,
  advanceDate: null,
  repayFromSalary: false,
  note: null,
  notify: [],
  receipt: null,
  employee:window.emp_id
})

function employee_details() {
  createResource({
    url: 'frappe.client.get_value',
    makeParams: () => ({
      doctype: 'Employee',
      name: window.emp_id,
      fieldname: ['name', 'expense_approver']
    }),
    auto: true,
    onSuccess: (res) => {
      if (res) {
        expenseApprover.value = res.expense_approver
      }
    },
    onError: (err) => {
      console.error('Failed to fetch employee details:', err)
    }
  })
}
function get_claim_details() {
  createResource({
    url: 'inspira.inspira.api.expense_advance.expense_advance.get_claim_details',
    makeParams: () => ({ emp: window.emp_id }),
    auto: true,
    onSuccess: (res) => {
      if (res.claims){
        const grouped = {
          Drafts: [],
          Approved: [],
          Rejected: []
        }

        for (const item of res.claims) {
          const date = new Date(item.expense_date)
          const formattedDate = date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
          })

          const mappedItem = {
            id: Math.random().toString(36).substring(2),
            title: formattedDate,
            description: item.description || item.expense_type,
            duration: '1 day',
            icon: '',
            iconColor: 'bg-purple-100 text-purple-600'
          }

          if (item.approval_status === 'Approved') grouped.Approved.push(mappedItem)
          else if (item.approval_status === 'Rejected') grouped.Rejected.push(mappedItem)
          else grouped.Drafts.push(mappedItem)
        }

        // Save all grouped items persistently
        expensesData.allItems = grouped

        // Show current tab's items
        expensesData.items = grouped[expensesData.activeTab]
      }
      if (res.advances){
        const grouped = {
          Drafts: [],
          Approved: [],
          Rejected: []
        }

        for (const item of res.advances) {
          const date = new Date(item.posting_date)
          const formattedDate = date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric'
          })

          const mappedItem = {
            id: Math.random().toString(36).substring(2),
            title: formattedDate,
            description: item.purpose,
            duration: '1 day',
            icon: '',
            iconColor: 'bg-purple-100 text-purple-600'
          }

          if (item.docstatus === 0) grouped.Drafts.push(mappedItem)
          else if (item.docstatus === 1) grouped.Approved.push(mappedItem)
          else if (item.docstatus === 2) grouped.Rejected.push(mappedItem)
        }

        advancesData.allItems = grouped
        advancesData.items = grouped[advancesData.activeTab]

      }
      if(res.approved_total){
        expenseStats.issuedAdvanceAmount = res.approved_total.total_approved_advance
        expenseStats.approvedExpenseAmount = res.approved_total.total_approved_claim
        expenseStats.requestedAdvanceAmount = res.approved_total.total_requested_advance
        expenseStats.submissionDeadline = res.approved_total.submissionDeadline
      }
      if (res.monthlyExpenseData) {
        monthlyExpenseData.series[0].data = res.monthlyExpenseData.series[0].data
        monthlyExpenseData.categories = res.monthlyExpenseData.categories
      }
    },
    onError: (error) => {
      console.error(error)
    }
  })
}
watch(() => expensesData.activeTab, (newTab) => {
  expensesData.items = expensesData.allItems[newTab]
})
watch(() => advancesData.activeTab, (tab) => {
  advancesData.items = advancesData.allItems[tab]
})

const handleSave = async () => {
  if(expenseFormData.category == "Expense Claim"){
    try {
      const payload = {
        doctype: 'Expense Claim',
        employee: expenseFormData.employee,
        purpose: expenseFormData.purpose || 'Expense Claim',
        idp_project: expenseFormData.project?.value || null,
        note: expenseFormData.note,
        repay_from_salary: expenseFormData.repayFromSalary,
        expense_approver: expenseApprover.value,  // Optional: set if needed
        // List of approvers can also be set in Notify
        notify: expenseFormData.notify.map(n => ({
          user: n.value  // Required child table field
        })), // Just email/user IDs
        expenses: [
          {
            expense_date: expenseFormData.expenseDate,
            expense_type: expenseFormData.expenseCategory?.value,
            amount: parseFloat(expenseFormData.amount),
            description: expenseFormData.note,
            idp_project: expenseFormData.project?.value || null,
            sanctioned_amount:parseFloat(expenseFormData.amount)
          }
        ]
      }

      if (!expenseFormData.project || !expenseFormData.expenseCategory || !expenseFormData.amount || !expenseFormData.expenseDate) {
        toast({
            title: 'Error',
            text: 'Please fill required fields: Project, Category, Amount and Date',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
        return
      }
      const res = createResource({
        url:"frappe.client.insert",
        makeParams: () => ({
          doc:payload
        }),
        auto: true,
        onSuccess: (res) => {
          toast({
              title: 'Success',
              text: 'Expense claim created sucessfully',
              icon: 'check-circle',
              position: 'bottom-right',
              iconClasses: 'text-green-500',
            })
          expenseFormData.employee = ''
          expenseFormData.purpose = ''
          expenseFormData.project = null
          expenseFormData.note = ''
          expenseFormData.repayFromSalary = false
          expenseFormData.notify = []
          expenseFormData.expenseDate = ''
          expenseFormData.expenseCategory = null
          expenseFormData.amount = ''
        },
        onError: (error) => {
          console.error('Failed to load users:', error)
          toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed create expense',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
        }
      })
      
      // frappe.msgprint(`Expense Claim ${res.name} saved successfully`)
    } catch (err) {
      console.error('Error saving expense:', err)
    }
  }
  if(expenseFormData.category == "Advances Request"){
    try {
      const payload = {
        doctype: 'Employee Advance',
        employee: expenseFormData.employee,
        purpose: expenseFormData.purpose || 'Employee Advance',
        idp_project: expenseFormData.project?.value || null,
        repay_from_salary: expenseFormData.repayFromSalary,
        // List of approvers can also be set in Notify
        notify: expenseFormData.notify.map(n => ({
          user: n.value  // Required child table field
        })), // Just email/user IDs
        advance_amount:parseFloat(expenseFormData.amount),
        posting_date:expenseFormData.expenseDate,
        currency:"INR",
        exchange_rate:1
      }
      const res = createResource({
        url:"frappe.client.insert",
        makeParams: () => ({
          doc:payload
        }),
        auto: true,
        onSuccess: (res) => {
          toast({
              title: 'Success',
              text: 'Employee advance created sucessfully',
              icon: 'check-circle',
              position: 'bottom-right',
              iconClasses: 'text-green-500',
            })
          expenseFormData.employee = ''
          expenseFormData.purpose = ''
          expenseFormData.project = null
          expenseFormData.note = ''
          expenseFormData.repayFromSalary = false
          expenseFormData.notify = []
          expenseFormData.expenseDate = ''
          expenseFormData.expenseCategory = null
          expenseFormData.amount = ''
        },
        onError: (error) => {
          console.error('Failed to load users:', error)
          toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed create advance',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
        }
      })
    }

    catch (err) {
      console.error('Error saving expense:', err)
    }
  }
}


// const handleSubmit = () => {
//   console.log('Submit expense:', expenseFormData)
// }

// const handleClose = () => {
//   console.log('Close form')
// }
onMounted(()=>{
  employee_details()
  get_claim_details()
})
</script>