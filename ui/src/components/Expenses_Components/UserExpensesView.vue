<template>
  <div class="flex gap-4 p-2 bg-[#F8F7F9]">
    <!-- Main Content Area -->
    <div class="flex-1">
      <!-- Stats Cards -->
      <ExpenseStatsCards :stats="expenseStats" />

      <!-- History Sections -->
      <div class="grid grid-cols-2 gap-4 mt-4">
        <AdvancesHistory :advances="advancesData" />
        <ExpenseHistory :expenses="expensesData" />
      </div>

      <!-- Monthly Expense Chart -->
      <MonthlyExpenseChart :chartData="monthlyExpenseData" class="mt-4" />
    </div>

    <!-- Right Sidebar Form -->
    <div class="w-96">
      <ExpenseForm :formData="expenseFormData" @save="handleSave" @submit="handleSubmit" @close="handleClose" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import ExpenseStatsCards from './User_view/ExpenseStatsCards.vue'
import AdvancesHistory from './User_view/AdvancesHistory.vue'
import ExpenseHistory from './User_view/ExpenseHistory.vue'
import MonthlyExpenseChart from './User_view/MonthlyExpenseChart.vue'
import ExpenseForm from './User_view/ExpenseForm.vue'

// Expense Stats Data
const expenseStats = reactive({
  requestedAdvanceAmount: 0,
  issuedAdvanceAmount: 0,
  approvedExpenseAmount: 0,
  submissionDeadline: 'September 27th'
})

// Advances History Data
const advancesData = reactive({
  activeTab: 'Drafts',
  tabs: ['Drafts', 'Approved', 'Rejected'],
  items: [
    {
      id: 1,
      title: 'Jun 1st',
      description: 'Conveyance Expenses',
      duration: '1 day',
      icon: 'plane',
      iconColor: 'bg-yellow-100 text-yellow-600'
    },
    {
      id: 2,
      title: 'Jun 5th - 12th 2025',
      description: 'Client side meeting',
      duration: '4 days',
      icon: 'plane',
      iconColor: 'bg-purple-100 text-purple-600'
    },
    {
      id: 3,
      title: 'Jun 11th',
      description: 'Client Lunch',
      duration: '1 day',
      icon: 'utensils',
      iconColor: 'bg-purple-100 text-purple-600'
    }
  ]
})

// Expense History Data
const expensesData = reactive({
  activeTab: 'Drafts',
  tabs: ['Drafts', 'Approved', 'Rejected'],
  items: [
    {
      id: 1,
      title: 'Jun 1st',
      description: 'Conveyance Expenses',
      duration: '1 day',
      icon: 'plane',
      iconColor: 'bg-yellow-100 text-yellow-600'
    },
    {
      id: 2,
      title: 'Jun 4th - 8th 2025',
      description: 'Client side meeting',
      duration: '4 days',
      icon: 'plane',
      iconColor: 'bg-purple-100 text-purple-600'
    },
    {
      id: 3,
      title: 'Jun 11th',
      description: 'Client Lunch',
      duration: '1 day',
      icon: 'utensils',
      iconColor: 'bg-purple-100 text-purple-600'
    }
  ]
})

// Monthly Expense Chart Data
const monthlyExpenseData = reactive({
  series: [{
    name: 'Monthly Expenses',
    data: [1000, 800, 1200, 900, 1500, 1100, 1300, 1000, 1600, 1200, 1400, 1100, 1800, 1300, 1500, 1200, 1700, 1400, 1900, 1500, 1600, 1300, 1100, 1400, 1200, 1500, 1300, 1600, 1400, 2000]
  }],
  categories: Array.from({ length: 30 }, (_, i) => i + 1)
})

// Expense Form Data
const expenseFormData = reactive({
  category: 'Expenses Claim',
  purpose: 'Conveyance Expenses',
  project: '',
  amount: '2,000',
  expenseCategory: 'Travel',
  expenseDate: '24 February, 2024',
  advanceDate: '',
  repayFromSalary: false,
  note: 'Type Here',
  notify: '',
  receipt: null
})

// Event Handlers
const handleSave = () => {
  console.log('Save expense:', expenseFormData)
}

const handleSubmit = () => {
  console.log('Submit expense:', expenseFormData)
}

const handleClose = () => {
  console.log('Close form')
}
</script>