<template>
  <div class="flex gap-4 p-2 bg-[#F8F7F9]">

    <div class="flex-1 flex flex-col gap-4">

      <div>
        <ManagerStatsCards :stats="managerStats" />
      </div>

      <div>
        <ExpenseClaimsSection :claims="expenseClaimsData" @view-details="openDetailsModal" @approve="handleApprove"
          @reject="handleReject" />
      </div>

      <div>
        <AdvanceRequestsSection :requests="advanceRequestsData" @view-details="openDetailsModal"
          @approve="handleApprove" @reject="handleReject" />
      </div>

    </div>

    <div class="w-96">
      <TeamRecentExpenses :expenses="teamRecentExpenses" v-if="!showDetailsModal"  />
      <ExpenseDetailsModal v-if="showDetailsModal" :expense="selectedExpense" @approve="handleModalApprove"
        @reject="handleModalReject" @close="closeDetailsModal" />
    </div>


  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import ManagerStatsCards from './Team_view/ManagerStatsCards.vue'
import ExpenseClaimsSection from './Team_view/ExpenseClaimsSection.vue'
import AdvanceRequestsSection from './Team_view/AdvanceRequestsSection.vue'
import TeamRecentExpenses from './Team_view/TeamRecentExpenses.vue'
import ExpenseDetailsModal from './Team_view/ExpenseDetailsModal.vue'

// Manager Stats Data
const managerStats = reactive({
  issuedAdvanceAmount: 0,
  pendingExpenseAmount: 0,
  approvedExpenseAmount: 0,
  nextDeadline: {
    date: 'September 27th',
    period: 'Jan 4th - 8th 2025'
  }
})

// Expense Claims Data
const expenseClaimsData = reactive({
  items: [
    {
      id: 1,
      title: 'Client Visit',
      location: 'Manor House',
      amount: 2500,
      date: 'Jun 1st',
      icon: 'plane',
      iconColor: 'bg-yellow-100 text-yellow-600',
      status: null,
      category: 'Expenses Claim',
      purpose: 'Conveyance Expenses',
      project: 'Project',
      expenseCategory: 'Travel',
      expenseDate: '24 February, 2024',
      note: 'Client meeting expenses'
    },
    {
      id: 2,
      title: 'Client Visit',
      location: 'Converge',
      amount: 2500,
      date: 'Jun 1st',
      icon: 'plane',
      iconColor: 'bg-yellow-100 text-yellow-600',
      status: 'Requested',
      category: 'Expenses Claim',
      purpose: 'Conveyance Expenses',
      project: 'Project',
      expenseCategory: 'Travel',
      expenseDate: '24 February, 2024',
      note: 'Client meeting expenses'
    },
    {
      id: 3,
      title: 'Client Visit',
      location: 'Sky Loft',
      amount: 2500,
      date: 'Jun 1st',
      icon: 'utensils',
      iconColor: 'bg-purple-100 text-purple-600',
      status: 'Paid',
      category: 'Expenses Claim',
      purpose: 'Conveyance Expenses',
      project: 'Project',
      expenseCategory: 'Travel',
      expenseDate: '24 February, 2024',
      note: 'Client meeting expenses'
    }
  ]
})

// Advance Requests Data
const advanceRequestsData = reactive({
  items: [
    {
      id: 4,
      title: 'Client Visit',
      location: 'Manor House',
      amount: 2500,
      date: 'Jun 1st',
      icon: 'plane',
      iconColor: 'bg-yellow-100 text-yellow-600',
      status: null,
      category: 'Advance Request',
      purpose: 'Conveyance Expenses',
      project: 'Project',
      expenseCategory: 'Travel',
      expenseDate: '24 February, 2024',
      note: 'Advance for client meeting'
    },
    {
      id: 5,
      title: 'Client Visit',
      location: 'Converge',
      amount: 2500,
      date: 'Jun 1st',
      icon: 'plane',
      iconColor: 'bg-yellow-100 text-yellow-600',
      status: 'Requested',
      category: 'Advance Request',
      purpose: 'Conveyance Expenses',
      project: 'Project',
      expenseCategory: 'Travel',
      expenseDate: '24 February, 2024',
      note: 'Advance for client meeting'
    },
    {
      id: 6,
      title: 'Client Visit',
      location: 'Sky Loft',
      amount: 2500,
      date: 'Jun 1st',
      icon: 'utensils',
      iconColor: 'bg-purple-100 text-purple-600',
      status: 'Paid',
      category: 'Advance Request',
      purpose: 'Conveyance Expenses',
      project: 'Project',
      expenseCategory: 'Travel',
      expenseDate: '24 February, 2024',
      note: 'Advance for client meeting'
    }
  ]
})

// Team Recent Expenses Data
const teamRecentExpenses = reactive({
  members: [
    { id: 1, name: 'Amit Tandon', amount: 3280 },
    { id: 2, name: 'Akshay Rane', amount: 3280 },
    { id: 3, name: 'Richa Mahangbal', amount: 3280 },
    { id: 4, name: 'Radha Singhania', amount: 3280 }
  ]
})

// Modal State
const showDetailsModal = ref(false)
const selectedExpense = ref(null)

// Event Handlers
const openDetailsModal = (expense) => {
  selectedExpense.value = expense
  showDetailsModal.value = true
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedExpense.value = null
}

const handleApprove = (expenseId) => {
  console.log('Approve expense:', expenseId)
}

const handleReject = (expenseId) => {
  console.log('Reject expense:', expenseId)
}

const handleModalApprove = () => {
  console.log('Modal approve:', selectedExpense.value)
  closeDetailsModal()
}

const handleModalReject = () => {
  console.log('Modal reject:', selectedExpense.value)
  closeDetailsModal()
}
</script>