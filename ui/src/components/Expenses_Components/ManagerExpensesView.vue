<template>
  <div class="flex gap-4 p-2 bg-[#F8F7F9]">

    <div class="flex-1 flex flex-col gap-4">

      <div>
        <ManagerStatsCards :stats="managerStats" />
      </div>

      <div>
        <ExpenseClaimsSection :claims="expenseClaimsData" @view-details="openDetailsModal" @approve="handleApprove"
          @reject="handleReject" />
      </div>

      <div>
        <AdvanceRequestsSection :requests="advanceRequestsData" @view-details="openDetailsModal"
          @approve="handleApprove" @reject="handleReject" />
      </div>

    </div>

    <div class="w-96">
      <TeamRecentExpenses :expenses="teamRecentExpenses" v-if="!showDetailsModal"  />
      <ExpenseDetailsModal v-if="showDetailsModal" :expense="selectedExpense" @approve="handleModalApprove"
        @reject="handleModalReject" @close="closeDetailsModal" />
    </div>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import ManagerStatsCards from './Team_view/ManagerStatsCards.vue'
import ExpenseClaimsSection from './Team_view/ExpenseClaimsSection.vue'
import AdvanceRequestsSection from './Team_view/AdvanceRequestsSection.vue'
import TeamRecentExpenses from './Team_view/TeamRecentExpenses.vue'
import ExpenseDetailsModal from './Team_view/ExpenseDetailsModal.vue'
import { createResource ,toast} from 'frappe-ui'

// Manager Stats Data
const managerStats = reactive({
  issuedAdvanceAmount: 0,
  pendingExpenseAmount: 0,
  approvedExpenseAmount: 0,
  nextDeadline: {
    // date: 'September 27th',
    // period: 'Jan 4th - 8th 2025'
  }
})

// Expense Claims Data
const expenseClaimsData = reactive({
  items: [
    // {
    //   id: 1,
    //   title: 'Client Visit',
    //   location: 'Manor House',
    //   amount: 2500,
    //   date: 'Jun 1st',
    //   icon: 'plane',
    //   iconColor: 'bg-yellow-100 text-yellow-600',
    //   status: null,
    //   category: 'Expenses Claim',
    //   purpose: 'Conveyance Expenses',
    //   project: 'Project',
    //   expenseCategory: 'Travel',
    //   expenseDate: '24 February, 2024',
    //   note: 'Client meeting expenses'
    // },
    // {
    //   id: 2,
    //   title: 'Client Visit',
    //   location: 'Converge',
    //   amount: 2500,
    //   date: 'Jun 1st',
    //   icon: 'plane',
    //   iconColor: 'bg-yellow-100 text-yellow-600',
    //   status: 'Requested',
    //   category: 'Expenses Claim',
    //   purpose: 'Conveyance Expenses',
    //   project: 'Project',
    //   expenseCategory: 'Travel',
    //   expenseDate: '24 February, 2024',
    //   note: 'Client meeting expenses'
    // },
    // {
    //   id: 3,
    //   title: 'Client Visit',
    //   location: 'Sky Loft',
    //   amount: 2500,
    //   date: 'Jun 1st',
    //   icon: 'utensils',
    //   iconColor: 'bg-purple-100 text-purple-600',
    //   status: 'Paid',
    //   category: 'Expenses Claim',
    //   purpose: 'Conveyance Expenses',
    //   project: 'Project',
    //   expenseCategory: 'Travel',
    //   expenseDate: '24 February, 2024',
    //   note: 'Client meeting expenses'
    // }
  ]
})

// Advance Requests Data
const advanceRequestsData = reactive({
  items: [
    // {
    //   id: 4,
    //   title: 'Client Visit',
    //   location: 'Manor House',
    //   amount: 2500,
    //   date: 'Jun 1st',
    //   icon: 'plane',
    //   iconColor: 'bg-yellow-100 text-yellow-600',
    //   status: null,
    //   category: 'Advance Request',
    //   purpose: 'Conveyance Expenses',
    //   project: 'Project',
    //   expenseCategory: 'Travel',
    //   expenseDate: '24 February, 2024',
    //   note: 'Advance for client meeting'
    // },
    // {
    //   id: 5,
    //   title: 'Client Visit',
    //   location: 'Converge',
    //   amount: 2500,
    //   date: 'Jun 1st',
    //   icon: 'plane',
    //   iconColor: 'bg-yellow-100 text-yellow-600',
    //   status: 'Requested',
    //   category: 'Advance Request',
    //   purpose: 'Conveyance Expenses',
    //   project: 'Project',
    //   expenseCategory: 'Travel',
    //   expenseDate: '24 February, 2024',
    //   note: 'Advance for client meeting'
    // },
    // {
    //   id: 6,
    //   title: 'Client Visit',
    //   location: 'Sky Loft',
    //   amount: 2500,
    //   date: 'Jun 1st',
    //   icon: 'utensils',
    //   iconColor: 'bg-purple-100 text-purple-600',
    //   status: 'Paid',
    //   category: 'Advance Request',
    //   purpose: 'Conveyance Expenses',
    //   project: 'Project',
    //   expenseCategory: 'Travel',
    //   expenseDate: '24 February, 2024',
    //   note: 'Advance for client meeting'
    // }
  ]
})

// Team Recent Expenses Data
const teamRecentExpenses = reactive({
  members: [
    // { id: 1, name: 'Amit Tandon', amount: 3280 },
    // { id: 2, name: 'Akshay Rane', amount: 3280 },
    // { id: 3, name: 'Richa Mahangbal', amount: 3280 },
    // { id: 4, name: 'Radha Singhania', amount: 3280 }
  ]
})

// Modal State
const showDetailsModal = ref(false)
const selectedExpense = ref(null)

// Event Handlers
const openDetailsModal = (expense) => {
  selectedExpense.value = expense
  showDetailsModal.value = true
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedExpense.value = null
}

const handleApprove = (expenseId) => {
  console.log('Approve expense:', expenseId)
}

const handleReject = (expenseId) => {
  console.log('Reject expense:', expenseId)
}

const handleModalApprove = () => {
  console.log('Modal approve:', selectedExpense.value)
  const request = selectedExpense.value
  if (request.category == "Expense Claim"){
    const approve_requst = createResource({
      url: 'frappe.client.set_value',
      makeParams: () => ({
        doctype: request.category,
        name: request.id,  // assumes request has the document name
        fieldname: {
          docstatus: 1,
          approval_status : 'Approved'
        }
      }),
      auto: true,
      onSuccess: (res) => {
        // console.log("Employee Shifts", res)
      
        toast({
            title: 'Success',
            text: 'Request approved sucessfully',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
          closeDetailsModal()
      },
      onError: (error) => {
        // console.log(error)
        toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed to approve request',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
      },
    })
  }else{
    const approve_requst = createResource({
      url: 'frappe.client.set_value',
      makeParams: () => ({
        doctype: request.category,
        name: request.id,  // assumes request has the document name
        fieldname: {
          docstatus: 1,
          workflow_state : 'Approved'
        }
      }),
      auto: true,
      onSuccess: (res) => {
        // console.log("Employee Shifts", res)
      
        toast({
            title: 'Success',
            text: 'Request approved sucessfully',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
          closeDetailsModal()
      },
      onError: (error) => {
        // console.log(error)
        toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed to approve request',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
      },
    })
  }
  
  
}

const handleModalReject = () => {
  console.log('Modal reject:', selectedExpense.value)
  // closeDetailsModal()
  const request = selectedExpense.value
  if (request.category == "Expense Claim"){
    const approve_requst = createResource({
      url: 'frappe.client.set_value',
      makeParams: () => ({
        doctype: request.category,
        name: request.id,  // assumes request has the document name
        fieldname: {
          docstatus: 1,
          approval_status : 'Rejected'
        }
      }),
      auto: true,
      onSuccess: (res) => {
        // console.log("Employee Shifts", res)
      
        toast({
            title: 'Success',
            text: 'Request rejected sucessfully',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
          closeDetailsModal()
      },
      onError: (error) => {
        // console.log(error)
        toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed to reject request',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
      },
    })
  }else{
    const approve_requst = createResource({
      url: 'frappe.client.set_value',
      makeParams: () => ({
        doctype: request.category,
        name: request.id,  // assumes request has the document name
        fieldname: {
          // docstatus: 1,
          workflow_state : 'Rejected',
          status :'Cancelled',
          // docstatus : 2
        }
      }),
      auto: true,
      onSuccess: (res) => {
        // console.log("Employee Shifts", res)
      
        toast({
            title: 'Success',
            text: 'Request rejected sucessfully',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-green-500',
          })
          closeDetailsModal()
      },
      onError: (error) => {
        // console.log(error)
        toast({
            title: 'Error',
            text: error.messages?.[0] || 'Failed to reject request',
            icon: 'alert-circle',
            position: 'bottom-right',
            iconClasses: 'text-red-500',
          })
      },
    })
  }
}
function get_team_details(){
  createResource({
    url: 'inspira.inspira.api.expense_advance.expense_advance.get_team_details',
    makeParams: () => ({ emp: window.emp_id }),
    auto: true,
    onSuccess: (res) => {
      console.log("res",res)
      teamRecentExpenses.members = res.team_claims.claims
      managerStats.approvedExpenseAmount = res.team_claims.appproved_claim_amount
      managerStats.nextDeadline = res.nextDeadline
      expenseClaimsData.items = res.expenseClaimsData.items
      managerStats.pendingExpenseAmount= res.expenseClaimsData.pending_amount
      advanceRequestsData.items = res.advanceRequestsData.items
      // managerStats.issuedAdvanceAmount =res.advanceRequestsData.pending_amount

    },
    onError:(error)=>{
      console.log(error)
    }
  })
}
onMounted(()=>{
  get_team_details()
})
</script>