<template>
  <!-- Modal Overlay -->
  <div class="w-full flex items-center justify-end" @click="$emit('close')">
    <div class="bg-white rounded-sm shadow-md w-full h-screen overflow-y-auto" @click.stop>
      <!-- Header -->
      <div class="p-2 border-b bg-[#E6E0E9]">
        <h3 class="text-md text-gray-900">Expenses & Advances</h3>
      </div>

      <!-- Form Content -->
      <div class="p-4 space-y-4">
        <!-- Category -->
        <div>
          <label class="block text-sm text-gray-700 mb-2">Category</label>
          <select 
            :value="expense.category"
            disabled
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 appearance-none focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
          >
            <option>{{ expense.category }}</option>
          </select>
        </div>

        <!-- Purpose -->
        <div v-if="expense.category != 'Expense Claim'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Purpose</label>
          <textarea 
            :value="expense.purpose"
            disabled
            rows="3"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 resize-none focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
          ></textarea>
        </div>

        <!-- Project and Amount -->
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
            <input 
              :value="expense.project"
              disabled
              type="text"
              class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Amount</label>
            <input 
              :value="expense.amount.toLocaleString()"
              disabled
              type="text"
              class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Expense Category and Date -->
        <div class="grid grid-cols-2 gap-3" >
          <div v-if="expense.category == 'Expense Claim'">
            <label class="block text-sm font-medium text-gray-700 mb-2">Expense Category</label>
            <input 
              :value="expense.expenseCategory"
              disabled
              type="text"
              class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
            />
          </div>
          <div v-if="expense.category == 'Expense Claim'">
            <label class="block text-sm font-medium text-gray-700 mb-2">Expense Date</label>
            <input 
              :value="expense.expenseDate"
              disabled
              type="text"
              class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
            />
          </div>
          <div v-if="expense.category != 'Expense Claim'">
            <label class="block text-sm font-medium text-gray-700 mb-2">Posting Date</label>
            <input 
              :value="expense.expenseDate"
              disabled
              type="text"
              class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
            />
          </div>
        </div>

        <!-- Note -->
        <div v-if="expense.category == 'Expense Claim'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Note</label>
          <textarea 
            :value="expense.note"
            disabled
            rows="3"
            class="w-full text-sm px-3 py-2 border border-gray-300 rounded-sm bg-gray-50 text-gray-600 resize-none focus:outline-none focus:ring focus:ring-gray-200 focus:border-transparent"
          ></textarea>
        </div>

        <!-- Receipt -->
        <div v-if="expense.category == 'Expense Claim'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Receipt</label>
          <div class="border-2 border-dashed border-gray-300 rounded-sm p-4 text-center">
            <button class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-600 transition-colors">
              View File
            </button>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="p-4 border-t border-gray-200 flex gap-3 justify-end">
        <button 
          @click="$emit('approve')"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-600 transition-colors"
        >
          Approve
        </button>
        <button 
          @click="$emit('reject')"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-700 transition-colors"
        >
          Reject
        </button>
        <button 
          @click="$emit('close')"
          class="px-4 py-2 bg-[#625B71] text-white rounded-md text-sm hover:bg-gray-800 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>


<script setup>
import { ChevronDown, Check, X } from 'lucide-vue-next'

defineProps({
  expense: {
    type: Object,
    required: true
  }
})

defineEmits(['approve', 'reject', 'close'])
</script>