<template>
  <div class="w-full bg-white rounded-sm shadow-sm h-screen">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Team's recent expenses</h3>
    </div>
    
    <div class="p-4 space-y-4">
      <div 
        v-for="member in expenses.members" 
        :key="member.id"
        class="flex items-center gap-3 p-2 rounded-sm shadow-md"
      >
        <!-- Avatar -->
        <div class="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center">
          <User class="w-5 h-5 text-purple-600" />
        </div>
        
        <!-- Name and Amount -->
        <div class="flex-1 flex items-center justify-between">
          <div class="text-sm font-medium text-blue-600">{{ member.name }}</div>
          <div class="text-sm font-medium text-gray-900">₹ {{ member.amount.toLocaleString() }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { User } from 'lucide-vue-next'

defineProps({
  expenses: {
    type: Object,
    required: true
  }
})
</script>