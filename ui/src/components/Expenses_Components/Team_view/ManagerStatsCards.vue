<template>
  <div class="w-full flex flex-col gap-2">
    <div class="w-full flex justify-between gap-4">
      <!-- Issued Advance Amount -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600 mb-1">Issued Advance Amount</div>
        <div class="text-2xl font-medium text-gray-600">
          ₹ {{ stats.issuedAdvanceAmount }}
          <span class="text-sm font-normal text-gray-500">INR</span>
        </div>
      </div>

      <!-- Next Deadline -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div>
          <div class="text-sm text-gray-600 mb-1">Next Deadline for Payroll Submissions</div>
          <div class="text-sm text-gray-500">{{ stats.nextDeadline.period }}</div>
        </div>
        <div class="text-lg font-medium text-gray-600">{{ stats.nextDeadline.date }}</div>
      </div>
    </div>

    <div class="w-full flex justify-between gap-4">
      <!-- Pending Expense Amount -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600 mb-1">Pending Expense Amount</div>
        <div class="text-2xl font-medium text-gray-600">
          ₹ {{ stats.pendingExpenseAmount }}
          <span class="text-sm font-normal text-gray-500">INR</span>
        </div>
      </div>

      <!-- Approved Expense Amount -->
      <div class="w-full flex justify-between bg-white border border-gray-200 p-3">
        <div class="text-sm text-gray-600 mb-1">Approved Expense Amount</div>
        <div class="text-2xl font-medium text-gray-600">
          ₹ {{ stats.approvedExpenseAmount }}
          <span class="text-sm font-normal text-gray-500">INR</span>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
defineProps({
  stats: {
    type: Object,
    required: true
  }
})
</script>