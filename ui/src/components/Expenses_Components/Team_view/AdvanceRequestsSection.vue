<template>
  <div class="bg-white rounded-sm shadow-sm border-gray-200">
    <div class="p-4 border-b border-gray-200">
      <h3 class="text-lg font-medium text-gray-900">Advance Requests</h3>
    </div>

    <div class="p-4 space-y-3">
      <div 
        v-for="request in requests.items" 
        :key="request.id"
        class="flex items-center justify-between gap-4 p-2 rounded-sm shadow-md"
      >
        <div class="flex items-center gap-4">
          <!-- Icon -->
          <div class="flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center" :class="request.iconColor">
            <Plane v-if="request.icon === 'plane'" class="w-5 h-5" />
            <Utensils v-else-if="request.icon === 'utensils'" class="w-5 h-5" />
          </div>

          <!-- Content -->
          <div class="flex gap-8">
            <div class="text-sm font-medium text-blue-600 mb-1">{{ request.title }}</div>
            <div class="text-sm text-gray-600">{{ request.location }}</div>
          </div>

          <!-- Amount -->
          <div class="text-sm font-medium text-gray-900">₹ {{ request.amount.toLocaleString() }}</div>

          <!-- Date -->
          <div class="text-sm text-gray-500">{{ request.date }}</div>
        </div>

        <div class="flex items-center gap-4">
          <!-- Status Badge -->
          <div v-if="request.status" class="flex">
            <span 
              class="px-3 py-1 rounded-full text-xs font-medium"
              :class="getStatusClass(request.status)"
            >
              {{ request.status }}
            </span>
          </div>

          <!-- Action Buttons -->
          <div v-if="!request.status" class="flex items-center gap-2">
            <button 
              @click="$emit('approve', request.id)"
              class="w-6 h-6 rounded-full bg-green-100 text-green-600 hover:bg-green-200 flex items-center justify-center transition-colors"
            >
              <Check class="w-4 h-4" />
            </button>
            <button 
              @click="$emit('reject', request.id)"
              class="w-6 h-6 rounded-full bg-red-100 text-red-600 hover:bg-red-200 flex items-center justify-center transition-colors"
            >
              <X class="w-4 h-4" />
            </button>
          </div>

          <!-- Expand Button -->
          <button 
            @click="$emit('view-details', request)"
            class="w-6 h-6 rounded-full bg-gray-100 text-gray-600 hover:bg-gray-200 flex items-center justify-center transition-colors"
          >
            <Maximize2 class="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { Plane, Utensils, Check, X, Maximize2 } from 'lucide-vue-next'

defineProps({
  requests: {
    type: Object,
    required: true
  }
})

defineEmits(['view-details', 'approve', 'reject'])

const getStatusClass = (status) => {
  switch (status) {
    case 'Requested':
      return 'bg-purple-100 text-purple-700'
    case 'Paid':
      return 'bg-purple-100 text-purple-700'
    default:
      return 'bg-gray-100 text-gray-700'
  }
}
</script>