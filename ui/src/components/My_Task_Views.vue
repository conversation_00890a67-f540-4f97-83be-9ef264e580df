<template>
  <div class="w-full">
    <div class="flex gap-5 border-b border-gray-300 items-center">
      <div class="flex gap-2 items-center pb-1">
        <CalendarIcon />
        <h1 class="text-[#434343] text-[17px] font-normal font-roboto leading-none">Tasks</h1>
      </div>
      <button
        v-for="tab in mainTabs"
        :key="tab"
        @click="activeMainTab = tab"
        class="pb-1 px-1 text-[#79747E] text-[14px] font-[600] relative transition-all"
        :class="[
          activeMainTab === tab
            ? 'font-medium border-b border-gray-600'
            : 'hover:text-gray-900'
        ]"
      >
        {{ tab }}
      </button>
    </div>

    <!-- Tab Content for Task Management -->
    <div class="pt-2 w-full">
      <Transition name="slide" mode="out-in">
        <div v-if="activeMainTab === 'User'" key="user-task">
          <UserTaskView ref="userTaskRef" />
        </div>
        <div v-else-if="activeMainTab === 'Team'" key="team-task">
          <TeamTaskView ref="teamTaskRef" />
        </div>
      </Transition>
    </div>

    <!-- Add Task Button (Fixed at bottom) -->
    <div class="fixed bottom-2 left-48 z-50">
      <button
        @click="showTaskOverlay = true"
        class="flex items-center gap-2 px-4 py-2 bg-white rounded-full shadow-2xl border-2 border-gray-300 hover:bg-gray-100"
      >
        <PlusIcon class="h-5 w-5" />
        <span>Add Tasks</span>
      </button>
    </div>

    <!-- Task Overlay -->
    <TaskOverlay
      v-if="showTaskOverlay"
      :task="null"
      :projectId="currentProjectId"
      @close="handleTaskOverlayClose"
      @save="handleTaskSave"
      @insert="handleTaskSave"
    />
  </div>
</template>

<script setup>
import { ref, watch, computed, nextTick } from 'vue';
import { toast } from 'frappe-ui';
import CalendarIcon from './icons/CalendarIcon.vue';
import PlusIcon from './icons/PlusButtonIcon.vue';
import UserTaskView from './My_Task_Components/UserTaskView.vue';
import TeamTaskView from './My_Task_Components/TeamTaskView.vue';
import TaskOverlay from './My_Task_Components/TaskOverlay.vue';
import { useRoute } from 'vue-router';
const is_manager = window.is_manager

const route = useRoute();
// const mainTabs = ['User', 'Team'];
const mainTabs = is_manager ? ['User', 'Team'] : ['User'];

const activeMainTab = ref(localStorage.getItem('activeTaskMainTab') || 'User');
const showTaskOverlay = ref(false);
const currentTask = ref(null);

const userTaskRef = ref(null);
const teamTaskRef = ref(null);

// Get current project ID from route params or query
const currentProjectId = computed(() => {
  return route.params.id || route.query.id || '';
});

watch(activeMainTab, (newVal) => {
  localStorage.setItem('activeTaskMainTab', newVal);
});

// Handle overlay close
const handleTaskOverlayClose =  () => {
  console.log('activeMainTab ', activeMainTab.value)
  showTaskOverlay.value = false;
};

const handleTaskSave = async (task) => {
  console.log('Task updated:', task);
  showTaskOverlay.value = false;
  await nextTick();
  console.log('userTaskRef ', userTaskRef)
  if (activeMainTab.value === 'User' && userTaskRef.value) {
    userTaskRef.value.fetchTasks();
  } else if (activeMainTab.value === 'Team' && teamTaskRef.value) {
    teamTaskRef.value.fetchTasks();
  }
};
</script>

<style scoped>
 .slide-enter-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-leave-active {
  transition: all 0.15s cubic-bezier(0.4, 0, 1, 1);
}

.slide-enter-from {
  opacity: 0;
  transform: translateX(100px);
}

.slide-leave-to {
  opacity: 0;
  transform: translateX(-100px);
}
</style>
