app_name = "inspira"
app_title = "Inspira"
app_publisher = "Agkiya Labs"
app_description = "Product for IDP"
app_email = "<EMAIL>"
app_license = "mit"


doctype_js = {
    "Sales Invoice": "inspira/custom/client_scripts/sales_invoice.js",
    "Payment Entry": "inspira/custom/client_scripts/payment_entry.js",
}

doc_events = {
    "Sales Invoice": {
        "before_submit": "inspira.inspira.custom.server_scripts.sales_invoice.si_before_submit",
        "on_submit": "inspira.inspira.custom.server_scripts.sales_invoice.si_contract_milestone_update",
        "on_cancel": "inspira.inspira.custom.server_scripts.sales_invoice.si_contract_milestone_update",
	},
    "Purchase Invoice": {
        "on_cancel": "inspira.inspira.custom.server_scripts.purchase_invoice.pi_on_cancel",
	},
    "Payment Entry": {
        "on_submit": "inspira.inspira.custom.server_scripts.payment_entry.pe_contract_milestone_update",
        "on_cancel": "inspira.inspira.custom.server_scripts.payment_entry.pe_contract_milestone_update_on_cancel",
	},
    "Attendance" :{
        "on_update_after_submit":"inspira.inspira.custom.server_scripts.attendance.attendance_before_submit",
        "on_submit":"inspira.inspira.custom.server_scripts.attendance.attendance_before_submit"
	},
    "Attendance Request" : {
        "before_submit":"inspira.inspira.custom.server_scripts.attendance.attendance_request_before_submit"
	}
}

scheduler_events = {
	"cron": {
		# 8 AM Daily
		"0 8 * * *": ["inspira.api.auto_email_site_report_pdf.send_email_8am_daily"],
		# 8 PM Daily
		"0 20 * * *": ["inspira.api.auto_email_site_report_pdf.send_email_8pm_daily"],
		# 8 AM Monday
		"0 8 * * 1": ["inspira.api.auto_email_site_report_pdf.send_email_weekly_mon_8am"],
		# 8 PM Saturday
		"0 20 * * 6": ["inspira.api.auto_email_site_report_pdf.send_email_weekly_sat_8pm"],
	}
}

has_permission = {"IDP Project": "inspira.inspira.custom.permissions.idp_project_has_permission"}

permission_query_conditions = {
	"IDP Project": "inspira.inspira.custom.permissions.idp_project_permission_query_conditions"
}

# Apps
# ------------------

# required_apps = []

# Each item in the list will be shown as an app in the apps page
# add_to_apps_screen = [
# 	{
# 		"name": "inspira",
# 		"logo": "/assets/inspira/logo.png",
# 		"title": "Inspira",
# 		"route": "/inspira",
# 		"has_permission": "inspira.api.permission.has_app_permission"
# 	}
# ]

# Includes in <head>
# ------------------

# include js, css files in header of desk.html
# app_include_css = "/assets/inspira/css/inspira.css"
# app_include_js = "/assets/inspira/js/inspira.js"

# include js, css files in header of web template
# web_include_css = "/assets/inspira/css/inspira.css"
# web_include_js = "/assets/inspira/js/inspira.js"

# include custom scss in every website theme (without file extension ".scss")
# website_theme_scss = "inspira/public/scss/website"

# include js, css files in header of web form
# webform_include_js = {"doctype": "public/js/doctype.js"}
# webform_include_css = {"doctype": "public/css/doctype.css"}

# include js in page
# page_js = {"page" : "public/js/file.js"}

# include js in doctype views
# doctype_js = {"doctype" : "public/js/doctype.js"}
# doctype_list_js = {"doctype" : "public/js/doctype_list.js"}
# doctype_tree_js = {"doctype" : "public/js/doctype_tree.js"}
# doctype_calendar_js = {"doctype" : "public/js/doctype_calendar.js"}

# Svg Icons
# ------------------
# include app icons in desk
# app_include_icons = "inspira/public/icons.svg"

# Home Pages
# ----------

# application home page (will override Website Settings)
# home_page = "login"

# website user home page (by Role)
# role_home_page = {
# 	"Role": "home_page"
# }

# Generators
# ----------

# automatically create page for each record of this doctype
# website_generators = ["Web Page"]

# Jinja
# ----------

# add methods and filters to jinja environment
# jinja = {
# 	"methods": "inspira.utils.jinja_methods",
# 	"filters": "inspira.utils.jinja_filters"
# }

# Installation
# ------------

# before_install = "inspira.install.before_install"
# after_install = "inspira.install.after_install"

# Uninstallation
# ------------

# before_uninstall = "inspira.uninstall.before_uninstall"
# after_uninstall = "inspira.uninstall.after_uninstall"

# Integration Setup
# ------------------
# To set up dependencies/integrations with other apps
# Name of the app being installed is passed as an argument

# before_app_install = "inspira.utils.before_app_install"
# after_app_install = "inspira.utils.after_app_install"

# Integration Cleanup
# -------------------
# To clean up dependencies/integrations with other apps
# Name of the app being uninstalled is passed as an argument

# before_app_uninstall = "inspira.utils.before_app_uninstall"
# after_app_uninstall = "inspira.utils.after_app_uninstall"

# Desk Notifications
# ------------------
# See frappe.core.notifications.get_notification_config

# notification_config = "inspira.notifications.get_notification_config"

# Permissions
# -----------
# Permissions evaluated in scripted ways

# permission_query_conditions = {
# 	"Event": "frappe.desk.doctype.event.event.get_permission_query_conditions",
# }
#
# has_permission = {
# 	"Event": "frappe.desk.doctype.event.event.has_permission",
# }

# DocType Class
# ---------------
# Override standard doctype classes

# override_doctype_class = {
# 	"ToDo": "custom_app.overrides.CustomToDo"
# }

# Document Events
# ---------------
# Hook on document methods and events

# doc_events = {
# 	"*": {
# 		"on_update": "method",
# 		"on_cancel": "method",
# 		"on_trash": "method"
# 	}
# }

# Scheduled Tasks
# ---------------

# scheduler_events = {
# 	"all": [
# 		"inspira.tasks.all"
# 	],
# 	"daily": [
# 		"inspira.tasks.daily"
# 	],
# 	"hourly": [
# 		"inspira.tasks.hourly"
# 	],
# 	"weekly": [
# 		"inspira.tasks.weekly"
# 	],
# 	"monthly": [
# 		"inspira.tasks.monthly"
# 	],
# }

# Testing
# -------

# before_tests = "inspira.install.before_tests"

# Overriding Methods
# ------------------------------
#
# override_whitelisted_methods = {
# 	"frappe.desk.doctype.event.event.get_events": "inspira.event.get_events"
# }
#
# each overriding function accepts a `data` argument;
# generated from the base implementation of the doctype dashboard,
# along with any modifications made in other Frappe apps
# override_doctype_dashboards = {
# 	"Task": "inspira.task.get_dashboard_data"
# }

# exempt linked doctypes from being automatically cancelled
#
# auto_cancel_exempted_doctypes = ["Auto Repeat"]

# Ignore links to specified DocTypes when deleting documents
# -----------------------------------------------------------

# ignore_links_on_delete = ["Communication", "ToDo"]

# Request Events
# ----------------
# before_request = ["inspira.utils.before_request"]
# after_request = ["inspira.utils.after_request"]

# Job Events
# ----------
# before_job = ["inspira.utils.before_job"]
# after_job = ["inspira.utils.after_job"]

# User Data Protection
# --------------------

# user_data_fields = [
# 	{
# 		"doctype": "{doctype_1}",
# 		"filter_by": "{filter_by}",
# 		"redact_fields": ["{field_1}", "{field_2}"],
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_2}",
# 		"filter_by": "{filter_by}",
# 		"partial": 1,
# 	},
# 	{
# 		"doctype": "{doctype_3}",
# 		"strict": False,
# 	},
# 	{
# 		"doctype": "{doctype_4}"
# 	}
# ]

# Authentication and authorization
# --------------------------------

# auth_hooks = [
# 	"inspira.auth.validate"
# ]

# Automatically update python controller files with type annotations for this app.
# export_python_type_annotations = True

# default_log_clearing_doctypes = {
# 	"Logging DocType Name": 30  # days to retain logs
# }


website_route_rules = [
	{"from_route": "/ui/<path:app_path>", "to_route": "ui"},
]
