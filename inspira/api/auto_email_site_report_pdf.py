import json
from pypdf import PdfWriter

import frappe
from frappe.utils.print_format import _download_multi_pdf, read_multi_pdf
from frappe.utils.jinja import render_template
from frappe.utils.pdf import get_pdf

SITE_MGMT_DOCTYPE = "IDP Site Management"
SITE_MGMT_INDEX_PRINT_FORMAT = "IDP Site Management - Index Report"
SITE_MGMT_PRINT_FORMAT = "IDP Site Management - Project Report"
PROJECT_DOCTYPE = "IDP Project"
PROJECT_USER_DOCTYPE = "IDP Project User"
EMAIL_BODY = """<p>Dear Sir/Ma'am,</p>
<p>Please find attached the site progress update for your perusal.</p>
"""

# TODO: ON V2 of the App move this file to API folder under inspira/inspira/api/reports

def create_pdf(site_ids, index=None):
	try:
		output = PdfWriter()
		if index:
			template = frappe.get_doc("Print Format", SITE_MGMT_INDEX_PRINT_FORMAT).html
			get_pdf(render_template(template, index), output=output if output else None)
		for site_id in site_ids:
			frappe.get_print(
				SITE_MGMT_DOCTYPE,
				site_id,
				SITE_MGMT_PRINT_FORMAT,
				as_pdf=True,
				output=output,
			)
		return read_multi_pdf(output)
	except Exception:
		frappe.log_error("Error create_pdf", frappe.get_traceback())
		raise


def get_projects(daily: bool = False, weekly: bool = False, time_filter: list = None):
	if not daily and not weekly:
		frappe.throw("Please select either daily or weekly")
	frequency_filter = "send_daily" if daily else "send_weekly"
	filters = [[PROJECT_USER_DOCTYPE, frequency_filter, "=", 1]]
	if time_filter:
		filters.extend(time_filter)

	fields = ["parent AS project_id", "group_concat(user) AS user_emails"]
	return frappe.get_all(
		PROJECT_USER_DOCTYPE,
		fields=fields,
		filters=filters,
		group_by="parent",
	)


def get_site_mgmts_data(start_date: str, end_date: str, project_id: str, user_id: str | None = None):
	filters = [
		[SITE_MGMT_DOCTYPE, "date", "Between", [start_date, end_date]],
		[SITE_MGMT_DOCTYPE, "project_id", "=", project_id],
		[SITE_MGMT_DOCTYPE, "workflow_state", "=", "Saved"],
	]
	if user_id:
		filters.append([SITE_MGMT_DOCTYPE, "owner", "=", user_id])

	site_mgmts = frappe.get_all(
		SITE_MGMT_DOCTYPE,
		fields=[
			"report_generated_by",
			"group_concat(DISTINCT name) AS site_ids",
			"group_concat(DISTINCT select_area) AS select_area",
			"max(daily_number_series) AS daily_number_series",
			"max(weekly_number_series) AS weekly_number_series",
			"project_name",
			"date"
		],
		filters=filters,
		order_by="creation asc",
		group_by="report_generated_by",
	)
	for d in site_mgmts:
		d["site_ids"] = d["site_ids"].split(",")
		d["select_area"] = d["select_area"].split(",")

	return site_mgmts


def get_final_data(
	start_date: str, end_date: str, daily: bool = False, weekly: bool = False, time_filter: list = None
) -> dict[str, str]:
	"""
	Retrieves site management data grouped by project, including email recipients.

	Args:
		start_date (str): The start date for filtering site managements.
		end_date (str): The end date for filtering site managements.
		daily (bool, optional): If True, filter for users who receive daily reports. Defaults to False.
		weekly (bool, optional): If True, filter for users who receive weekly reports. Defaults to False.

	Returns:
		dict: A dictionary where keys are project IDs, and values are dictionaries
			  containing "site_mgmts" (list of site names), "email_to" (list of user emails),
			  and "project_name". Returns an empty dictionary if no data is found.

	Raises:
		frappe.ValidationError: If neither daily nor weekly is selected.
	"""
	try:
		if not daily and not weekly:
			frappe.throw("Please select either daily or weekly")

		if not start_date:
			frappe.throw("Start Date is required!")

		if not end_date:
			frappe.throw("End Date is required!")

		project_users = get_projects(daily=daily, weekly=weekly, time_filter=time_filter)
		projects_grouped = {}

		# Early return if no data
		if not project_users:
			return {}

		for project_user in project_users:
			project_name = frappe.db.get_value(PROJECT_DOCTYPE, project_user["project_id"], "project_name")
			site_mgmts = get_site_mgmts_data(start_date, end_date, project_user["project_id"])

			if site_mgmts:
				projects_grouped[project_user["project_id"]] = {
					"site_mgmts": site_mgmts,
					"email_to": project_user["user_emails"].split(","),
					"project_name": project_name,
				}
		return projects_grouped
	except Exception:
		frappe.log_error("Error get_final_data", frappe.get_traceback())
		raise

def create_index(project_id, site_mgmt, daily, weekly, team=False):
	index_data = []
	project_doc = frappe.get_doc(PROJECT_DOCTYPE, project_id)
	for area in project_doc.project_area:
		index_data.append({
			"area": area.area,
			"matched": area.area in site_mgmt["select_area"]
		})

	report_id = ""
	if daily:
		number_series = site_mgmt["daily_number_series"]
		number_series = f"{number_series:05d}"
		report_id = f"SM_{site_mgmt['project_name']}_Daily_{number_series}"
	elif weekly:
		number_series = site_mgmt["weekly_number_series"]
		number_series = f"{number_series:05d}"
		report_id = f"SM_{site_mgmt['project_name']}_Weekly_{number_series}"

	final_data = frappe._dict({
		"doc": frappe._dict({
			"project_name": site_mgmt['project_name'],
			"project_area": index_data,
			"report_id": report_id if not team else "",
			"report_generated_by": site_mgmt["report_generated_by"] if not team else "",
			"date": site_mgmt["date"],
			"expected_start_date": project_doc.expected_start_date,
			"expected_end_date": project_doc.expected_end_date,
		})
	})
	
	return final_data


def send_email(projects_grouped, daily=False, weekly=False):
	try:
		for _project, data in projects_grouped.items():
			update_series(data["site_mgmts"], daily, weekly)
			for site_mgmt in data["site_mgmts"]:
				try:
					index = create_index(_project, site_mgmt, daily, weekly)
					pdf = create_pdf(site_mgmt["site_ids"], index)
					subject = f"{data['project_name']} - Site Progress Report by {site_mgmt['report_generated_by']}"
					frappe.sendmail(
						recipients=data["email_to"],
						subject=subject,
						message=EMAIL_BODY,
						attachments=[
							{
								"fname": f"Site Management Report {data['project_name']} by {site_mgmt['report_generated_by']}.pdf",
								"fcontent": pdf,
							}
						],
						now=True,
					)

					for site_id in site_mgmt["site_ids"]:
						if daily:
							frappe.db.set_value(
								SITE_MGMT_DOCTYPE,
								site_id,
								"daily_number_series",
								site_mgmt["daily_number_series"],
								update_modified=False,
							)
						elif weekly:
							frappe.db.set_value(
								SITE_MGMT_DOCTYPE,
								site_id,
								"weekly_number_series",
								site_mgmt["weekly_number_series"],
								update_modified=False,
							)
				except Exception:
					err_json = {
						"tb": frappe.get_traceback(),
						"site_mgmt": site_mgmt,
						"daily": daily,
						"weekly": weekly,
					}
					frappe.log_error("Error send_email", str(err_json))
	except Exception:
		frappe.log_error("Error send_email", frappe.get_traceback())


def update_series(site_mgmts, daily=False, weekly=False):
	max_daily = 0
	max_weekly = 0
	for site_mgmt in site_mgmts:
		if site_mgmt["daily_number_series"] > max_daily:
			max_daily = site_mgmt["daily_number_series"]
	
		if site_mgmt["weekly_number_series"] > max_weekly:
			max_weekly = site_mgmt["weekly_number_series"]

	for site_mgmt in site_mgmts:
		if daily:
			site_mgmt['daily_number_series'] = max_daily + 1
			max_daily += 1
		if weekly:
			site_mgmt['weekly_number_series'] = max_weekly + 1
			max_weekly += 1


def get_date_range(days_ago_start, days_ago_end):
	start_date = frappe.utils.add_days(frappe.utils.today(), days_ago_start)
	end_date = frappe.utils.add_days(frappe.utils.today(), days_ago_end)
	return start_date, end_date


def send_email_for_period(days_ago_start: int, days_ago_end: int, daily: bool = False, weekly: bool = False, time_filter: list = None):
	try:
		start_date, end_date = get_date_range(days_ago_start, days_ago_end)
		projects_grouped = get_final_data(start_date, end_date, daily=daily, weekly=weekly, time_filter=time_filter)
		send_email(projects_grouped, daily=daily, weekly=weekly)
	except Exception:
		err = {
			"tb": frappe.get_traceback(),
			"start_date": start_date,
			"end_date": end_date,
			"daily": daily,
			"weekly": weekly,
			"time_filter": time_filter
		}
		frappe.log_error("Error in send_email_for_period", str(err))


def send_email_8am_daily():
	time_filter = [[PROJECT_USER_DOCTYPE, "daily_time", "=", '8 AM']]
	send_email_for_period(-1, -1, daily=True, time_filter=time_filter)


def send_email_8pm_daily():
	time_filter = [[PROJECT_USER_DOCTYPE, "daily_time", "=", '8 PM']]
	send_email_for_period(0, 0, daily=True, time_filter=time_filter)


def send_email_weekly_mon_8am():
	time_filter = [[PROJECT_USER_DOCTYPE, "weekly_time", "=", 'Monday - 8 AM']]
	send_email_for_period(-7, -2, weekly=True, time_filter=time_filter)  # -2 For getting Sat


def send_email_weekly_sat_8pm():
	time_filter = [[PROJECT_USER_DOCTYPE, "weekly_time", "=", 'Saturday - 8 PM']]
	send_email_for_period(-7, 0, weekly=True, time_filter=time_filter)


@frappe.whitelist()
def get_project_pdf(project_json):
	"""
	project_id: mandatory
	date: mandatory
	user_id: optional
	"""
	site_mgmts = get_site_mgmts_data(
		project_json["date"],
		project_json["date"],
		project_json["project_id"],
		project_json.get("user_id", None),
	)

	final_data = {}
	if site_mgmts:
		final_data = site_mgmts[0]
		for site in site_mgmts[1:]:
			final_data['site_ids'].extend(site['site_ids'])
			final_data['select_area'].extend(site['select_area'])

		team = False
		if not project_json.get("user_id", None):
			team = True
		index = create_index(project_json["project_id"], final_data, 1, 0, team=team)
		pdf = create_pdf(final_data["site_ids"], index)
		frappe.local.response.filecontent = pdf
		frappe.local.response.type = "pdf"
		frappe.local.response.filename = f"Site Management Report.pdf"
