import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields
from frappe.desk.page.setup_wizard.setup_wizard import make_records

def execute():
    custom_fields={
        "Customer":[
            {
                "fieldname": "deal",
                "fieldtype": "Data",
                "label": "Deal",
                "insert_after":"customer_group",
                "options": "",
                "read_only":1
            },
        ],
    }
    create_custom_fields(custom_fields, update=1)