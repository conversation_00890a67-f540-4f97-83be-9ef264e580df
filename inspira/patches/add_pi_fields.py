import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields
from frappe.desk.page.setup_wizard.setup_wizard import make_records

def execute():
    custom_fields={
        "Purchase Invoice":[
            {
                "fieldname": "contract",
                "fieldtype": "Link",
                "label": "Contract",
                "insert_after":"supplier",
                "options": "IDP Contract",
                "reqd": 0,
                "read_only": 1
            },
            {
                "fieldname": "variable_for",
                "fieldtype": "Link",
                "label": "Variable For",
                "insert_after":"contract",
                "options": "User",
                "reqd": 0,
                "read_only": 1
            },
            {
                "fieldname": "project_user_name",
                "fieldtype": "Data",
                "label": "Project User Name",
                "insert_after":"variable_for",
                "reqd": 0,
                "read_only": 1
            }
        ]
    }
    create_custom_fields(custom_fields, update=1)