from frappe.desk.page.setup_wizard.setup_wizard import make_records


def execute():
	records = [
		{
			"name": "IDP Site Management - Project Report",
			"owner": "Administrator",
			"creation": "2025-02-20 17:06:40.997199",
			"modified": "2025-05-15 14:06:02.547328",
			"modified_by": "<EMAIL>",
			"docstatus": 0,
			"idx": 0,
			"doc_type": "IDP Site Management",
			"module": "Inspira",
			"default_print_language": "en",
			"standard": "No",
			"custom_format": 1,
			"disabled": 0,
			"print_format_type": "Jinja",
			"raw_printing": 0,
			"html": "<body>\n    <div id=\"header-html\" style=\"height: 1px; display: block; width: 100%;\">&nbsp;</div>\n    <div class=\"main-container\">\n        <div class=\"main\">\n            <div class=\"header\"\n                style=\"display: flex; justify-content: space-between; align-items: center;border-top:none;border-left:none;border-right:none;padding-bottom:5px;\">\n                <div class=\"address\" style=\"flex: 1; text-align: left;color:#918080;\">\n                    <p style=\"font-size:22px;color:#5C5C5C;\">SITE PROGRESS REPORT</p>\n                    <p style=\"color:#5C5C5C;\">Project Name: {{doc.project_name}}</p>\n                    <p style=\"background-color: yellow; color: black;\">\n                        Area Name: {{ doc.select_area }}\n                    </p>\n                </div>\n\n                <div style=\"text-align: right;margin-top:0px;\">\n                    <img src=\"/files/tpa_text_logo.png\" alt=\"Company Logo\" style=\"width: 260px; height: auto;\">\n                </div>\n            </div>\n\n            <div class=\"image-grid\" style=\"margin: 15px 0; width: 100%; text-align: left; padding: 0 0px;\">\n\n                <div class=\"image-container\" style=\"border: 1px solid #5C5C5C; \">\n                    <div class=\"image-title\"\n                        style=\"border: 1px solid #5C5C5C; width: 100%;border-top:none;border-left:none;border-right:none;\">\n                        Elevation A\n                    </div>\n                    {% if doc.image_1 %}\n                    <a href=\"{{ doc.image_1 }}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block;\">\n                        <img src=\"{{ doc.image_1 }}\" alt=\"Elevation A\"\n                            style=\"width: 98%;height:200px;object-fit: contain; margin: 2px;\">\n                    </a>\n                    {% else %}\n                    <p style=\"margin: 0; line-height: 200px; height: 200px; width: 100%;\">No Image Available</p>\n                    {% endif %}\n                </div>\n\n                <div class=\"image-container\" style=\"border:1px solid #5C5C5C; \">\n                    <div class=\"image-title\"\n                        style=\"border: 1px solid #5C5C5C; width: 100%;border-top:none;border-left:none;border-right:none;\">\n                        Elevation B</div>\n                    {% if doc.image_2 %}\n                    <a href=\"{{ doc.image_2 }}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block;\">\n                        <img src=\"{{ doc.image_2 }}\" alt=\"Elevation B\"\n                            style=\"width: 98%;height:200px;object-fit: contain; margin: 2px;\">\n                    </a>\n                    {% else %}\n                    <p style=\"margin: 0; line-height: 200px; height: 200px; width: 100%;\">No Image Available</p>\n                    {% endif %}\n                </div>\n\n                <div class=\"image-container\" style=\"border: 1px solid #5C5C5C;\">\n                    <div class=\"image-title\"\n                        style=\"border: 1px solid #5C5C5C;border-top:none;border-left:none;border-right:none;\">Elevation\n                        C\n                    </div>\n                    {% if doc.image_3 %}\n                    <a href=\"{{ doc.image_3 }}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block;\">\n                        <img src=\"{{ doc.image_3 }}\" alt=\"Elevation C\"\n                            style=\"width: 98%;height:200px; object-fit: contain; margin: 2px;\">\n                    </a>\n                    {% else %}\n                    <p style=\"margin: 0; line-height: 200px; height: 200px; width: 100%;\">No Image Available</p>\n                    {% endif %}\n                </div>\n\n                <div class=\"image-container\" style=\"border: 1px solid #5C5C5C; \">\n                    <div class=\"image-title\"\n                        style=\"border: 1px solid #5C5C5C; width: 100%;border-top:none;border-left:none;border-right:none;\">\n                        Elevation D</div>\n                    {% if doc.image_d %}\n                    <a href=\"{{ doc.image_d }}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: block;\">\n                        <img src=\"{{ doc.image_d }}\" alt=\"Elevation D\"\n                            style=\"width: 98%;height:200px; object-fit: contain; margin: 2px;\">\n                    </a>\n                    {% else %}\n                    <p style=\"margin: 0; line-height: 200px; height: 200px; width: 100%;\">No Image Available</p>\n                    {% endif %}\n                </div>\n                <div class=\"image-container\" style=\"border: 1px solid #5C5C5C; \">\n                    <div class=\"image-title\"\n                        style=\"border: 1px solid #5C5C5C; width: 100%;border-top:none;border-left:none;border-right:none;\">\n                        Elevation E</div>\n                    {% if doc.elevation_e %}\n                    <a href=\"{{ doc.elevation_e }}\" target=\"_blank\" rel=\"noopener noreferrer\"\n                        style=\"display: block;\">\n                        <img src=\"{{ doc.elevation_e }}\" alt=\"Elevation D\"\n                            style=\"width: 98%;height:200px;object-fit: contain; margin: 2px;\">\n                    </a>\n                    {% else %}\n                    <p style=\"margin: 0; line-height: 200px; height: 200px; width: 100%;\">No Image Available</p>\n                    {% endif %}\n                </div>\n                <div class=\"image-container\" style=\"border: 1px solid #5C5C5C; \">\n                    <div class=\"image-title\"\n                        style=\"border: 1px solid #5C5C5C; width: 100%;border-top:none;border-left:none;border-right:none;\">\n                        Elevation F</div>\n                    {% if doc.elevation_f %}\n                    <a href=\"{{ doc.elevation_f }}\" target=\"_blank\" rel=\"noopener noreferrer\"\n                        style=\"display: block;\">\n                        <img src=\"{{ doc.elevation_f }}\" alt=\"Elevation D\"\n                            style=\"width: 98%;height:200px; object-fit: contain; margin: 2px;\">\n                    </a>\n                    {% else %}\n                    <p style=\"margin: 0; line-height: 200px; height: 200px; width: 100%;\">No Image Available</p>\n                    {% endif %}\n                </div>\n            </div>\n        </div>\n        \n         <!--<div style=\"page-break-after: always;\"></div> -->\n        \n        <div class=\"report-section\" style=\"margin: 15px 0; width: 100.15%; text-align: left; padding-right: 0px;\">\n            <table\n                style=\"width: 100%; text-align: center; border-collapse: collapse; border-spacing: 0; margin-left: 0;\">\n                <thead>\n                    <tr>\n                        <td\n                            style=\"border: 1px solid #918080; background-color: #BDB7AB; color: #FFFFFFF; width: 20%; line-height: 1;text-align:center;\">\n                            Discipline</td>\n                        <td\n                            style=\"border: 1px solid #918080; background-color: #BDB7AB; color: #FFFFFFF; width: 40%; line-height: 1;text-align:center;\">\n                            Current Updates</td>\n                        <td\n                            style=\"border: 1px solid #918080; background-color:#BDB7AB; color: #FFFFFFF; width: 40%; line-height: 1;text-align:center;\">\n                            Future Plans</td>\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for i in doc.disciplian_updates %}\n                    <tr>\n                        <td\n                            style=\"border: 1px solid #5C5C5C; color: #5C5C5C; width: 20%;text-align:left;vertical-align: middle;\">\n                            {{ i.discipline or \"-\" }}</td>\n                        <td style=\"border: 1px solid #5C5C5C; color: #5C5C5C; width: 40%;text-align:left;\">{{\n                            i.current_updates or \"-\" }}</td>\n                        <td style=\"border: 1px solid #5C5C5C; color: #5C5C5C; width: 40%;text-align:left;\">{{\n                            i.future_plan\n                            or \"-\" }}</td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n    </div>\n    \n    \n    \n    <div id=\"footer-html\" style=\"width: 100%; position: relative; border-top: 1px solid #918080; padding-top: 5px;\">\n    <table style=\"width: 100%; color: #5C5C5C;border-top: 1px solid #918080;\">\n        <tr>\n            <td style=\"text-align: left; color: #5C5C5C; font-size: 12px;\">\n                Date of Report: {{ frappe.utils.formatdate(doc.date, \"dd-MMMM-yyyy\") if doc.date else '' }}\n            </td>\n            <td style=\"text-align: right; color: #5C5C5C; font-size: 12px;\">\n                Prepared By: {{doc.report_generated_by or \"\"}}\n            </td>\n        </tr>\n    </table>\n</div>\n    \n</body>",
			"margin_top": 15,
			"margin_bottom": 15,
			"margin_left": 15,
			"margin_right": 15,
			"align_labels_right": 0,
			"show_section_headings": 0,
			"line_breaks": 0,
			"absolute_value": 0,
			"font_size": 14,
			"page_number": "Hide",
			"css": ".main {\n    /*width: 100%;*/\n    font-family: Graphic Italic Medium;\n    \n}\n\n        .header {\n           \n            align-items: center;\n            /*background-color: rgb(145, 128, 128);*/\n            \n            /*padding: 8px;*/\n           \n            display:-webkit-box;\n           -webkit-box-pack:justify;\n           border:1px solid #5C5C5C;\n        }\n\n        .address {\n            max-width: 50%;\n        }\n\n        .address h4 {\n            margin: 0;\n            font-size: 15px;\n            font-weight: bold;\n        }\n\n        .address p {\n            /*margin: 5px 0;*/\n        }\n\n       \n\n       .image-grid {\n            display: flex;\n            flex-wrap: wrap; \n            justify-content: space-between; \n            gap:2px; \n            /*padding: 2px;*/\n            /*margin:15px;*/\n            \n        }\n\n        .image-container {\n            width: calc(50% - 1px);  \n           \n            \n            text-align: center;\n            background-color: #ffffff;\n        }\n\n        .image-title {\n            background-color: #BDB7AB;\n            color:#FFFFFFF; \n            /*padding: 5px 0;*/\n            font-size: 10px;\n            /*font-weight: bold;*/\n            border: 1px solid #5C5C5C;\n        }\n\n        .image-container img {\n            width: 100%;  \n            height: auto; \n           \n        }\n\n        \n        @media print {\n            .image-grid {\n                display: block; \n            }\n            .image-container {\n                display: inline-block;\n                width: 49%;\n                margin-bottom: 10px;\n                border: 1px solid #5C5C5C;\n                \n                text-align: center;\n                background-color: #ffffff;\n            }\n        }",
			"print_format_builder": 0,
			"print_format_builder_beta": 0,
			"doctype": "Print Format",
			"__onload": {
				"print_templates": []
			},
			"__last_sync_on": "2025-05-15T08:38:36.811Z"
		},
		{
			"name": "IDP Site Management - Index Report",
			"creation": "2025-05-14 13:11:12.718175",
			"modified": "2025-05-14 23:43:50.565638",
			"modified_by": "Administrator",
			"owner": "Administrator",
			"docstatus": 0,
			"idx": 0,
			"doc_type": "IDP Site Management",
			"module": "Inspira",
			"default_print_language": "en",
			"standard": "No",
			"custom_format": 1,
			"disabled": 0,
			"print_format_type": "Jinja",
			"raw_printing": 0,
			"html": "<div style=\"font-family: Graphic Italic Medium\">\n\t<div\n\t\tstyle=\"\n\t\t\tdisplay: -webkit-box;\n\t\t\t-webkit-box-pack: justify;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: center;\n\t\t\tborder-top: none !important;\n\t\t\tborder-left: none !important;\n\t\t\tborder-right: none !important;\n\t\t\tborder: 1px solid #5c5c5c;\n\t\t\tpadding-bottom: 5px;\n\t\t\"\n\t>\n\t\t<div\n\t\t\tstyle=\"/* flex: 1; */ text-align: left; color: #918080; max-width: 50%\"\n\t\t>\n\t\t\t<p style=\"font-size: 22px; color: #5c5c5c\">SITE PROGRESS REPORT</p>\n\t\t\t<p style=\"color: #5c5c5c\">Project Name: {{doc.project_name}}</p>\n\t\t\t<p style=\"color: #5c5c5c\">Report Id: {{ doc.report_id | default('') }}</p>\n\t\t\t<p style=\"color: #5c5c5c\">\n\t\t\t\tPrepared By: {{ doc.report_generated_by | default('') }}\n\t\t\t</p>\n\t\t\t<p style=\"color: #5c5c5c\">\n\t\t\t\tDate of Report: {{ frappe.utils.formatdate(doc.date, \"dd-MMMM-yyyy\") if doc.date else ''}}\n\t\t\t</p>\n\t\t\t<p style=\"color: #5c5c5c\">\n\t\t\t\tContract Start Date: {{ frappe.utils.formatdate(doc.expected_start_date, \"dd-MMMM-yyyy\") if doc.expected_start_date else ''}}\n\t\t\t</p>\n\t\t\t<p style=\"color: #5c5c5c\">\n\t\t\t\tContract End Date: {{ frappe.utils.formatdate(doc.expected_end_date, \"dd-MMMM-yyyy\") if doc.expected_end_date else ''}}\n\t\t\t</p>\n\t\t</div>\n\t\t<div style=\"text-align: right; margin-top: 0px\">\n\t\t\t<img\n\t\t\t\tsrc=\"/files/tpa_text_logo.png\"\n\t\t\t\talt=\"Company Logo\"\n\t\t\t\tstyle=\"width: 260px; height: auto\"\n\t\t\t/>\n\t\t</div>\n\t</div>\n\n\t<p style=\"font-size: 22px; color: #5c5c5c\">INDEX</p>\n\n\t<div style=\"margin: 15px 0; width: 100%; text-align: left\">\n\t\t<table\n\t\t\tstyle=\"\n\t\t\t\twidth: 100%;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder-collapse: collapse;\n\t\t\t\tborder-spacing: 0;\n\t\t\t\"\n\t\t>\n\t\t\t<thead>\n\t\t\t\t<tr>\n\t\t\t\t\t<td\n\t\t\t\t\t\tstyle=\"\n\t\t\t\t\t\t\tborder: 1px solid #918080;\n\t\t\t\t\t\t\tbackground-color: #bdb7ab;\n\t\t\t\t\t\t\tcolor: #000000;\n\t\t\t\t\t\t\twidth: 5%;\n\t\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\"\n\t\t\t\t\t>\n\t\t\t\t\t\t#\n\t\t\t\t\t</td>\n\t\t\t\t\t<td\n\t\t\t\t\t\tstyle=\"\n\t\t\t\t\t\t\tborder: 1px solid #918080;\n\t\t\t\t\t\t\tbackground-color: #bdb7ab;\n\t\t\t\t\t\t\tcolor: #000000;\n\t\t\t\t\t\t\twidth: 45%;\n\t\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\"\n\t\t\t\t\t>\n\t\t\t\t\t\tArea Name\n\t\t\t\t\t</td>\n\t\t\t\t\t<td\n\t\t\t\t\t\tstyle=\"\n\t\t\t\t\t\t\tborder: 1px solid #918080;\n\t\t\t\t\t\t\tbackground-color: #bdb7ab;\n\t\t\t\t\t\t\tcolor: #000000;\n\t\t\t\t\t\t\twidth: 30%;\n\t\t\t\t\t\t\tline-height: 1;\n\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\"\n\t\t\t\t\t>\n\t\t\t\t\t\tSubmission Status\n\t\t\t\t\t</td>\n\t\t\t\t</tr>\n\t\t\t</thead>\n\t\t\t<tbody>\n\t\t\t\t{% for area in doc.project_area %}\n\t\t\t\t<tr>\n\t\t\t\t\t<td style=\"border: 1px solid #5c5c5c; text-align: center\">\n\t\t\t\t\t\t{{ loop.index }}\n\t\t\t\t\t</td>\n\t\t\t\t\t<td style=\"border: 1px solid #5c5c5c\">{{ area.area }}</td>\n\t\t\t\t\t<td style=\"border: 1px solid #5c5c5c; text-align: center\">\n\t\t\t\t\t\t{% if area.matched %}\n\t\t\t\t\t\t<span style=\"color: green; font-size: 18px\">✓</span>\n\t\t\t\t\t\t{% else %}\n\t\t\t\t\t\t<span style=\"color: red; font-size: 18px\">✗</span>\n\t\t\t\t\t\t{% endif %}\n\t\t\t\t\t</td>\n\t\t\t\t</tr>\n\t\t\t\t{% endfor %}\n\t\t\t</tbody>\n\t\t</table>\n\t</div>\n</div>\n",
			"margin_top": 15,
			"margin_bottom": 15,
			"margin_left": 15,
			"margin_right": 15,
			"align_labels_right": 0,
			"show_section_headings": 0,
			"line_breaks": 0,
			"absolute_value": 0,
			"font_size": 14,
			"page_number": "Hide",
			"css": "",
			"print_format_builder": 0,
			"print_format_builder_beta": 0,
			"doctype": "Print Format",
			"__onload": {
				"print_templates": []
			}
		}
	]

	make_records(records)
