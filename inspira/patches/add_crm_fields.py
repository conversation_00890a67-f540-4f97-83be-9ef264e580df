import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields
from frappe.desk.page.setup_wizard.setup_wizard import make_records

def execute():
    custom_fields={
        "CRM Lead":[
            {
                "fieldname": "custom_fields",
                "fieldtype": "Tab Break",
                "label": "Custom Fields",
                "insert_after":"status_change_log",
                "options": ""
            },
            {
                "fieldname": "date",
                "fieldtype": "Date",
                "label": "Date",
                "insert_after":"custom_fields",
                "options": ""
            },
            {
                "fieldname": "category",
                "fieldtype": "Select",
                "label": "Category",
                "insert_after":"date",
                "options": "\nArchitecture\nInterior\nArchitecture & Interior"
            },
            {
                "fieldname": "classification",
                "fieldtype": "Select",
                "label": "Classification",
                "insert_after":"category",
                "options": "\nResidential\nCommercial"
            },
            {
                "fieldname": "sub_classification",
                "fieldtype": "Link",
                "label": "Sub Classification",
                "insert_after":"classification",
                "options": "IDP Sub Classification"
            },
            {
                "fieldname": "column_break_unstz",
                "fieldtype": "Column Break",
                "label": "",
                "insert_after":"lead_owner",
                "options": ""
            },
            {
                "fieldname": "land_area_sq_ft",
                "fieldtype": "Float",
                "label": "Land Area (Sq Ft)",
                "insert_after":"column_break_unstz",
                "options": ""
            },
            {
                "fieldname": "built_up_area_sq_ft",
                "fieldtype": "Float",
                "label": "Built Up Area (Sq Ft)",
                "insert_after":"land_area_sq_ft",
                "options": ""
            },
            {
                "fieldname": "carpet_area_sq_ft",
                "fieldtype": "Float",
                "label": "Carpet Area (Sq Ft)",
                "insert_after":"land_area_sq_ft",
                "options": ""
            },
            {
                "fieldname": "timeline_to_start",
                "fieldtype": "Select",
                "label": "Timeline to Start",
                "insert_after":"built_up_area_sq_ft",
                "options": "\nASAP\n1-3 months\n3-6 months\nJust Exploring"
            },
            {
                "fieldname": "estimated_budget_range",
                "fieldtype": "Int",
                "label": "Estimated Budget Range",
                "insert_after":"timeline_to_start",
                "options": ""
            },
            {
                "fieldname": "preferred_design_style",
                "fieldtype": "Select",
                "label": "Preferred Design Style",
                "insert_after":"estimated_budget_range",
                "options": "\nModern\nContemporary\nMinimalist\nScandinavian\nMid-Century Modern\nIndustrial\nFuturistic / Parametric\nBrutalist\nSustainable / Eco-Friendly / Green Design\nTraditional\nColonial\nNeo-Classical\nClassical (Greek/Roman)\nVictorian\nGothic Revival\nTudor\nGeorgian\nFarmhouse\nRustic / Country\nTropical / Resort Style\nVernacular (Local Traditional)\nArt Deco\nEclectic\nBohemian\nRetro / Vintage\nMediterranean / Spanish Revival\nMoroccan\nJapanese Minimalism / Zen\nCoastal / Beach House\nLuxury Modern\nLuxury Classical\nBoutique / High-End Custom\nUrban Loft / Industrial Chic"
            },
            # {
            #     "fieldname": "",
            #     "fieldtype": "",
            #     "label": "",
            #     "insert_after":"",
            #     "options": ""
            # }
        ],
        "CRM Deal":[
            {
                "fieldname": "custom_fields",
                "fieldtype": "Tab Break",
                "label": "Custom Fields",
                "insert_after":"status_change_log",
                "options": ""
            },
            {
                "fieldname": "date",
                "fieldtype": "Date",
                "label": "Date",
                "insert_after":"custom_fields",
                "options": ""
            },
            {
                "fieldname": "category",
                "fieldtype": "Select",
                "label": "Category",
                "insert_after":"date",
                "options": "\nArchitecture\nInterior\nArchitecture & Interior"
            },
            {
                "fieldname": "classification",
                "fieldtype": "Select",
                "label": "Classification",
                "insert_after":"category",
                "options": "\nResidential\nCommercial"
            },
            {
                "fieldname": "sub_classification",
                "fieldtype": "Link",
                "label": "Sub Classification",
                "insert_after":"classification",
                "options": "IDP Sub Classification"
            },
            {
                "fieldname": "column_break_unstz",
                "fieldtype": "Column Break",
                "label": "",
                "insert_after":"lead_owner",
                "options": ""
            },
            {
                "fieldname": "land_area_sq_ft",
                "fieldtype": "Float",
                "label": "Land Area (Sq Ft)",
                "insert_after":"column_break_unstz",
                "options": ""
            },
            {
                "fieldname": "built_up_area_sq_ft",
                "fieldtype": "Float",
                "label": "Built Up Area (Sq Ft)",
                "insert_after":"land_area_sq_ft",
                "options": ""
            },
            {
                "fieldname": "carpet_area_sq_ft",
                "fieldtype": "Float",
                "label": "Carpet Area (Sq Ft)",
                "insert_after":"land_area_sq_ft",
                "options": ""
            },
            {
                "fieldname": "timeline_to_start",
                "fieldtype": "Select",
                "label": "Timeline to Start",
                "insert_after":"built_up_area_sq_ft",
                "options": "\nASAP\n1-3 months\n3-6 months\nJust Exploring"
            },
            {
                "fieldname": "estimated_budget_range",
                "fieldtype": "Int",
                "label": "Estimated Budget Range",
                "insert_after":"timeline_to_start",
                "options": ""
            },
            {
                "fieldname": "preferred_design_style",
                "fieldtype": "Select",
                "label": "Preferred Design Style",
                "insert_after":"estimated_budget_range",
                "options": "\nModern\nContemporary\nMinimalist\nScandinavian\nMid-Century Modern\nIndustrial\nFuturistic / Parametric\nBrutalist\nSustainable / Eco-Friendly / Green Design\nTraditional\nColonial\nNeo-Classical\nClassical (Greek/Roman)\nVictorian\nGothic Revival\nTudor\nGeorgian\nFarmhouse\nRustic / Country\nTropical / Resort Style\nVernacular (Local Traditional)\nArt Deco\nEclectic\nBohemian\nRetro / Vintage\nMediterranean / Spanish Revival\nMoroccan\nJapanese Minimalism / Zen\nCoastal / Beach House\nLuxury Modern\nLuxury Classical\nBoutique / High-End Custom\nUrban Loft / Industrial Chic"
            },
            {
                "fieldname": "customer",
                "fieldtype": "Link",
                "label": "Customer",
                "insert_after":"preferred_design_style",
                "options": "Customer"
            }
        ]
    }
    create_custom_fields(custom_fields, update=1)

    records = [
        {
            "doctype": "Property Setter",
            "doctype_or_field": "DocField",
            "doc_type": "CRM Lead",
            "field_name": "territory",
            "property": "label",
            "property_type": "Data",
            "value": "Project Location",
        },
        {
            "doctype": "Property Setter",
            "doctype_or_field": "DocType",
            "doc_type": "CRM Lead",
            "property": "field_order",
            "property_type": "Data",
            "value": '["details", "organization", "website", "territory", "industry", "job_title", "source", "lead_owner", "custom_column_break_unstz", "custom_land_area_sq_ft", "custom_built_up_area_sq_ft", "custom_timeline_to_start", "custom_estimated_budget_range", "custom_preferred_design_style", "person_tab", "salutation", "first_name", "last_name", "email", "mobile_no", "organization_tab", "section_break_uixv", "naming_series", "lead_name", "middle_name", "gender", "phone", "column_break_dbsv", "status", "no_of_employees", "annual_revenue", "image", "converted", "sla_tab", "sla", "sla_creation", "column_break_ffnp", "sla_status", "communication_status", "response_details_section", "response_by", "column_break_pweh", "first_response_time", "first_responded_on", "log_tab", "status_change_log", "custom_custom_fields", "custom_date", "custom_category", "custom_classification", "custom_sub_classification"]',
        },
        {
            "doctype": "Property Setter",
            "doctype_or_field": "DocField",
            "doc_type": "CRM Deal",
            "field_name": "territory",
            "property": "label",
            "property_type": "Data",
            "value": "Project Location",
        },
    ]
    make_records(records)

    update_crm_field_layout()


def update_crm_field_layout():
    field_maps = [
        {
            "doctype": "CRM Fields Layout",
            "docname": "CRM Lead-Quick Entry",
            "fields": {
                "layout": '[{"name":"first_tab","sections":[{"name":"lead_section","columns":[{"name":"column_EO1H","fields":["status"]},{"name":"column_RWBe","fields":["lead_owner"]}]},{"label":"Lead Details","name":"section_NYxe","opened":true,"columns":[{"name":"column_cLyO","fields":["date","source","territory","preferred_design_style"]},{"label":"","name":"column_TWcx","fields":["timeline_to_start","land_area_sq_ft","built_up_area_sq_ft","estimated_budget_range"]},{"label":"","name":"column_RXpA","fields":["category","classification","sub_classification"]}],"editingLabel":false},{"name":"person_section","columns":[{"name":"column_5jrk","fields":["salutation","email"]},{"name":"column_5CPV","fields":["first_name","mobile_no"]},{"name":"column_gXOy","fields":["last_name","gender"]}],"editingLabel":false,"label":"Contact Details"},{"name":"organization_section","columns":[{"name":"column_GHfX","fields":["organization","website"]},{"name":"column_hXjS","fields":["industry","no_of_employees"]},{"name":"column_RDNA","fields":["annual_revenue"]}],"editingLabel":false,"label":"Organization Details"}]}]'
            }
        },
        {
            "doctype": "CRM Fields Layout",
            "docname": "CRM Deal-Quick Entry",
            "fields": {
                "layout": '[{"name":"first_tab","sections":[{"name":"deal_section","columns":[{"name":"column_mdps","fields":["status"]},{"name":"column_H40H","fields":["deal_owner"]}],"editingLabel":false,"label":""},{"name":"organization_section","hidden":true,"editable":false,"columns":[{"name":"column_GpMP","fields":["organization"]},{"label":"","name":"column_aSIx","fields":[]}],"editingLabel":false,"label":""},{"name":"contact_section","hidden":true,"editable":false,"columns":[{"name":"column_CeXr","fields":["contact"]},{"name":"column_yHbk","fields":[]}]},{"label":"Deal Details","name":"section_etup","opened":true,"columns":[{"name":"column_gxrT","fields":["date","source","territory","preferred_design_style"]},{"label":"","name":"column_maYA","fields":["timeline_to_start","land_area_sq_ft","built_up_area_sq_ft","estimated_budget_range"]},{"label":"","name":"column_AMVj","fields":["category","classification","sub_classification"]}],"editingLabel":false},{"name":"contact_details_section","editable":false,"columns":[{"name":"column_ZTWr","fields":["salutation","email"]},{"name":"column_tabr","fields":["first_name","mobile_no"]},{"name":"column_Qjdx","fields":["last_name","gender"]}],"editingLabel":false,"label":"Contact Details"},{"name":"organization_details_section","editable":false,"columns":[{"name":"column_S3tQ","fields":["organization_name","website"]},{"name":"column_KqV1","fields":["industry","no_of_employees"]},{"name":"column_1r67","fields":["annual_revenue"]}],"editingLabel":false,"label":"Organization Details"}]}]'
            }
        }
    ]

    for field_map in field_maps:
        doc = frappe.get_doc(field_map["doctype"], field_map["docname"])
        doc.update(field_map["fields"])
        doc.save()
