from frappe.desk.page.setup_wizard.setup_wizard import make_records


def execute():
    records = [
        # Workflow Action Master
        {
            "name": "Save",
            "owner": "Administrator",
            "creation": "2025-03-12 12:34:27.194312",
            "modified": "2025-03-12 12:34:27.194312",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 1,
            "workflow_action_name": "Save",
            "doctype": "Workflow Action Master",
        },
        {
            "name": "Edit",
            "owner": "Administrator",
            "creation": "2025-03-12 12:35:32.796526",
            "modified": "2025-03-12 12:35:32.796526",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 1,
            "workflow_action_name": "Edit",
            "doctype": "Workflow Action Master",
        },
        {
            "name": "Submit",
            "owner": "Administrator",
            "creation": "2025-05-17 17:34:23.126565",
            "modified": "2025-05-17 17:34:23.126565",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_action_name": "Submit",
            "doctype": "Workflow Action Master",
        },
        # Site Visit States
        {
            "name": "Draft",
            "owner": "Administrator",
            "creation": "2025-03-12 12:18:13.225649",
            "modified": "2025-03-12 13:09:31.271035",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 66,
            "workflow_state_name": "Draft",
            "icon": "",
            "style": "Warning",
            "doctype": "Workflow State",
        },
        {
            "name": "Saved",
            "owner": "Administrator",
            "creation": "2025-03-12 12:26:07.464131",
            "modified": "2025-03-12 13:09:47.442121",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 155,
            "workflow_state_name": "Saved",
            "icon": "",
            "style": "Success",
            "doctype": "Workflow State",
        },
        # Site Visit Workflow
        {
            "name": "Site Visit",
            "owner": "Administrator",
            "creation": "2025-03-12 12:35:49.922042",
            "modified": "2025-03-12 12:35:49.922042",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_name": "Site Visit",
            "document_type": "IDP Site Management",
            "is_active": 1,
            "override_status": 0,
            "send_email_alert": 0,
            "workflow_state_field": "workflow_state",
            "doctype": "Workflow",
            "states": [
                {
                    "name": "ft9vm7s9ql",
                    "owner": "Administrator",
                    "creation": "2025-03-12 12:35:49.922042",
                    "modified": "2025-03-12 12:35:49.922042",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "All",
                    "send_email": 1,
                    "parent": "Site Visit",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State"
                },
                {
                    "name": "43hgbckgc7",
                    "owner": "Administrator",
                    "creation": "2025-03-12 12:35:49.922042",
                    "modified": "2025-03-12 12:35:49.922042",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Saved",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "All",
                    "send_email": 1,
                    "parent": "Site Visit",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State"
                }
            ],
            "transitions": [
                {
                    "name": "369bmpkk98",
                    "owner": "Administrator",
                    "creation": "2025-03-12 12:35:49.922042",
                    "modified": "2025-03-12 12:35:49.922042",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "action": "Save",
                    "next_state": "Saved",
                    "allowed": "All",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Site Visit",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition"
                },
                {
                    "name": "ngl8omhilm",
                    "owner": "Administrator",
                    "creation": "2025-03-12 12:35:49.922042",
                    "modified": "2025-03-12 12:35:49.922042",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Saved",
                    "action": "Edit",
                    "next_state": "Draft",
                    "allowed": "All",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Site Visit",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition"
                }
            ],
            "parent": "IDP Site Management",
        },
        # Timesheet States
        {
            "creation": "2025-05-26 17:00:35.463502",
            "docstatus": 0,
            "doctype": "Workflow State",
            "icon": "",
            "idx": 0,
            "modified": "2025-05-26 17:00:35.463502",
            "modified_by": "Administrator",
            "name": "Not Submitted",
            "owner": "Administrator",
            "style": "",
            "workflow_state_name": "Not Submitted"
        },
        {
            "name": "Sent For Approval",
            "owner": "Administrator",
            "creation": "2025-05-26 17:00:27.547800",
            "modified": "2025-05-26 17:00:27.547800",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_state_name": "Sent For Approval",
            "icon": "",
            "style": "",
            "doctype": "Workflow State"
        },
        # Timesheet Workflow
        {
            "name": "IDP Timesheet",
            "owner": "Administrator",
            "creation": "2025-05-17 17:36:02.851529",
            "modified": "2025-05-26 17:05:12.478841",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_name": "IDP Timesheet",
            "document_type": "IDP Timesheet",
            "is_active": 1,
            "override_status": 0,
            "send_email_alert": 0,
            "workflow_state_field": "workflow_state",
            "doctype": "Workflow",
            "states": [
                {
                "name": "l5cip5rpsn",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 1,
                "state": "Not Submitted",
                "doc_status": "0",
                "is_optional_state": 0,
                "avoid_status_override": 0,
                "allow_edit": "All",
                "send_email": 1,
                "parent": "IDP Timesheet",
                "parentfield": "states",
                "parenttype": "Workflow",
                "doctype": "Workflow Document State"
                },
                {
                "name": "l5cdph5kq4",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 2,
                "state": "Sent For Approval",
                "doc_status": "0",
                "is_optional_state": 0,
                "avoid_status_override": 0,
                "allow_edit": "All",
                "send_email": 1,
                "parent": "IDP Timesheet",
                "parentfield": "states",
                "parenttype": "Workflow",
                "doctype": "Workflow Document State"
                },
                {
                "name": "l5ca04ug7e",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 3,
                "state": "Approved",
                "doc_status": "1",
                "is_optional_state": 0,
                "avoid_status_override": 0,
                "allow_edit": "HR Manager",
                "send_email": 1,
                "parent": "IDP Timesheet",
                "parentfield": "states",
                "parenttype": "Workflow",
                "doctype": "Workflow Document State"
                },
                {
                "name": "l5c4qj6nn1",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 4,
                "state": "Rejected",
                "doc_status": "0",
                "is_optional_state": 0,
                "avoid_status_override": 0,
                "allow_edit": "HR Manager",
                "send_email": 1,
                "parent": "IDP Timesheet",
                "parentfield": "states",
                "parenttype": "Workflow",
                "doctype": "Workflow Document State"
                }
            ],
            "transitions": [
                {
                "name": "l5cfnk9s4r",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 1,
                "state": "Not Submitted",
                "action": "Submit",
                "next_state": "Sent For Approval",
                "allowed": "All",
                "allow_self_approval": 1,
                "send_email_to_creator": 0,
                "parent": "IDP Timesheet",
                "parentfield": "transitions",
                "parenttype": "Workflow",
                "doctype": "Workflow Transition"
                },
                {
                "name": "l5cftu6pgl",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 2,
                "state": "Sent For Approval",
                "action": "Approve",
                "next_state": "Approved",
                "allowed": "HR Manager",
                "allow_self_approval": 1,
                "send_email_to_creator": 0,
                "parent": "IDP Timesheet",
                "parentfield": "transitions",
                "parenttype": "Workflow",
                "doctype": "Workflow Transition"
                },
                {
                "name": "l5cj10v6vq",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 3,
                "state": "Sent For Approval",
                "action": "Reject",
                "next_state": "Rejected",
                "allowed": "HR Manager",
                "allow_self_approval": 1,
                "send_email_to_creator": 0,
                "parent": "IDP Timesheet",
                "parentfield": "transitions",
                "parenttype": "Workflow",
                "doctype": "Workflow Transition"
                },
                {
                "name": "l5c7ta68ki",
                "owner": "Administrator",
                "creation": "2025-05-17 17:36:02.851529",
                "modified": "2025-05-26 17:05:12.478841",
                "modified_by": "Administrator",
                "docstatus": 0,
                "idx": 4,
                "state": "Rejected",
                "action": "Submit",
                "next_state": "Sent For Approval",
                "allowed": "All",
                "allow_self_approval": 1,
                "send_email_to_creator": 0,
                "parent": "IDP Timesheet",
                "parentfield": "transitions",
                "parenttype": "Workflow",
                "doctype": "Workflow Transition"
                }
            ]
        },
        # CRM Deal Custom Button on CRM portal
        # {
        #     "name": "CRM Deal - Custom Action Button",
        #     "owner": "Administrator",
        #     "creation": "2025-05-27 14:13:24.518106",
        #     "modified": "2025-05-29 15:13:04.275977",
        #     "modified_by": "Administrator",
        #     "docstatus": 0,
        #     "idx": 0,
        #     "dt": "CRM Deal",
        #     "view": "Form",
        #     "enabled": 1,
        #     "is_standard": 0,
        #     "script": "\nfunction setupForm({ doc }) {\n    // if(!doc.custom_customer){\n    return {\n        actions: [{\n            buttonLabel: \"Create\",\n            group: \"Add\",\n            hideLabel: true,\n            items: [\n                {\n                    label: \"Customer\",\n                    onClick: async () => {\n                        \n                        const params = new URLSearchParams({\n                                customer_name: doc.name || '',\n                                deal:doc.name || ''\n                            });\n                            if(!doc.customer){\n                                const new_customer_url = `${window.origin}/app/customer/new?${params.toString()}`;\n                                window.open(new_customer_url, \"_blank\");\n                            }else{\n                                const customer_url = `${window.origin}/app/customer/${doc.customer}`;\n                                window.open(customer_url, \"_blank\");\n                            }\n                            \n                    }\n                },\n                {\n                    label: \"Contract\",\n                    onClick: async () => {\n                        const params = new URLSearchParams({\n                                party_name: doc.name || '',\n                                project_value : 1,\n                                status : doc.status,\n                                square_feet : doc.land_area_sq_ft,\n                                contract_type : \"Delivery\",\n                                category : doc.category,\n                                classification : doc.classification,\n                                sub_classification : doc.sub_classification,\n                                deal :doc.name\n                            });\n                            const new_contract_url = `${window.origin}/app/idp-contract/new?${params.toString()}`;\n                            window.open(new_contract_url, \"_blank\");\n                    }\n                }\n            ]\n        }]\n    };\n    // } // Uncomment this if you want to use the condition\n}",
        #     "doctype": "CRM Form Script"
        # },
        {
            "name": "CRM Deal - Custom Action Button",
            "owner": "Administrator",
            "creation": "2025-05-27 14:13:24.518106",
            "modified": "2025-06-09 16:41:13.402406",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "dt": "CRM Deal",
            "view": "Form",
            "enabled": 1,
            "is_standard": 0,
            "script": "\nfunction setupForm({ doc,$dialog }) {\n    if(doc.status == \"Won\"){\n    return {\n        actions: [{\n            buttonLabel: \"Customer\",\n            group: \"Add\",\n            hideLabel: true,\n            items: [\n                {\n                    label: \"New Customer\",\n                    onClick: async () => {\n                        \n                        const params = new URLSearchParams({\n                                customer_name: doc.name || '',\n                                deal:doc.name || ''\n                            });\n                            if(!doc.customer){\n                                const new_customer_url = `${window.origin}/app/customer/new?${params.toString()}`;\n                                window.open(new_customer_url, \"_blank\");\n                            }\n                            \n                    }\n                },\n\n            {\n                label: \"Existing Customer\",\n                onClick: async () => {\n                    if (!doc.customer) {\n                        $dialog({\n                            title: \"Customer Not Selected\",\n                            message: \"Please select a customer before proceeding.\",\n                            actions: [\n                                {\n                                    label: \"OK\",\n                                    variant: \"solid\",\n                                    onClick(close) {\n                                        close();\n            \n                                        // Wait for dialog to close before focusing\n                                        setTimeout(() => {\n                                            // Target the button inside the customer field\n                                            const customerButton = document.querySelector('[data-name=\"customer\"] button');\n                                            if (customerButton) {\n                                                customerButton.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                                                customerButton.focus();\n                                                customerButton.click(); // Open the dropdown\n                                            }\n                                        }, 100);\n                                    }\n                                }\n                            ]\n                        });\n                        \n                    } else {\n                        const customer_url = `${window.origin}/app/customer/${doc.customer}`;\n                        window.open(customer_url, \"_blank\");\n                    }\n                }\n            }\n\n            ]\n        },]\n    };\n    } // Uncomment this if you want to use the condition\n    \n}",
            "doctype": "CRM Form Script"
        },
        {
            "name": "CRM Deal - Contract Button",
            "owner": "Administrator",
            "creation": "2025-06-09 12:19:04.097045",
            "modified": "2025-06-20 13:48:19.200703",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "dt": "CRM Deal",
            "view": "Form",
            "enabled": 1,
            "is_standard": 0,
            "script": "function setupForm({ doc }) {\n    if(doc.customer){\n    return {\n        actions: [{\n            buttonLabel: \"Contract\",\n            group: \"Add\",\n            hideLabel: true,\n            items: [\n                {\n                    \n                    label: \"Contract\",\n                    onClick: async () => {\n                        if(doc.customer){\n                            const params = new URLSearchParams({\n                                    party_name: doc.customer || '',\n                                    // project_value : 1,\n                                    status : doc.status,\n                                    square_feet : doc.carpet_area_sq_ft,\n                                    // contract_type : \"Delivery\",\n                                    category : doc.category,\n                                    classification : doc.classification,\n                                    sub_classification : doc.sub_classification,\n                                    deal :doc.name\n                                });\n                                const new_contract_url = `${window.origin}/app/idp-contract/new?${params.toString()}`;\n                                window.open(new_contract_url, \"_blank\");\n                        }else{\n                            $dialog({\n                            title: \"Customer Not Selected\",\n                            message: \"Please select a customer before proceeding.\",\n                            actions: [\n                                {\n                                    label: \"OK\",\n                                    variant: \"solid\",\n                                    onClick(close) {\n                                        close();\n            \n                                        // Wait for dialog to close before focusing\n                                        setTimeout(() => {\n                                            // Target the button inside the customer field\n                                            const customerButton = document.querySelector('[data-name=\"customer\"] button');\n                                            if (customerButton) {\n                                                customerButton.scrollIntoView({ behavior: 'smooth', block: 'center' });\n                                                customerButton.focus();\n                                                customerButton.click(); // Open the dropdown\n                                            }\n                                        }, 100);\n                                    }\n                                }\n                            ]\n                        });\n                        }\n                    }\n                \n                },\n                ]}\n            ],\n    }\n    }\n}",
            "doctype": "CRM Form Script"
        },
        {
            "name": "Customer After Insert",
            "owner": "Administrator",
            "creation": "2025-05-29 15:10:30.093065",
            "modified": "2025-05-29 15:59:20.550454",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "script_type": "DocType Event",
            "reference_doctype": "Customer",
            "event_frequency": "All",
            "doctype_event": "After Insert",
            "allow_guest": 0,
            "disabled": 0,
            "script": "if doc.deal:\n    deal = frappe.get_doc(\"CRM Deal\",doc.deal)\n    deal.customer = doc.name\n    deal.save()",
            "enable_rate_limit": 0,
            "rate_limit_count": 5,
            "rate_limit_seconds": 86400,
            "doctype": "Server Script"
        },
        # Attendance request workflow
        {
            "name": "Attendance Request",
            "owner": "Administrator",
            "creation": "2025-06-02 14:10:08.390895",
            "modified": "2025-06-02 14:10:08.390895",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_name": "Attendance Request",
            "document_type": "Attendance Request",
            "is_active": 1,
            "override_status": 0,
            "send_email_alert": 0,
            "workflow_state_field": "workflow_state",
            "doctype": "Workflow",
            "states": [
                {
                    "name": "ogk6c4u2c6",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:10:08.390895",
                    "modified": "2025-06-02 14:10:08.390895",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Attendance Request",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State"
                },
                {
                    "name": "ogk3el38si",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:10:08.390895",
                    "modified": "2025-06-02 14:10:08.390895",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Approved",
                    "doc_status": "1",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Attendance Request",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State"
                },
                {
                    "name": "ogkfqr3n98",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:10:08.390895",
                    "modified": "2025-06-02 14:10:08.390895",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 3,
                    "state": "Rejected",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Attendance Request",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State"
                }
            ],
            "transitions": [
                {
                    "name": "ogk4cmkv69",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:10:08.390895",
                    "modified": "2025-06-02 14:10:08.390895",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "action": "Approve",
                    "next_state": "Approved",
                    "allowed": "Employee",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Attendance Request",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition"
                },
                {
                    "name": "ogk7bcn1in",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:10:08.390895",
                    "modified": "2025-06-02 14:10:08.390895",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Draft",
                    "action": "Reject",
                    "next_state": "Rejected",
                    "allowed": "Employee",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Attendance Request",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition"
                }
            ],
            "__last_sync_on": "2025-06-02T08:50:32.802Z"
        },
        # IDP outdoor duty request workflow
        {
            "name": "Outdoor Duty Request",
            "owner": "Administrator",
            "creation": "2025-06-02 14:09:39.436613",
            "modified": "2025-06-02 14:09:43.019543",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_name": "Outdoor Duty Request",
            "document_type": "IDP Outdoor Duty Request",
            "is_active": 1,
            "override_status": 0,
            "send_email_alert": 0,
            "workflow_state_field": "workflow_state",
            "doctype": "Workflow",
            "states": [
                {
                    "name": "o7iltbnpp6",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:09:39.436613",
                    "modified": "2025-06-02 14:09:43.019543",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Outdoor Duty Request",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State",
                    "__unsaved": 1
                },
                {
                    "name": "o7irvuv7gr",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:09:39.436613",
                    "modified": "2025-06-02 14:09:43.019543",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Approved",
                    "doc_status": "1",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Outdoor Duty Request",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State",
                    "__unsaved": 1
                },
                {
                    "name": "o7issbebog",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:09:39.436613",
                    "modified": "2025-06-02 14:09:43.019543",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 3,
                    "state": "Rejected",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Outdoor Duty Request",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State",
                    "__unsaved": 1
                }
            ],
            "transitions": [
                {
                    "name": "o7ifdhnsi0",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:09:39.436613",
                    "modified": "2025-06-02 14:09:43.019543",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "action": "Approve",
                    "next_state": "Approved",
                    "allowed": "Employee",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Outdoor Duty Request",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition",
                    "__unsaved": 1
                },
                {
                    "name": "o7iejk4a40",
                    "owner": "Administrator",
                    "creation": "2025-06-02 14:09:39.436613",
                    "modified": "2025-06-02 14:09:43.019543",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Draft",
                    "action": "Reject",
                    "next_state": "Rejected",
                    "allowed": "Employee",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Outdoor Duty Request",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition",
                    "__unsaved": 1
                }
            ]
        },
        # Employee Advance Work Flow
        {
            "name": "Employee Advance",
            "owner": "Administrator",
            "creation": "2025-07-15 17:37:39.649387",
            "modified": "2025-07-15 17:37:45.610967",
            "modified_by": "Administrator",
            "docstatus": 0,
            "idx": 0,
            "workflow_name": "Employee Advance",
            "document_type": "Employee Advance",
            "is_active": 1,
            "override_status": 0,
            "send_email_alert": 0,
            "workflow_state_field": "workflow_state",
            "doctype": "Workflow",
            "states": [
                {
                    "name": "bbks3ha66l",
                    "owner": "Administrator",
                    "creation": "2025-07-15 17:37:39.649387",
                    "modified": "2025-07-15 17:37:45.610967",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Employee Advance",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State",
                    "__unsaved": 1
                },
                {
                    "name": "bbk8shcvro",
                    "owner": "Administrator",
                    "creation": "2025-07-15 17:37:39.649387",
                    "modified": "2025-07-15 17:37:45.610967",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Approved",
                    "doc_status": "1",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Employee Advance",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State",
                    "__unsaved": 1
                },
                {
                    "name": "bbke58etd4",
                    "owner": "Administrator",
                    "creation": "2025-07-15 17:37:39.649387",
                    "modified": "2025-07-15 17:37:45.610967",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 3,
                    "state": "Rejected",
                    "doc_status": "0",
                    "is_optional_state": 0,
                    "avoid_status_override": 0,
                    "allow_edit": "Employee",
                    "send_email": 1,
                    "parent": "Employee Advance",
                    "parentfield": "states",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Document State",
                    "__unsaved": 1
                }
            ],
            "transitions": [
                {
                    "name": "bbktb12c13",
                    "owner": "Administrator",
                    "creation": "2025-07-15 17:37:39.649387",
                    "modified": "2025-07-15 17:37:45.610967",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 1,
                    "state": "Draft",
                    "action": "Approve",
                    "next_state": "Approved",
                    "allowed": "Employee",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Employee Advance",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition",
                    "__unsaved": 1
                },
                {
                    "name": "bbkaf2jiqv",
                    "owner": "Administrator",
                    "creation": "2025-07-15 17:37:39.649387",
                    "modified": "2025-07-15 17:37:45.610967",
                    "modified_by": "Administrator",
                    "docstatus": 0,
                    "idx": 2,
                    "state": "Draft",
                    "action": "Reject",
                    "next_state": "Rejected",
                    "allowed": "Employee",
                    "allow_self_approval": 1,
                    "send_email_to_creator": 0,
                    "parent": "Employee Advance",
                    "parentfield": "transitions",
                    "parenttype": "Workflow",
                    "doctype": "Workflow Transition",
                    "__unsaved": 1
                }
            ]
        }
    ]
    make_records(records)