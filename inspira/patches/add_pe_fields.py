import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields
from frappe.desk.page.setup_wizard.setup_wizard import make_records


def execute():
    custom_fields = {
        "Payment Entry": [
            {
                "fieldname": "milestone_allocation",
                "fieldtype": "Table",
                "label": "Milestone Allocation",
                "insert_after": "references",
                "options": "IDP Sales Invoice Allocation",
                "read_only": 0,
            },
        ],
    }
    create_custom_fields(custom_fields, update=1)
