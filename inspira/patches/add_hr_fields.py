import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields
def execute():
    custom_fields={ 
        "Holiday":[
            {
                "fieldname": "holiday_type",
                "fieldtype": "Link",
                "label": "IDP Holiday Type",
                "insert_after":"description",
                "options": "IDP Holiday Type"
            },
        ],
        "Leave Application":[
            {
                "fieldname": "notify_users",
                "fieldtype": "Table MultiSelect",
                "label": "IDP Notify User",
                "insert_after":"description",
                "options": "IDP Notify User"
            }
        ],
        "Attendance Request":[
            {
                "fieldname": "notify_users",
                "fieldtype": "Table MultiSelect",
                "label": "IDP Notify User",
                "insert_after":"reason",
                "options": "IDP Notify User"
            },
            {
                "fieldname": "duty_type",
                "fieldtype": "Data",
                "label": "Duty Type",
                "insert_after":"notify_users",
                "options": ""
            },
            {
                "fieldname": "project",
                "fieldtype": "Link",
                "label": "Project",
                "insert_after":"duty_type",
                "options": "IDP Project"
            },
            {
                "fieldname": "approver",
                "fieldtype": "Link",
                "label": "Approver",
                "options": "User",
                "read_only": 1,
                "insert_after" :"amended_from"
            },
            {
                "fieldname": "approved_on",
                "fieldtype": "Date",
                "label": "Approved on",
                "read_only": 1,
                "insert_after" :"approver"
            },
            {
                "fetch_from": "approver.full_name",
                "fieldname": "approver_name",
                "fieldtype": "Data",
                "label": "Approver Name",
                "read_only": 1,
                "fetch_if_empty": 1,
                "insert_after" :"approved_on"
            }
            # {
            #     "fieldname": "attach",
            #     "fieldtype": "Attach",
            #     "label": "Attachment",
            #     "insert_after":"project",
            #     "options": ""
            # }
        ],
        "Employee": [
            {
                "fieldname": "hourly_seat_cost",
                "fieldtype": "Currency",
                "label": "Hourly Cost",
                "insert_after":"salary_mode",
                "options": ""
            }
        ]
    }
    create_custom_fields(custom_fields, update=1)