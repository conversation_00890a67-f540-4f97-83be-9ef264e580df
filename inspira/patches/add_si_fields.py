import frappe
from frappe import _
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields
from frappe.desk.page.setup_wizard.setup_wizard import make_records

def execute():
    custom_fields={
        "Sales Invoice":[
            {
                "fieldname": "contract",
                "fieldtype": "Link",
                "label": "Contract",
                "insert_after":"customer",
                "options": "IDP Contract",
                "reqd": 1
            },
        ],
        "Sales Invoice Item":[
            {
                "fieldname": "milestone",
                "fieldtype": "Link",
                "label": "Milestone",
                "insert_after":"item_code",
                "options": "IDP Milestones",
                "reqd": 1
            },
        ]
    }
    create_custom_fields(custom_fields, update=1)