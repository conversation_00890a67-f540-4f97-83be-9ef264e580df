from frappe.desk.page.setup_wizard.setup_wizard import make_records


def execute():
	records = [
		# IDP Task Type"
		{"doctype": "IDP Task Type", "name": "Milestone"},
		{"doctype": "IDP Task Type", "name": "Deliverables"},
		{"doctype": "IDP Task Type", "name": "Task"},
		{"doctype": "IDP Task Type", "name": "Sub Task"},
		# IDP Project Type
		{"doctype": "IDP Project Type", "project_type": "Other"},
		{"doctype": "IDP Project Type", "project_type": "External"},
		{"doctype": "IDP Project Type", "project_type": "Internal"},
		# IDP Discipline
		{"doctype": "IDP Discipline", "discipline_name": "Civil"},
		{"doctype": "IDP Discipline", "discipline_name": "Carpentry"},
		{"doctype": "IDP Discipline", "discipline_name": "Electrical"},
		{"doctype": "IDP Discipline", "discipline_name": "HVA<PERSON>"},
		{"doctype": "IDP Discipline", "discipline_name": "POP"},
		{"doctype": "IDP Discipline", "discipline_name": "FireFighting"},
		{"doctype": "IDP Discipline", "discipline_name": "Windows"},
		{"doctype": "IDP Discipline", "discipline_name": "Painting and Polish"},
		{"doctype": "IDP Discipline", "discipline_name": "Decor"},
		{"doctype": "IDP Discipline", "discipline_name": "Misc"},
		{"doctype": "IDP Discipline", "discipline_name": "Plumbing"},
		# IDP Status Master
		{"doctype":"IDP Status Master",'form': 'Contract', 'status': 'Active Confirmed', 'color': '#29CD42'},
		{"doctype":"IDP Status Master",'form': 'Contract', 'status': 'Active Not Confirmed', 'color': '#CB2929'},
		{"doctype":"IDP Status Master",'form': 'Contract', 'status': 'Completed', 'color': '#29CD42'},
		{"doctype":"IDP Status Master",'form': 'Contract', 'status': 'On Hold', 'color': '#ECAD4B'},
		{"doctype":"IDP Status Master",'form': 'MIlestone Payment', 'status': 'Due', 'color': None},
		{"doctype":"IDP Status Master",'form': 'MIlestone Payment', 'status': 'Not Due', 'color': None},
		{"doctype":"IDP Status Master",'form': 'MIlestone Payment', 'status': 'Partial Received', 'color': None},
		{"doctype":"IDP Status Master",'form': 'MIlestone Payment', 'status': 'Payment Pending', 'color': None},
		{"doctype":"IDP Status Master",'form': 'MIlestone Payment', 'status': 'Received', 'color': None},
		{"doctype":"IDP Status Master",'form': 'Task', 'status': 'Done', 'color': '#29CD42'},
		{"doctype":"IDP Status Master",'form': 'Task', 'status': 'In Progress', 'color': '#ECAD4B'},
		{"doctype":"IDP Status Master",'form': 'Task', 'status': 'On Hold', 'color': '#9c9c9c'},
		{"doctype":"IDP Status Master",'form': 'Task', 'status': 'To Do', 'color': '#CB2929'}
	]

	make_records(records)
