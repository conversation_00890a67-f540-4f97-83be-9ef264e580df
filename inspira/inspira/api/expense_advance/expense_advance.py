import frappe
from frappe.query_builder.functions import Sum, CustomFunction
from frappe.utils import getdate,now_datetime
from erpnext.accounts.utils import get_fiscal_year
from frappe import qb

@frappe.whitelist()
def get_claim_details(emp):
    return {
        "claims":get_claim_data(emp),
        "advances":get_advance_data(emp),
        "approved_total":get_approved_totals(emp),
        "monthlyExpenseData":get_monthly_expense_chart_data(emp)
    }

def get_claim_data(emp):
    expense = qb.DocType("Expense Claim")
    expense_detail = qb.DocType("Expense Claim Detail")
    fiscal_year = get_fiscal_year()
    year_start = frappe.utils.getdate(fiscal_year['year_start_date'])
    year_end = frappe.utils.getdate(fiscal_year['year_end_date'])
    query = (
        qb.from_(expense)
        .join(expense_detail).on(expense.name == expense_detail.parent)
        .select(
            (expense_detail.expense_date),
            expense_detail.expense_type,
            expense_detail.sanctioned_amount,
            expense_detail.description,
            expense_detail.idp_project,
            expense.approval_status
        )
        .where(
            (expense.employee == emp)&
            (expense_detail.expense_date.between(year_start, year_end))
        )
        
    )

    result = query.run(as_dict=True)
    return result

def get_advance_data(emp):
    advance = qb.DocType("Employee Advance")
    fiscal_year = get_fiscal_year()
    year_start = frappe.utils.getdate(fiscal_year['year_start_date'])
    year_end = frappe.utils.getdate(fiscal_year['year_end_date'])
    query = (
        qb.from_(advance)
        .select(
            advance.posting_date,
            advance.purpose,
            advance.advance_amount,
            advance.docstatus,
            advance.status
        )
        .where(
            (advance.employee == emp)&
            (advance.posting_date.between(year_start, year_end))
        )
        
    )

    result = query.run(as_dict=True)
    return result

from datetime import date, timedelta

def get_ordinal_suffix(day):
    if 11 <= day <= 13:
        return 'th'
    return {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
def get_approved_totals(emp):
    fiscal_year = get_fiscal_year()
    year_start = getdate(fiscal_year['year_start_date'])
    year_end = getdate(fiscal_year['year_end_date'])

    total_claim = get_total_approved_claim(emp, year_start, year_end)
    total_advance = get_total_approved_advance(emp, year_start, year_end)
    total_requested_advance = get_total_requested_advance(emp, year_start, year_end)
    today = date.today()
    # Get first day of next month, then subtract 1 day
    next_month = today.replace(day=28) + timedelta(days=4)
    last_day = next_month - timedelta(days=next_month.day)

    month_name = last_day.strftime("%B")  # Full month name, e.g., "September"
    day = last_day.day
    suffix = get_ordinal_suffix(day)
    
    settings = frappe.db.get_value("HR Settings", None, ["expense_submission_date"], as_dict=True)
    submissionDeadlineDate = getdate(settings.expense_submission_date) if settings and settings.expense_submission_date else None

    if submissionDeadlineDate:
        day = submissionDeadlineDate.day
        suffix = get_ordinal_suffix(day)
        month_name = submissionDeadlineDate.strftime("%B")
        submissionDeadline = f"{month_name} {day}{suffix}"
    else:
        submissionDeadline = None
    return {
        "total_approved_claim": total_claim or 0,
        "total_approved_advance": total_advance or 0,
        "total_requested_advance":total_requested_advance or 0,
        # "submissionDeadline":f"{month_name} {day}{suffix}"
        "submissionDeadline":submissionDeadline
    }

def get_total_approved_claim(emp, start_date, end_date):
    expense = qb.DocType("Expense Claim")
    detail = qb.DocType("Expense Claim Detail")

    query = (
        qb.from_(expense)
        .join(detail).on(detail.parent == expense.name)
        .where(
            (expense.employee == emp) &
            (detail.expense_date.between(start_date, end_date)) &
            (expense.approval_status == "Approved")
        )
        .select(Sum(detail.sanctioned_amount).as_("total"))
    )

    result = query.run(as_dict=True)
    return result[0]["total"] if result else 0

def get_total_approved_advance(emp, start_date, end_date):
    advance = qb.DocType("Employee Advance")

    query = (
        qb.from_(advance)
        .where(
            (advance.employee == emp) &
            (advance.posting_date.between(start_date, end_date)) &
            (advance.docstatus == 1)
        )
        .select(Sum(advance.advance_amount).as_("total"))
    )

    result = query.run(as_dict=True)
    return result[0]["total"] if result else 0

def get_total_requested_advance(emp, start_date, end_date):
    advance = qb.DocType("Employee Advance")

    query = (
        qb.from_(advance)
        .where(
            (advance.employee == emp) &
            (advance.posting_date.between(start_date, end_date)) &
            (advance.docstatus == 0)
        )
        .select(Sum(advance.advance_amount).as_("total"))
    )

    result = query.run(as_dict=True)
    return result[0]["total"] if result else 0

from datetime import datetime, timedelta
from frappe.query_builder import DocType
from collections import defaultdict

def get_monthly_expense_chart_data(emp):
    expense = DocType("Expense Claim")
    expense_detail = DocType("Expense Claim Detail")

    # Calculate the first and last day of the current month
    today = now_datetime()
    start_of_month = datetime(today.year, today.month, 1)
    if today.month == 12:
        end_of_month = datetime(today.year + 1, 1, 1) - timedelta(days=1)
    else:
        end_of_month = datetime(today.year, today.month + 1, 1) - timedelta(days=1)

    # Query with date BETWEEN range instead of MONTH()/YEAR()
    query = (
        frappe.qb.from_(expense)
        .join(expense_detail).on(expense.name == expense_detail.parent)
        .select(
            expense_detail.expense_date,
            expense_detail.sanctioned_amount
        )
        .where(
            (expense.employee == emp) &
            (expense_detail.expense_date.between(start_of_month, end_of_month))
        )
    )

    result = query.run(as_dict=True)

    # Aggregate totals per day
    daily_totals = defaultdict(float)
    for row in result:
        day = getdate(row['expense_date']).day
        daily_totals[day] += float(row['sanctioned_amount'])

    # Build final chart data (day 1 to 30/31)
    num_days = end_of_month.day
    data = [round(daily_totals.get(day, 0), 2) for day in range(1, num_days + 1)]
    categories = list(range(1, num_days + 1))

    return {
        "series": [{
            "name": "Monthly Expenses",
            "data": data
        }],
        "categories": categories
    }

# **************************************************** Team Details ************************
import frappe
from frappe.query_builder import DocType
from frappe.utils import flt

def get_team(emp):
    employee = DocType("Employee")
    
    # Query to get team members who report to the given employee
    # query = (
    #     frappe.qb.from_(employee)
    #     .select(employee.name)
    #     .where(employee.reports_to == emp)
    # )
    query = (
        frappe.qb.from_(employee)
        .select(employee.name)
        .where(employee.expense_approver == frappe.session.user)
    )
    
    result = query.run(as_dict=True)
    employee_list = [row['name'] for row in result]
    return employee_list

@frappe.whitelist()
def get_team_details(emp):
    team = get_team(emp)
    settings = frappe.db.get_value("HR Settings", None, ["expense_submission_date"], as_dict=True)
    submissionDeadlineDate = getdate(settings.expense_submission_date) if settings and settings.expense_submission_date else None

    if submissionDeadlineDate:
        day = submissionDeadlineDate.day
        suffix = get_ordinal_suffix(day)
        month_name = submissionDeadlineDate.strftime("%B")
        submissionDeadline = f"{month_name} {day}{suffix}"
    else:
        submissionDeadline = None
    fiscal_year = get_fiscal_year()
    year_start = frappe.utils.getdate(fiscal_year['year_start_date'])
    year_end = frappe.utils.getdate(fiscal_year['year_end_date'])
    def format_date(date_obj):
        day = date_obj.day
        suffix = get_ordinal_suffix(day)
        month_name = date_obj.strftime("%B")
        return f"{month_name} {day}{suffix}"

    formatted_start = format_date(year_start)
    formatted_end = format_date(year_end)
    return {
        "team": team,
        "team_claims": team_claims(team),  # Pass team as the correct parameter
        "nextDeadline":{
            "date":submissionDeadline,
            "period":f"{formatted_start} - {formatted_end}"
        },
        "expenseClaimsData":get_team_pending_claims(team),
        "advanceRequestsData":get_team_pending_advances(team)
    }

def team_claims(team):
    # Fetching expense claims for team members
    expense_claim = "Expense Claim"
    
    # Use get_all to fetch the sum of sanctioned_amount grouped by employee
    expense_data = frappe.get_all(
        expense_claim,
        fields=["employee", "sum(total_sanctioned_amount) as total_expenses"],
        filters={"employee": ["in", team],"docstatus":1,"approval_status":'Approved'},
        group_by="employee"
    )

    # Combine the team names with the expense data
    team_expenses = []
    total = 0
    for member in expense_data:
        amount = flt(member['total_expenses'])
        total += amount
        team_expenses.append({
            "id": member['employee'],
            "name": get_employee_name(member['employee']),  # Assuming this helper function exists to fetch names
            "amount": flt(member['total_expenses'])  # Ensuring the amount is float
        })
    
    return {
        "claims":team_expenses,
        "appproved_claim_amount":total
    }

def get_employee_name(employee_id):
    # Helper function to get employee's full name from employee ID
    employee = frappe.get_doc("Employee", employee_id)
    return employee.employee_name if employee else "Unknown"

from frappe.utils import getdate, formatdate, flt


def get_team_pending_claims(team):
    # Ensure team is a list
    if isinstance(team, str):
        import json
        team = json.loads(team)

    expense_data = frappe.get_all(
        "Expense Claim",
        fields=[
            "name",
            "employee",
            "total_claimed_amount",
            "total_sanctioned_amount",
            "idp_project",
            "posting_date",
            "note",
            "approval_status",
            # "purpose"
        ],
        filters={
            "employee": ["in", team],
            "docstatus": 0
        }
    )

    def get_icon_and_color(purpose):
        # You can map purposes to icons/colors here
        if purpose and 'meal' in purpose.lower():
            return 'utensils', 'bg-purple-100 text-purple-600'
        if purpose and 'travel' in purpose.lower():
            return 'plane', 'bg-yellow-100 text-yellow-600'
        return 'file', 'bg-gray-100 text-gray-600'

    team_expenses = []
    total_amount = 0
    for idx, claim in enumerate(expense_data, start=1):
        icon, iconColor = get_icon_and_color(claim.note)
        expense_row = frappe.get_all(
            "Expense Claim Detail",
            filters={"parent": claim.name},
            fields=["expense_type"],
            limit=1
        )
        expense_category = expense_row[0].expense_type if expense_row else "Other"
        amount = flt(claim.total_sanctioned_amount or claim.total_claimed_amount or 0)
        total_amount += amount
        team_expenses.append({
            "id": claim.name,
            "title": "Client Visit",
            "location": "",  # You can enrich this if you have location data
            "amount": flt(claim.total_sanctioned_amount or claim.total_claimed_amount or 0),
            "date": formatdate(claim.posting_date, "MMM d'th'"),  # e.g., Jun 1st
            "icon": icon,
            "iconColor": iconColor,
            "status": claim.approval_status or "Pending",
            "category": "Expense Claim",
            "purpose": claim.note or "",
            "project": claim.idp_project or "",
            "expenseCategory": expense_category,  # Can be derived from purpose or other fields
            "expenseDate": formatdate(claim.posting_date, "d MMMM, yyyy"),  # e.g., 24 February, 2024
            "note": claim.note or ""
        })

    return {
        "items":team_expenses,
        "pending_amount":total_amount
    }

def get_team_pending_advances(team):
    # Ensure team is a list
    if isinstance(team, str):
        import json
        team = json.loads(team)

    advance_data = frappe.get_all(
        "Employee Advance",
        fields=[
            "name",
            "employee",
            "advance_amount",
            "idp_project",
            "posting_date",
            "purpose",
            "status"
        ],
        filters={
            "employee": ["in", team],
            "docstatus": 0,
            "workflow_state":['!=','Rejected']
        }
    )

    def get_icon_and_color(purpose):
        if purpose and 'travel' in purpose.lower():
            return 'plane', 'bg-yellow-100 text-yellow-600'
        elif purpose and 'training' in purpose.lower():
            return 'chalkboard-teacher', 'bg-blue-100 text-blue-600'
        return 'money-bill-wave', 'bg-green-100 text-green-600'

    team_advances = []
    total_amount = 0
    for advance in advance_data:
        icon, iconColor = get_icon_and_color(advance.purpose)
        amount = flt(advance.advance_amount or 0)
        total_amount += amount
        team_advances.append({
            "id": advance.name,
            "title": "Advance Request",
            "location": "",  # Fill if applicable
            "amount": amount,
            "date": formatdate(advance.posting_date, "MMM d'th'"),
            "icon": icon,
            "iconColor": iconColor,
            "status": advance.status or "Pending",
            "category": "Employee Advance",
            "purpose": advance.purpose or "",
            "project": advance.idp_project or "",
            "expenseCategory": "Advance",
            "expenseDate": formatdate(advance.posting_date, "d MMMM, yyyy"),
            "note": advance.purpose or ""
        })

    return {
        "items": team_advances,
        "pending_amount": total_amount
    }
