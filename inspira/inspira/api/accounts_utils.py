import frappe
from frappe import _, qb
from frappe.query_builder.functions import Sum


def get_sales_collection(start_date, end_date):
    SalesInvoice = qb.DocType("Sales Invoice")
    query = (
        qb.from_(SalesInvoice)
        .select(
            Sum(SalesInvoice.grand_total).as_("total_invoiced"),
            Sum(SalesInvoice.outstanding_amount).as_("total_outstanding")
        )
        .where(
            (SalesInvoice.posting_date[start_date:end_date])
            & (SalesInvoice.docstatus == 1)
        )
    )
    result = query.run(as_dict=True)
    return result[0] if result else {}

def calculate_growth_percentage(last_fy_collected, current_fy_collected):
    if last_fy_collected == 0:
        return 0
    growth = (current_fy_collected - last_fy_collected) / last_fy_collected
    return round(growth * 100, 2)