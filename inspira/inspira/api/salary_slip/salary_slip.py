import frappe
from frappe.utils import getdate
import calendar

@frappe.whitelist()
def salary_slip(emp_id, year, month):
    import base64

    # Convert month name to number
    try:
        month_number = list(calendar.month_name).index(month)
    except ValueError:
        frappe.throw(f"Invalid month name: {month}")

    year = int(year)

    salary_slips = frappe.get_all(
        "Salary Slip",
        filters={"employee": emp_id, "docstatus": 1},
        fields=["name", "start_date"]
    )

    for slip in salary_slips:
        start = getdate(slip.start_date)
        if start.month == month_number and start.year == year:
            # Get PDF
            pdf = frappe.get_print("Salary Slip", slip.name,print_format="", as_pdf=True)
            return {
                "pdf_base64": base64.b64encode(pdf).decode("utf-8"),
                "slip_name": slip.name
            }

    return {
        "message": "No matching Salary Slip found."
    }
