from datetime import timedelta

import frappe
from frappe.utils import getdate, nowdate
from frappe.model.workflow import apply_workflow as frappe_apply_workflow


@frappe.whitelist()
def apply_workflow(doctype, docname, action):
    doc = frappe.get_doc(doctype, docname)
    return frappe_apply_workflow(doc, action)


def get_dates_in_range(start_dt, end_dt):
    for n in range((end_dt - start_dt).days + 1):
        yield start_dt + timedelta(n)


def format_period(start, end):
    return f"{start.strftime('%d %b %y')} - {end.strftime('%d %b %y')}"


def get_current_month_week_ranges(current_week=False):
    # Starts from Monday
    today = getdate(nowdate())
    month_start = today.replace(day=1)

    # Get first Monday on or before month_start
    first_week_start = month_start - timedelta(days=month_start.weekday())  # Monday

    # Get last day of the current month
    if month_start.month == 12:
        next_month_start = month_start.replace(year=month_start.year + 1, month=1, day=1)
    else:
        next_month_start = month_start.replace(month=month_start.month + 1, day=1)
    month_end = next_month_start - timedelta(days=1)

    week_ranges = []
    week_start = first_week_start

    while week_start <= month_end:
        week_end = week_start + timedelta(days=6)

        # Ensure the range stays within current month
        actual_start = max(week_start, month_start)
        actual_end = min(week_end, month_end)

        # If current_week is True, return only the current week
        if current_week and actual_start <= today <= actual_end:
            return [(actual_start, actual_end)]

        week_ranges.append((actual_start, actual_end))
        week_start += timedelta(days=7)

    return week_ranges
