import frappe
from erpnext.accounts.utils import get_fiscal_year

@frappe.whitelist()
def get_project_details():

    # Active Projects (status = 'Open')
    active = frappe.db.sql("""
        SELECT COUNT(DISTINCT project.name)
        FROM `tabIDP Project` AS project
        # JOIN `tabIDP Project User` AS pu ON pu.parent = project.name
        WHERE project.status = 'Open'
    """)[0][0]

    # # Completed Projects
    # completed = frappe.db.sql("""
    #     SELECT COUNT(DISTINCT project.name)
    #     FROM `tabIDP Project` AS project
    #     # JOIN `tabIDP Project User` AS pu ON pu.parent = project.name
    #     WHERE project.status = 'Completed'
    # """)[0][0]
    fiscal_year = get_fiscal_year()
    if not fiscal_year:
        frappe.throw("Fiscal year not found")
    start_date, end_date = fiscal_year['year_start_date'], fiscal_year['year_end_date']

	# Query completed projects within fiscal year
    completed = frappe.db.sql("""
		SELECT COUNT(DISTINCT project.name)
		FROM `tabIDP Project` AS project
		WHERE project.status = 'Completed'
		AND project.completion_date BETWEEN %s AND %s
	""", (start_date, end_date))[0][0]

    # Total Projects
    delayed = frappe.db.sql("""
        SELECT COUNT(DISTINCT project.name)
        FROM `tabIDP Project` AS project
        # JOIN `tabIDP Project User` AS pu ON pu.parent = project.name
        WHERE project.expected_end_date < %s
    """, (frappe.utils.getdate(),))[0][0]

    # Simulated delayed projects (replace with real logic)
    # delayed = 3
    on_track = max(active - delayed, 0)

    # Calculate percentages
    delayed_percentage = round((delayed / active) * 100, 2) if active else 0
    on_track_percentage = round((on_track / active) * 100, 2) if active else 0

    # Status distribution
    status_rows = frappe.db.sql("""
        SELECT project.status, COUNT(*) as count
        FROM `tabIDP Project` AS project
        # JOIN `tabIDP Project User` AS pu ON pu.parent = project.name
        # WHERE pu.user = %s
        GROUP BY project.status
    """, as_dict=True)

    total_status = sum(row["count"] for row in status_rows) or 1
    status_distribution = [
        {
            "status": row["status"],
            "percentage": round((row["count"] / total_status) * 100, 2),
            "count":row["count"]
        }
        for row in status_rows
    ]

    return {
        "activeProjects": active,
        "completedProjects": completed,
        "delayedProjects": {
            "percentage": delayed_percentage,
            "count": delayed
        },
        "onTrackProjects": {
            "percentage": on_track_percentage,
            "count": on_track
        },
        "statusDistribution": status_distribution
    }



@frappe.whitelist()
def get_project_progress():

    results = frappe.db.sql("""
        WITH ranked_milestones AS (
            SELECT 
                project.project_name AS name,
                milestone.status,
                milestone.completion_ AS progress,
                milestone.milestone,
                ROW_NUMBER() OVER (
                    PARTITION BY project.name
                    ORDER BY milestone.idx ASC  -- Or milestone.due_date, if available
                ) as row_num
            FROM `tabIDP Project` AS project
            #JOIN `tabIDP Project User` AS pu ON pu.parent = project.name
            JOIN `tabIDP Contract` AS contract ON contract.name = project.contract
            JOIN `tabIDP Milestones` AS milestone ON milestone.parent = contract.name
            WHERE project.status = 'Open'
              AND milestone.completion_ != 100
        )
        SELECT name, status, progress, milestone
        FROM ranked_milestones
        WHERE row_num = 1
    """, as_dict=True)

    def get_status(progress):
        if progress >= 75:
            return "success"
        elif progress >= 40:
            return "warning"
        else:
            return "danger"

    return [
        {
            "name": row.name,
            "status": get_status(row.progress or 0),
            "progress": row.progress or 0,
            "milestone": row.milestone
        }
        for row in results
    ]


@frappe.whitelist()
def get_sq_feet_distribution():
    # Fetch all users who are leads (distinct and ordered alphabetically for consistency)
    team_members = frappe.db.sql("""
        SELECT DISTINCT u.full_name
        FROM `tabIDP Contract Lead` AS lead
        JOIN `tabUser` AS u ON u.name = lead.user
        ORDER BY u.full_name
    """, as_list=True)

    team_members = [row[0] for row in team_members]

    # Fetch square feet per user
    results = frappe.db.sql("""
        SELECT u.full_name, SUM(contract.square_feet) as total_sq_ft
        FROM `tabIDP Contract` AS contract
        JOIN `tabIDP Contract Lead` AS lead ON lead.parent = contract.name
        JOIN `tabUser` AS u ON u.name = lead.user
        GROUP BY u.full_name
    """, as_dict=True)

    # Build mapping of name -> square feet
    sq_feet_map = {row.full_name: row.total_sq_ft for row in results}

    # Format output
    return {
        "categories": team_members,
        "series": [{
            "name": "Square Feet",
            "data": [sq_feet_map.get(name, 0) for name in team_members]
        }]
    }


@frappe.whitelist()
def get_project_distribution():
    # Fetch all users who are leads (distinct and ordered alphabetically for consistency)
    team_members = frappe.db.sql("""
        SELECT DISTINCT u.full_name
        FROM `tabIDP Contract Lead` AS lead
        JOIN `tabUser` AS u ON u.name = lead.user
        ORDER BY u.full_name
    """, as_list=True)

    team_members = [row[0] for row in team_members]

    # Fetch square feet per user
    results = frappe.db.sql("""
        SELECT u.full_name, COUNT(project.name) as total_project
        FROM `tabIDP Contract` AS contract
        JOIN `tabIDP Contract Lead` AS lead ON lead.parent = contract.name
        JOIN `tabUser` AS u ON u.name = lead.user
        JOIN `tabIDP Project` AS project ON project.contract = contract.name
        GROUP BY u.full_name
    """, as_dict=True)

    # Build mapping of name -> square feet
    sq_feet_map = {row.full_name: row.total_project for row in results}

    # Format output
    return {
        "categories": team_members,
        "series": [{
            "name": "Square Feet",
            "data": [sq_feet_map.get(name, 0) for name in team_members]
        }]
    }

@frappe.whitelist()
def get_avg_sq_feet_per_person():
    # Step 1: Get all team leads and count of employees reporting to them
    team_data = frappe.db.sql("""
        SELECT 
            u.full_name AS team,
            COUNT(emp.name) AS size
        FROM `tabIDP Contract Lead` AS lead
        JOIN `tabUser` AS u ON u.name = lead.user
        JOIN `tabEmployee` AS e ON e.user_id = u.name
        LEFT JOIN `tabEmployee` AS emp ON emp.reports_to = e.name
        GROUP BY u.full_name
    """, as_dict=True)

    # Step 2: Get total square feet per team lead
    sq_feet_data = frappe.db.sql("""
        SELECT 
            u.full_name AS team,
            SUM(contract.square_feet) AS total_sq_ft
        FROM `tabIDP Contract` AS contract
        JOIN `tabIDP Contract Lead` AS lead ON lead.parent = contract.name
        JOIN `tabUser` AS u ON u.name = lead.user
        GROUP BY u.full_name
    """, as_dict=True)

    # Step 3: Map square feet to team
    sq_feet_map = {row.team: row.total_sq_ft for row in sq_feet_data}

    # Step 4: Combine and calculate avg square feet per person
    result = []
    for row in team_data:
        team = row["team"]
        size = row["size"]
        total_sq_ft = sq_feet_map.get(team, 0) or 0
        avg_sq_per_person = total_sq_ft / size if size > 0 else total_sq_ft
        result.append({
            "team": team,
            "size": size,
            "avgSqPerPerson": round(avg_sq_per_person)
        })

    return result

