import frappe
from frappe import _, qb
from frappe.utils import flt, add_months
from frappe.query_builder.functions import Count, Sum, Extract, DateFormat
from frappe.query_builder.functions import Round

from erpnext.accounts.utils import get_fiscal_year


from inspira.inspira.api.accounts_utils import get_sales_collection, calculate_growth_percentage
from inspira.inspira.custom.utils import get_fmt_money, get_fmt_percent


@frappe.whitelist()
def get_landing_data():
	fiscal_year = get_fiscal_year()
	if not fiscal_year:
		frappe.throw("Fiscal year not found")
	fy_start, fy_end = fiscal_year['year_start_date'], fiscal_year['year_end_date']
	new_hires = frappe.db.count("Employee", {
		"status": "Active",
		"date_of_joining": ["between", [fy_start, fy_end]]
	})
	active_employee = frappe.db.get_all(
		"Employee",
		filters={'status': 'Active'},
		fields=["name", "ctc"]
	)

	# Resigned Employees in current FY
	resigned_count = frappe.db.count("Employee", {
		"status": "Left",
		"relieving_date": ["between", [fy_start, fy_end]]
	})

	# Employees at start of FY (active before FY start)
	start_employee_count = frappe.db.count("Employee", {
		"status": "Active",
		"date_of_joining": ["<", fy_start]
	})

	# Estimated average employee count in FY
	average_employee_count = (start_employee_count + len(active_employee)) / 2 or 1  # avoid division by zero
	# Calculate overall attrition %
	overall_attrition = round((resigned_count / average_employee_count) * 100, 2)
	# Average Salary (CTC)
	total_ctc = sum(emp.ctc or 0 for emp in active_employee)
	frappe.log_error("total_ctc",total_ctc)
	average_salary = round(total_ctc / len(active_employee), 2) if len(active_employee) else 0
	return {
		"ytdCollection": get_ytd_collection(fy_start, fy_end),
		"salesAnalysis": get_sales_analysis(fy_start, fy_end),
		"serviceBreakup": get_service_breakup(fy_start, fy_end),
		"upcomingPayroll": get_upcoming_payroll(),
		"metricCards": get_metric_cards(),
		"revenueChart": get_revenue_chart(fy_start, fy_end),
		"expenseCategoryChart": get_expense_category_chart(),
		"newHires":new_hires,
		"totalEmployees":len(active_employee),
		"overallAttrition":overall_attrition,
		"averageSalary":average_salary
		# "plHistoricalTable": get_pl_historical_table(),
		# "teamProjectDistribution": get_team_project_distribution()
	}


def get_ytd_collection(fy_start, fy_end):
	current_fy = get_sales_collection(fy_start, fy_end)
	current_fy_invoiced = flt(current_fy.get("total_invoiced", 0))
	current_fy_balance = flt(current_fy.get("total_outstanding", 0))
	current_fy_collected = current_fy_invoiced - current_fy_balance

	last_fy_start_date = add_months(fy_start, -12)
	last_fy_end_date = add_months(fy_end, -12)
	last_fy = get_sales_collection(last_fy_start_date, last_fy_end_date)
	last_fy_invoiced = flt(last_fy.get("total_invoiced", 0))
	last_fy_balance = flt(last_fy.get("total_outstanding", 0))
	last_fy_collected = last_fy_invoiced - last_fy_balance

	growth_percentage = calculate_growth_percentage(last_fy_collected, current_fy_collected)

	return {
		"invoiced": get_fmt_money(current_fy_invoiced),
		"collected": get_fmt_money(current_fy_collected),
		"projects": get_projects_count(fy_start, fy_end),
		"growthPercentage": get_fmt_percent(growth_percentage)
	}


def get_projects_count(start_date, end_date):
	IDPProject = qb.DocType("IDP Project")
	query = (
		qb.from_(IDPProject)
		.select(Count("*").as_("count"))
		.where(
			(IDPProject.expected_start_date <= end_date) 
			& (IDPProject.expected_end_date >= start_date)
		)
	)
	result = query.run(as_dict=True)
	return result[0]['count'] if result else 0


def get_sales_analysis(fy_start, fy_end):
	customers_added = get_customers_added(fy_start, fy_end)
	# revenue_generated = get_revenue_generated(fy_start, fy_end)
	total_square_feet = get_total_square_feet(fy_start, fy_end)
	return {
		"customersAdded": customers_added,
		# "revenueGenerated": get_fmt_money(revenue_generated),
		"totalSquareFeet": total_square_feet
	}


def get_customers_added(start_date, end_date):
	IDPContract = qb.DocType("IDP Contract")
	query = (
		qb.from_(IDPContract)
		.select(Count(IDPContract.party_name).distinct().as_("count"))
		.where(
			(IDPContract.start_date <= end_date) 
			& (IDPContract.end_date >= start_date)
		)
	)
	result = query.run(as_dict=True)
	return result[0]['count'] if result else 0


def get_total_square_feet(start_date, end_date):
	IDPContract = qb.DocType("IDP Contract")
	query = (
		qb.from_(IDPContract)
		.select(Sum(IDPContract.square_feet).as_("square_feet"))
		.where(
			(IDPContract.start_date <= end_date) 
			& (IDPContract.end_date >= start_date)
		)
	)
	result = query.run(as_dict=True)
	print(result)
	return result[0]['square_feet'] if result else 0


def get_service_breakup(fy_start, fy_end):
	IDPContract = qb.DocType("IDP Contract")
	query = (
		qb.from_(IDPContract)
		.select(
			Count(IDPContract.category).as_("count"),
			IDPContract.category
		)
		.where(
			(IDPContract.start_date <= fy_end)
			& (IDPContract.end_date >= fy_start)
		)
		.groupby(IDPContract.category)
	)
	result = query.run(as_dict=True)
	service_breakup = {}
	if result:
		for row in result:
			service_breakup[row['category']] = row['count']
	return service_breakup


def get_upcoming_payroll():
	pass


def get_metric_cards():
	interiorDeals = len(frappe.db.get_all("IDP Contract",{'completion_':["!=",100],"category":"Interior"}))
	architectureDeals = len(frappe.db.get_all("IDP Contract",{'completion_':["!=",100],"category":"Architecture"}))
	return {
		"interiorDeals":interiorDeals,
		"architectureDeals":architectureDeals
	}


def get_revenue_chart(fy_start, fy_end):
	PaymentEntry = qb.DocType("Payment Entry")
	query = (
		qb.from_(PaymentEntry)
		.select(
			DateFormat(PaymentEntry.posting_date, "%b").as_("month"),
			Sum(PaymentEntry.total_allocated_amount).as_("value")
		)
		.where(
			(PaymentEntry.posting_date >= fy_start)
			& (PaymentEntry.posting_date <= fy_end)
		)
		.groupby(Extract("month", PaymentEntry.posting_date))
	)
	result = query.run(as_dict=True)
	return result

def get_expense_category_chart():
	fiscal_year = get_fiscal_year()
	if not fiscal_year:
		frappe.throw("Fiscal year not found")
	start_date, end_date = fiscal_year['year_start_date'], fiscal_year['year_end_date']
	try:
		purchase_invoice = qb.DocType("Purchase Invoice")
		purchase_item = qb.DocType("Purchase Invoice Item")
		item = qb.DocType("Item")
		total_query = (
			qb.from_(purchase_invoice)
			.join(purchase_item).on(purchase_item.parent == purchase_invoice.name)
			.join(item).on(item.name == purchase_item.item_code)
			.select(Sum(purchase_item.base_amount).as_("total"))
			.where(
				(item.item_group == "Expenses") &
				(purchase_invoice.posting_date.between(start_date, end_date)) &
				(purchase_invoice.docstatus == 1)
			)
		)
		total_result = total_query.run(as_dict=True)
		total_amount = total_result[0]["total"] or 1  # prevent division by zero

		# Step 2: Get amount per item
		query = (
			qb.from_(purchase_invoice)
			.join(purchase_item).on(purchase_item.parent == purchase_invoice.name)
			.join(item).on(item.name == purchase_item.item_code)
			.select(
				purchase_item.item_name.as_("category"),
				Round(Sum(purchase_item.base_amount) * 100.0 / total_amount, 2).as_("percentage")
			)
			.where(
				(item.item_group == "Expenses") &
				(purchase_invoice.posting_date.between(start_date, end_date)) &
				(purchase_invoice.docstatus == 1)
			)
			.groupby(purchase_item.item_code)
		)

		result = query.run(as_dict=True)
		return result
	except Exception as e:
		return e
	
# def get_pl_historical_table():
# 	pass

# def get_team_project_distribution():
	# pass



'''
Finance Data should be in LAKHS

Revenue Generated = Sum of Contract value in that year
Contract should have active filters instead of date range

Service Based Sales Breakup - On hover show the actual amount instead of percentage

Deals Card should be independent
Interior Deals = Ongoing Interior deals in CRM Deals
Architecture Deals = Ongoing Architecture deals in CRM Deals

Upcoming Payroll - Should come with New Hires
New Hires = Employee Joining date in current FY
Total Employees = Count of Active Employees
Overall Attrition = Count of Resigned Employees in current FY / Total Average No of Employees in current FY
Average Salary = Sum of Salary (Cost to Company (CTC)) of all active Employees / Total Active Employees

Number of employees who left:
This refers to the total count of employees who departed from the company during the chosen period (e.g., month, quarter, year).
Average number of employees:
This is calculated by adding the number of employees at the beginning and end of the period and dividing by two.

Expense Category Breakup
Sum of Item Groups in Purchase Invoice where posting date is in current FY and Item Group parent is Expenses
Expenses
	- Professional Fees
	- Rent
	- Finance Cost / EMI
	- Electricity Charges
	- IT Cost
	+ Other Expenses
		- Expense 1
		- Expense 2
		- Expense 3

P&L - Current FY & Historical - Use frappe's PL Report. Show cache value with date time and show a button to generate a new one

Team-wise Project No. Distribution
SUM of IDP Contract Lead of Active Contracts

##Projects Dashboard
Active Projects = No of Ongoing Projects
Projects Completed In Current FY = 
Delayed Projects = Milestone Not Completed & End Date < Today
On Track Projects = Milestone Not Completed & End Date > Today


Project Progress
Status = Contract status should be pulled same as on Contracts Page. Also Move the logic to store it instead of calculating it every time
Active Contracts => Current Milestone = First Milestone Not Completed


Project Status = IDP Contract sum of Statuses of all Contracts No Filters

Team-wise Project Sq. feet Distribution = Sum of Square Feet of Active Contracts based on IDP Contract Lead

Team-wise Project Count Distribution = Same as Landing Page

Team Wise Avg Sq/Person
Team = IDP Contract Lead
Team Size = Count of Active Employees that reports to IDP Contract Lead
Avg Sq/Person = Sum of Sq Feet of Active Contracts based on IDP Contract Lead / Team Size
'''

# Run Profit and loss statement report 
from erpnext.accounts.report.profit_and_loss_statement.profit_and_loss_statement import execute
from frappe.utils import getdate, nowdate
@frappe.whitelist()
def profit_and_loss_statement():
	employee = frappe.get_value("Employee", {"user_id": frappe.session.user}, ["name", "company"], as_dict=True)
	if not employee:
		frappe.throw("No Employee record found for the user.")
	company = employee.company
	start_fy = frappe.get_all("Fiscal Year", fields=["name"], order_by="year_start_date ASC", limit=1)
	if not start_fy:
		frappe.throw("No fiscal years found for the company.")
	from_fiscal_year = start_fy[0].name

    # Get current fiscal year based on today
	to_fiscal_year = frappe.get_value("Fiscal Year", {"year_start_date": ["<=", nowdate()], "year_end_date": [">=", nowdate()]}, "name")
	if not to_fiscal_year:
		frappe.throw("Current fiscal year not found.")
	filters = frappe._dict({
        "company": company,
        "from_fiscal_year": from_fiscal_year,
        "to_fiscal_year": to_fiscal_year,
		"filter_based_on":"Fiscal Year",
		"periodicity":"Yearly"
    })
	report_data = execute(filters)
	return report_data

from frappe.core.doctype.prepared_report.prepared_report import make_prepared_report
@frappe.whitelist()
def prepared_profit_and_loss_statement_report():
	employee = frappe.get_value("Employee", {"user_id": frappe.session.user}, ["name", "company"], as_dict=True)
	if not employee:
		frappe.throw("No Employee record found for the user.")
	company = employee.company
	start_fy = frappe.get_all("Fiscal Year", fields=["name"], order_by="year_start_date ASC", limit=1)
	if not start_fy:
		frappe.throw("No fiscal years found for the company.")
	from_fiscal_year = start_fy[0].name

    # Get current fiscal year based on today
	to_fiscal_year = frappe.get_value("Fiscal Year", {"year_start_date": ["<=", nowdate()], "year_end_date": [">=", nowdate()]}, "name")
	if not to_fiscal_year:
		frappe.throw("Current fiscal year not found.")
	report_name = 'Profit and Loss Statement'
	filters = frappe._dict({
        "company": company,
        "from_fiscal_year": from_fiscal_year,
        "to_fiscal_year": to_fiscal_year,
		"filter_based_on":"Fiscal Year",
		"periodicity":"Yearly"
    })
	res = make_prepared_report(report_name, filters=filters)
	return res