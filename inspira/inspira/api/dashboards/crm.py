import frappe
from erpnext.accounts.utils import get_fiscal_year
from frappe.utils import getdate,add_days,fmt_money,add_years

@frappe.whitelist()
def get_crm_details():
    return {
        "leadAnalysis":get_lead(),
        "winRateData":get_win_rate_data(),
        "dealAnalysis":get_deal(),
        "leadsDealsData":get_leads_and_deals_monthwise(),
        "dealsFunnelData":get_deals_funnel_data(),
        "pendingTasks":get_data_for_fy(),
        "followups":upcomming_followup(),
        "revenue" : generated_revenue()
    }

def get_lead():
    fiscal_year = get_fiscal_year()
    year_start = getdate(fiscal_year['year_start_date'])
    year_end = getdate(fiscal_year['year_end_date'])

    total_leads = frappe.db.count('CRM Lead', {
        'creation': ['between', [year_start, year_end]]
    })

    last_year_start = add_days(year_start, -365)
    last_year_end = add_days(year_end, -365)

    last_year_leads = frappe.db.count('CRM Lead', {
        'creation': ['between', [last_year_start, last_year_end]]
    })

    # Calculate percentage change
    if last_year_leads == 0:
        percent_change = 100.0 if total_leads > 0 else 0.0
    else:
        percent_change = ((total_leads - last_year_leads) / total_leads) * 100

    interior_leads = frappe.db.count('CRM Lead', {
        'creation': ['between', [year_start, year_end]],'category':'Interior'
    })
    architecture_leads = frappe.db.count('CRM Lead', {
        'creation': ['between', [year_start, year_end]],'category':'Architecture'
    })
    converted_leads = frappe.db.count('CRM Lead', {
        'creation': ['between', [year_start, year_end]],'converted':1
    })
    open_leads = frappe.db.count('CRM Lead', {
        'creation': ['between', [year_start, year_end]],'converted':0
    })
    total_customers = frappe.db.count('Customer', {
        'creation': ['between', [year_start, year_end]]
    })
    last_year_customers = frappe.db.count('Customer', {
        'creation': ['between', [last_year_start, last_year_end]]
    })
    total_land_area = frappe.db.get_all(
        'CRM Deal',
        filters={
            'date': ['between', [year_start, year_end]],
            'status': 'Won'
        },
        fields=['sum(land_area_sq_ft) as total_land_area']
    )[0].total_land_area or 0

    return {
        "total_leads": total_leads,
        "percent_change":round(percent_change,2),
        "interior_leads":interior_leads,
        "architecture_leads":architecture_leads,
        "converted_leads":f"{round((converted_leads/total_leads)*100,2)}%",
        "open_leads":open_leads,
        "total_customers":total_customers,
        "customer_purcentage":round(((total_customers - last_year_customers)/total_customers)*100,2),
        "total_land_area":total_land_area,
    }

def get_win_rate_data():
    fiscal_year = get_fiscal_year()
    year_start = getdate(fiscal_year["year_start_date"])
    year_end = getdate(fiscal_year["year_end_date"])

    # Count 'Won' deals
    won = frappe.db.count('CRM Deal', {
        'date': ['between', [year_start, year_end]],
        'status': 'Won'
    })

    # Count 'Lost' deals
    lost = frappe.db.count('CRM Deal', {
        'date': ['between', [year_start, year_end]],
        'status': 'Lost'
    })

    total = won + lost

    def percent(value):
        return round((value / total) * 100, 2) if total > 0 else 0

    return [
        {"name": "Won", "value": won, "percentage": percent(won)},
        {"name": "Lost", "value": lost, "percentage": percent(lost)}
    ]

def get_deal():
    fiscal_year = get_fiscal_year()
    year_start = getdate(fiscal_year['year_start_date'])
    year_end = getdate(fiscal_year['year_end_date'])

    total_leads = frappe.db.count('CRM Deal', {
        'date': ['between', [year_start, year_end]]
    })
    last_year_start = add_days(year_start, -365)
    last_year_end = add_days(year_end, -365)

    last_year_leads = frappe.db.count('CRM Deal', {
        'date': ['between', [last_year_start, last_year_end]]
    })
    if last_year_leads == 0:
        percent_change = 100.0 if total_leads > 0 else 0.0
    else:
        percent_change = ((total_leads - last_year_leads) / total_leads) * 100
    interior_deal = frappe.db.count('CRM Deal', {
        'creation': ['between', [year_start, year_end]],'category':'Interior'
    })
    architecture_deal = frappe.db.count('CRM Deal', {
        'creation': ['between', [year_start, year_end]],'category':'Architecture'
    })
    open_deals = frappe.db.count('CRM Deal', {
        'date': ['between', [year_start, year_end]],
        'status': ['not in', ['Won', 'Lost']]
    })
    total_budget = get_open_deals_summary(year_start, year_end)
    return {
        "total_deal":total_leads,
        "percent_change":percent_change,
        "interior_deal":interior_deal,
        "architecture_deal":architecture_deal,
        "open_deals":open_deals,
        "total_budget":f"{round(total_budget/1000000 ,2)} Cr",
        "avg_total_budget" : f"{round((total_budget/1000000)/open_deals , 2)} Cr"
    }

import frappe
from frappe.utils import getdate, nowdate
from collections import OrderedDict
from dateutil.relativedelta import relativedelta
from erpnext.accounts.utils import get_fiscal_year  # ✅ Correct import

@frappe.whitelist()
def get_leads_and_deals_monthwise():
    # Get current fiscal year based on today
    fiscal_year_data = get_fiscal_year(nowdate())
    year_start = getdate(fiscal_year_data[1])  # Start date of FY
    year_end = getdate(fiscal_year_data[2])    # End date of FY

    # Dynamically build months between year_start and year_end
    months = []
    month_labels = []
    current = year_start
    while current <= year_end:
        month_key = current.strftime("%Y-%m")  # e.g. "2025-04"
        month_label = current.strftime("%b")   # e.g. "Apr"
        months.append(month_key)
        month_labels.append(month_label)
        current += relativedelta(months=1)

    # Initialize counters
    lead_counts = OrderedDict((m, 0) for m in months)
    deal_counts = OrderedDict((m, 0) for m in months)

    # Get Leads (based on 'date' field)
    leads = frappe.get_all("CRM Lead", filters={
        'creation': ['between', [year_start, year_end]]
    }, fields=["creation"])
    for lead in leads:
        month_key = getdate(lead.creation).strftime("%Y-%m")
        if month_key in lead_counts:
            lead_counts[month_key] += 1

    # Get Deals (based on 'creation' field)
    deals = frappe.get_all("CRM Deal", filters={
        'date': ['between', [year_start, year_end]]
    }, fields=["date"])
    for deal in deals:
        month_key = getdate(deal.date).strftime("%Y-%m")
        if month_key in deal_counts:
            deal_counts[month_key] += 1

    return {
        "months": month_labels,
        "leads": list(lead_counts.values()),
        "deals": list(deal_counts.values())
    }


@frappe.whitelist()
def get_deals_funnel_data():
    # Get current fiscal year date range
    fiscal_year_data = get_fiscal_year(nowdate())
    year_start = getdate(fiscal_year_data[1])
    year_end = getdate(fiscal_year_data[2])

    # Get deal counts grouped by status
    deal_statuses = frappe.get_all(
        "CRM Deal",
        filters={"date": ["between", [year_start, year_end]]},
        fields=["status", "count(*) as total"],
        group_by="status"
    )

    total_deals = sum(row.total for row in deal_statuses)

    # Structure the data for frontend
    result = []
    for row in deal_statuses:
        percentage = round((row.total / total_deals) * 100, 2) if total_deals else 0
        result.append({
            "name": row.status,
            "value": row.total,
            "percentage": percentage
        })

    return result


from frappe import qb
from frappe.query_builder.functions import Count,Sum

def get_data_for_fy():
    crm_task = qb.DocType("CRM Task")
    
    query = (
        qb.from_(crm_task)
        .select(
            crm_task.reference_doctype.as_('category'),
            crm_task.reference_docname.as_('name'),
            Count(crm_task.name).as_('count')
        )
        .where(
            (~crm_task.status.isin(["Done", "Canceled"]))  # Exclude 'Done' and 'Canceled'
        )
        .groupby(crm_task.reference_doctype, crm_task.reference_docname)
    )
    
    result = query.run(as_dict=True)
    return result


def upcomming_followup():
    crm_task = qb.DocType("CRM Task")
    
    query = (
        qb.from_(crm_task)
        .select(
            crm_task.reference_doctype.as_('category'),
            crm_task.reference_docname.as_('contact'),
            crm_task.due_date.as_('date'),
            (crm_task.status).as_('status')
        )
        .where(
            (~crm_task.status.isin(["Done", "Canceled"]))  # Exclude 'Done' and 'Canceled'
        )
    )
    
    result = query.run(as_dict=True)
    return result

def generated_revenue():
    contract = qb.DocType("IDP Contract")
    deal = qb.DocType("CRM Deal")
    
    # Current fiscal year
    fy_current = get_fiscal_year(nowdate())
    start_current = getdate(fy_current[1])
    end_current = getdate(fy_current[2])
    
    # Last fiscal year (subtract 1 year from start date)
    start_last = add_years(start_current, -1)
    end_last = add_years(end_current, -1)
    
    # Helper query function
    def get_revenue(start_date, end_date):
        query = (
            qb.from_(deal)
            .join(contract).on(contract.deal == deal.name)
            .select((Sum(contract.project_value) / 10000000).as_('revenue'))
            .where(
                (contract.start_date >= start_date) &
                (contract.start_date <= end_date) &
                (~contract.status.isin(["Done", "Canceled"]))  # Exclude statuses
            )
        )
        result = query.run(as_dict=True)
        return result[0]['revenue'] or 0

    # Get revenue for both years
    current_revenue = get_revenue(start_current, end_current)
    last_revenue = get_revenue(start_last, end_last)
    
    # Calculate % change (avoid divide-by-zero)
    if last_revenue == 0:
        change_percent = 100 if current_revenue > 0 else 0
    else:
        change_percent = ((current_revenue - last_revenue) / current_revenue) * 100

    return {
        "current_revenue_cr": f"{round(current_revenue, 2)} Cr",
        "last_year_revenue_cr": round(last_revenue, 2),
        "change_percent": round(change_percent, 2)
    }

def get_open_deals_summary(year_start, year_end):
    deal = qb.DocType("CRM Deal")

    query = (
        qb.from_(deal)
        .select(Sum(deal.estimated_budget_range).as_('total_estimated_budget'))
        .where(
            (deal.date >= year_start) &
            (deal.date <= year_end) &
            (~deal.status.isin(['Won', 'Lost']))
        )
    )

    result = query.run(as_dict=True)
    return result[0]['total_estimated_budget'] or 0
