import frappe
from frappe import qb
from frappe.query_builder.functions import Sum, CustomFunction
from frappe.utils import getdate
from erpnext.accounts.utils import get_fiscal_year

Month = lambda x: CustomFunction("MONTH", ["date"])(x)

@frappe.whitelist()
def finacial_data():
    return {
        "quarterwiseProjectionsData": quarterwise_projections(),
        "associateProjectionData": leadwise_projections(),
        "companyRevenueData":get_monthly_revenue_from_payments(),
        "yoyCollectionData":get_yoy_monthly_payment_percentage(),
        "accounts_receivable":accounts_receivable()
    }

def quarterwise_projections():
    IDPProject = qb.DocType("IDP Project")
    IDPContract = qb.DocType("IDP Contract")
    IDPMilestones = qb.DocType("IDP Milestones")

    # Define month expression once for reuse
    month_expr = Month(IDPMilestones.planned_end_date)

    query = (
        qb.from_(IDPProject)
        .join(IDPContract).on(IDPContract.name == IDPProject.contract)
        .join(IDPMilestones).on(IDPMilestones.parent == IDPContract.name)
        .select(
            month_expr.as_('month'),
            (Sum(IDPMilestones.fee_amount)/********).as_('amount')
        )
        .where(IDPMilestones.planned_end_date.isnotnull())
        .groupby(month_expr)
    )

    result = query.run(as_dict=True)

    quarters = {
        'Q1': 0,
        'Q2': 0,
        'Q3': 0,
        'Q4': 0
    }

    for row in result:
        month = int(row['month'])
        amount = float(row['amount'] or 0)

        if month in [4, 5, 6]:
            quarters['Q1'] += amount
        elif month in [7, 8, 9]:
            quarters['Q2'] += amount
        elif month in [10, 11, 12]:
            quarters['Q3'] += amount
        elif month in [1, 2, 3]:
            quarters['Q4'] += amount

    return [{'quarter': q, 'value': round(v, 2)} for q, v in quarters.items()]

from datetime import datetime

def leadwise_projections():
    IDPProject = qb.DocType("IDP Project")
    IDPContract = qb.DocType("IDP Contract")
    IDPMilestones = qb.DocType("IDP Milestones")
    IDPLeads = qb.DocType("IDP Contract Lead")
    User = qb.DocType("User")

    # Get current month start and end dates
    today = getdate()
    month_start = today.replace(day=1)
    if today.month == 12:
        month_end = today.replace(year=today.year + 1, month=1, day=1)
    else:
        month_end = today.replace(month=today.month + 1, day=1)

    query = (
        qb.from_(IDPProject)
        .join(IDPContract).on(IDPContract.name == IDPProject.contract)
        .join(IDPMilestones).on(IDPMilestones.parent == IDPContract.name)
        .join(IDPLeads).on(IDPLeads.parent == IDPContract.name)
        .join(User).on(User.name == IDPLeads.user)
        .select(
            (Sum(IDPMilestones.fee_amount) / ********).as_('value'),
            User.full_name.as_('name')
        )
        .where(
            (IDPMilestones.planned_end_date >= month_start) &
            (IDPMilestones.planned_end_date < month_end)
        )
        .groupby(User.full_name)
    )

    result = query.run(as_dict=True)
    return result  # directly usable in Vue

def get_monthly_revenue_from_payments():
    PaymentEntry = qb.DocType("Payment Entry")
    month_expr = Month(PaymentEntry.posting_date)

    # Get current fiscal year range
    _, fy_start, fy_end = get_fiscal_year(getdate())

    # Month map for fiscal year Apr–Mar
    month_map = {
        1: "Jan", 2: "Feb", 3: "Mar",
        4: "Apr", 5: "May", 6: "Jun",
        7: "Jul", 8: "Aug", 9: "Sep",
        10: "Oct", 11: "Nov", 12: "Dec"
    }
    fiscal_months = [4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3]

    query = (
        qb.from_(PaymentEntry)
        .select(
            month_expr.as_('month'),
            (Sum(PaymentEntry.paid_amount) / ********).as_('value')  # In Crores
        )
        .where(
            (PaymentEntry.posting_date >= fy_start) &
            (PaymentEntry.posting_date <= fy_end) &
            (PaymentEntry.docstatus == 1) &
            (PaymentEntry.payment_type == "Receive")
        )
        .groupby(month_expr)
    )

    result = query.run(as_dict=True)

    # Prepare month-wise result
    revenue_map = {m: 0 for m in range(1, 13)}
    for row in result:
        revenue_map[int(row["month"])] = float(row["value"] or 0)

    # Format output in Apr–Mar order
    formatted = [
        {"month": month_map[m], "value": round(revenue_map[m], 2)}
        for m in fiscal_months
    ]

    return formatted


from frappe.query_builder import DocType
from datetime import date

def get_yoy_monthly_payment_percentage():
    PaymentEntry = DocType("Payment Entry")
    month_map = ["Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec", "Jan", "Feb", "Mar"]
    fiscal_month_order = [4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3]

    def get_fy_range_from_date(dt, offset=0):
        year = dt.year
        if dt.month < 4:
            year -= 1
        year -= offset
        return date(year, 4, 1), date(year + 1, 3, 31), f"fy{str(year)[-2:]}{str(year+1)[-2:]}"

    def get_data_for_fy(start_date, end_date):
        month_expr = Month(PaymentEntry.posting_date)

        query = (
            qb.from_(PaymentEntry)
            .select(
                month_expr.as_('month'),
                Sum(PaymentEntry.paid_amount).as_('amount')
            )
            .where(
                (PaymentEntry.posting_date >= start_date) &
                (PaymentEntry.posting_date <= end_date) &
                (PaymentEntry.docstatus == 1) &
                (PaymentEntry.payment_type == "Receive")
            )
            .groupby(month_expr)
        )
        result = query.run(as_dict=True)

        monthly = {int(r["month"]): float(r["amount"] or 0) for r in result}
        values = [monthly.get(m, 0) for m in fiscal_month_order]
        max_val = max(values) or 1  # prevent division by zero
        percentage = [round((v / max_val) * 100, 2) for v in values]

        return percentage

    # Prepare result dict dynamically for last 3 FYs
    today = getdate()
    result = {
        "months": month_map
    }

    for offset in [2, 1, 0]:  # Last 3 FYs: oldest to newest
        fy_start, fy_end, key = get_fy_range_from_date(today, offset)
        result[key] = get_data_for_fy(fy_start, fy_end)

    return result

# def accounts_receivable():
#     report_name = "Accounts Receivable"
#     company = frappe.db.get_value("Employee", {'user_id': frappe.session.user}, "company")
#     filters = {
#         'company': company,
#         'report_date': frappe.utils.getdate(),
#         'ageing_based_on': 'Due Date',
#         "calculate_ageing_with": "Report Date",
#         "range": "30, 60, 90, 120"
#     }
#     res = frappe.call("frappe.desk.query_report.run", report_name=report_name, filters=filters)
#     mapped_result = []
#     for row in res.get("result", []):
#         mapped_result.append({
#             "date": frappe.utils.formatdate(row.get("posting_date")),
#             "customer": row.get("customer_name"),
#             "associate": row.get("sales_person"),
#             "fee": row.get("outstanding_amount"),
#             "gst": row.get("gst", 0),  # optional
#             "total": row.get("invoice_total", row.get("outstanding_amount"))
#         })

#     return mapped_result

def accounts_receivable():
    report_name = "Accounts Receivable"
    company = frappe.db.get_value("Employee", {'user_id': frappe.session.user}, "company")
    filters = {
        'company': company,
        'report_date': frappe.utils.getdate(),
        'ageing_based_on': 'Due Date',
        "calculate_ageing_with": "Report Date",
        "range": "30, 60, 90, 120"
    }

    res = frappe.call("frappe.desk.query_report.run", report_name=report_name, filters=filters)

    # Extract fieldnames from the columns metadata
    columns = res.get("columns", [])
    rows = res.get("result", [])

    fieldnames = [col.get("fieldname") for col in columns]

    mapped_result = []
    for row in rows[:-1]:
        # Ensure it's a list and matches column length
        if isinstance(row, list):
            row_dict = dict(zip(fieldnames, row))
        elif isinstance(row, dict):
            row_dict = row
        else:
            continue

        mapped_result.append({
            "date": frappe.utils.formatdate(row_dict.get("posting_date")),
            "customer": row_dict.get("customer_name"),
            "associate": row_dict.get("sales_person"),
            "fee": row_dict.get("outstanding", 0),
            "gst": row_dict.get("gst", 0),  # fallback if missing
            "total": row_dict.get("invoice_total", row_dict.get("outstanding", 0))
        })

    last_row = {}
    if rows:
        raw = rows[-1]
        last_row = dict(zip(fieldnames, raw)) if isinstance(raw, list) else raw

    # Extract ageing columns dynamically
    ageing_columns = [col for col in columns if col.get("fieldname", "").startswith("range")]

    debtors_aging_data = []
    for col in ageing_columns:
        fieldname = col.get("fieldname")
        label = col.get("label")
        # value = float(last_row.get(fieldname, 0))
        raw_val = last_row.get(fieldname, 0)
        try:
            value = float(raw_val)/100000 if raw_val not in (None, '', '-') else 0.0
        except ValueError:
            value = 0.0
        debtors_aging_data.append({
            "range": label,
            "value": value or 0
        })
        
    return {
        "details": mapped_result,
        "aging_summary": debtors_aging_data
    }
