import frappe


@frappe.whitelist()
def get_reportees(manager_email_id=None, manager_hr_id=None):
    if not manager_email_id and not manager_hr_id:
        frappe.throw("Please provide either manager email id or manager hr id")

    if manager_email_id:
        manager_hr_id = frappe.db.get_value("Employee", {"user_id": manager_email_id}, "name")

    if not manager_hr_id:
        return []

    filters = [["Employee","status","=","Active"],["Employee","reports_to","=",manager_hr_id]]
    return frappe.get_all("Employee", filters=filters, fields=["name","employee_name","user_id"])


def get_present_days(employee, start_date, end_date):
    attendance = frappe.get_all("Attendance", filters={
        "employee": employee,
        "attendance_date": ["between", [start_date, end_date]],
        "status": ["in", ["Present", "Half Day"]],
        "docstatus": 1
    }, fields=["attendance_date", "status"])
    
    present_days = 0
    for record in attendance:
        if record.status == "Present":
            present_days += 1
        elif record.status == "Half Day":
            present_days += 0.5
    return present_days

def get_attendance(employee, start_date, end_date):
    attendance = frappe.get_all("Attendance", filters={
        "employee": employee,
        "attendance_date": ["Between", [start_date, end_date]],
        "docstatus": 1
    }, fields=["attendance_date", "status", "shift"])
    return attendance


def get_holidays(start_date, end_date):
    return frappe.get_all("Holiday", filters={
        "holiday_date": ["between", [start_date, end_date]],
        "parentfield": "holidays"
    }, fields=["holiday_date", "weekly_off"])
