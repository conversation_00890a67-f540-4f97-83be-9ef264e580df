from datetime import datetime
from dateutil.relativedelta import relativedelta

import frappe
from frappe.utils import getdate, nowdate, get_timedelta

from inspira.inspira.api.hrms.hr_utils import get_reportees, get_present_days, get_holidays, get_attendance
from inspira.inspira.api.utils import get_dates_in_range, format_period, get_current_month_week_ranges


@frappe.whitelist()
def get_team_timesheets(start_date, end_date):
	employees = get_reportees(frappe.session.user)
	timesheets = []
	for employee in employees:
		emp_timesheet = get_employee_timesheet(employee.name, start_date, end_date)
		if emp_timesheet:
			timesheets.append(emp_timesheet)
	return timesheets


def get_employee_timesheet(employee, start_date, end_date):
	filters = {
		"employee": employee,
		"start_date": start_date,
		"end_date": end_date,
		"workflow_state": ["!=", "Not Submitted"],
	}
	try:
		timesheet = frappe.get_doc("IDP Timesheet", filters)
		resp = {
			"timesheet_id": "",
			"id": "",
			"daysPresent": 0,
			"totalHours": 0,
			"hoursFilled": 0,
			"hoursFilled": 0,
			"compliance": 0,
			"dailyHours": [],
			"weekTotal": 0,
			"workflow_state": "",
		}
		if timesheet:
			# If more than one rows than add the hours on basis on day
			daily_hours = [get_timedelta("00:00:00")] * 7  # Initialize array for Monday to Sunday
			days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
			for weekly_timesheet in timesheet.weekly_timesheets:
				for idx, day in enumerate(days):
					daily_hours[idx] += get_timedelta(weekly_timesheet.get(day))

			resp["dailyHours"] = daily_hours
			resp["timesheet_id"] = timesheet.name
			resp["id"] = timesheet.employee
			resp["name"] = timesheet.employee_name
			resp["daysPresent"] = get_present_days(employee, start_date, end_date)
			resp["totalHours"] = 8 * resp["daysPresent"]
			resp["hoursFilled"] = timesheet.total_hours
			resp["compliance"] = round((resp["hoursFilled"] / resp["totalHours"]) * 100, 2) if resp["totalHours"] else 0
			resp["weekTotal"] = timesheet.total_hours
			resp["workflow_state"] = timesheet.workflow_state
		return resp

	except frappe.DoesNotExistError:
		return {}


@frappe.whitelist()
def get_user_timesheet_compliance_monthly():
	employee = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, ["name","employee_name","user_id"], as_dict=1)
	week_ranges = get_current_month_week_ranges()
	results = []

	for start, end in week_ranges:
		week_data = get_timesheet_compliance([employee], start.isoformat(), end.isoformat(), is_weekly=True)
		if week_data:
			results.append(week_data[0])  # only one employee data expected

	return results


@frappe.whitelist()
def get_current_week_team_timesheet_compliance():
	last_week = get_current_month_week_ranges(current_week=True)
	employees = get_reportees(manager_email_id=frappe.session.user)
	start_date = last_week[0][0].isoformat()
	end_date = last_week[0][1].isoformat()
	return get_timesheet_compliance(employees, start_date, end_date, is_weekly=True)


def get_timesheet_compliance(employees, start_date, end_date, is_weekly=False):
	'''
	Resp Structure
	{
		period: '01 Jan 24 - 07 Jan 24',
		name: 'Sandeep Kakde',
		irregularities: ['filled', false, 'holiday', 'weekoff', 'filled', 'filled', 'weekoff'],
		compliance: '83%'
	}
	'''
	start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
	end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
	date_list = [d.isoformat() for d in get_dates_in_range(start_dt, end_dt)]
	period = format_period(start_dt, end_dt)

	holidays = get_holidays(start_date, end_date)
	# Prepare sets for quick lookup
	holiday_map = {getdate(h['holiday_date']).isoformat(): h for h in holidays}

	timesheets = []
	for employee in employees:
		attendance = get_attendance(employee['name'], start_date, end_date)
		attendance_map = {
			getdate(a["attendance_date"]).isoformat(): a["status"]
			for a in attendance
		}

		base_query = f"""
			SELECT
				ts.employee,
				ts.employee,
				ts.employee_name,
				DATE(tsd.from_time) ts_date,
				tsd.hours as total_hours,
				tsd.shift_hours,
				if (tsd.hours >= tsd.shift_hours, 1, 0) as is_compliant
			FROM
				`tabIDP Timesheet` ts
			INNER JOIN
				`tabIDP Timesheet Detail` tsd
			ON
				ts.name = tsd.parent
			WHERE
				ts.employee = '{employee['name']}'
				AND ts.docstatus = 1
		"""

		if is_weekly:
			base_query += f"""
				AND ts.start_date = '{start_date}'
				AND ts.end_date = '{end_date}'
			"""
		else:
			base_query += f"""
				AND ts.start_date <= '{end_date}' 
				AND ts.end_date >= '{start_date}'
			"""

		base_query = base_query + " GROUP BY tsd.name "
		res = frappe.db.sql(base_query, as_dict=True)
		today = getdate(nowdate())

		
		timesheet_map = {}
		for r in res:
			timesheet_map[getdate(r["ts_date"]).isoformat()] = r

		data = {
			"period": period,
			"name": employee['employee_name'],
			"irregularities": [],
			"compliance": 0
		}

		total_working_days = 0
		compliant_days = 0


		for date in date_list:
			status = attendance_map.get(date)
			timesheet = timesheet_map.get(date)
			holiday_info = holiday_map.get(date)
			

			# Case: It's a holiday (skip adding to total_working_days even if they worked)
			if holiday_info:
				if status or timesheet:
					# Worked on holiday (attendance or timesheet) — record it
					data["irregularities"].append("filled")
					if getdate(date) <= today: # If Future Date then skip
						compliant_days += 1
						total_working_days += 1
				else:
					irregularity_type = "weekoff" if holiday_info.get("weekly_off") else "holiday"
					data["irregularities"].append(irregularity_type)
					if getdate(date) <= today: # If Future Date then skip
						compliant_days += 1
						total_working_days += 1
				continue

			# If Future Date then skip
			if getdate(date) > today:
				data["irregularities"].append("-")
				continue

			# Case: Present on working day
			if status == "Present":
				if timesheet and timesheet["total_hours"] >= timesheet["shift_hours"]:
					data["irregularities"].append("filled")
					compliant_days += 1
				else:
					data["irregularities"].append("false")
				total_working_days += 1

			#Case: Half Day on working day
			elif status == "Half Day":
				if timesheet and timesheet["total_hours"] >= (timesheet["shift_hours"] / 2):
					data["irregularities"].append("filled")
					compliant_days += 1
				else:
					data["irregularities"].append("false")
				total_working_days += 1

			# Case: Absent
			elif status == "Absent":
				data["irregularities"].append("absent")
				total_working_days += 1

			# Case: No attendance, not a holiday → treat as absent
			elif timesheet:
				if timesheet["total_hours"] >= timesheet["shift_hours"]:
					data["irregularities"].append("filled")
					compliant_days += 1
				else:
					data["irregularities"].append("false")
				total_working_days += 1
			else:
				data["irregularities"].append("absent")
				total_working_days += 1

		data["compliance"] = round((compliant_days / total_working_days) * 100) if total_working_days else 0
		timesheets.append(data)

	return timesheets


def compute_average_compliance(timesheet_data):
	if not timesheet_data:
		return 0
	total = sum(emp["compliance"] for emp in timesheet_data)
	return round(total / len(timesheet_data), 2)


def get_team_timesheet_compliance(start_date, end_date, is_weekly=True):
	employees = get_reportees(manager_email_id=frappe.session.user)
	return get_timesheet_compliance(employees, start_date, end_date, is_weekly)


@frappe.whitelist()
def get_overall_quarterly_compliance():
	today = getdate(nowdate())

	fiscal_year = frappe.defaults.get_user_default("fiscal_year")
	if not fiscal_year:
		fiscal_year = frappe.get_value("Fiscal Year", {"disabled": 0, "year_start_date": ["<=", today]}, "name")

	fy = frappe.get_doc("Fiscal Year", fiscal_year)
	fy_start = getdate(fy.year_start_date)
	fy_end = getdate(fy.year_end_date)

	# Define quarters: (name, start_date, end_date)
	quarters = []
	start = fy_start
	for i in range(4):
		end = start + relativedelta(months=3, days=-1)
		if end > fy_end:
			end = fy_end
		quarters.append((f"Q{i+1}", start, end))
		start = end + relativedelta(days=1)

	result = {}
	categories = []
	data = []
	for q_name, q_start, q_end in quarters:
		timesheet_data = get_team_timesheet_compliance(q_start.isoformat(), q_end.isoformat(), is_weekly=False)
		avg_compliance = compute_average_compliance(timesheet_data)
		categories.append(q_name)
		data.append(avg_compliance)

	return {
		"series": [
			{
				"name": "Compliance",
				"data": data
			}
		],
		"categories": categories
	}


@frappe.whitelist()
def get_current_month_and_ytd_compliance():
	today = getdate(nowdate())

	# ----- Current Month -----
	month_start = today.replace(day=1)
	month_end = today

	monthly_data = get_team_timesheet_compliance(
		month_start.isoformat(),
		month_end.isoformat(),
		is_weekly=False
	)

	# ----- YTD -----
	fiscal_year = frappe.defaults.get_user_default("fiscal_year")
	if not fiscal_year:
		fiscal_year = frappe.get_value(
			"Fiscal Year",
			{"disabled": 0, "year_start_date": ["<=", today]},
			"name"
		)

	fy_doc = frappe.get_doc("Fiscal Year", fiscal_year)
	fy_start = getdate(fy_doc.year_start_date)
	ytd_end = today

	ytd_data = get_team_timesheet_compliance(
		fy_start.isoformat(),
		ytd_end.isoformat(),
		is_weekly=False
	)

	result = {
		"month": today.strftime("%B"),
		"current_month": compute_average_compliance(monthly_data),
		"ytd": compute_average_compliance(ytd_data),
	}

	return result
