import frappe
from frappe.utils import formatdate, date_diff,strip_html
from frappe.query_builder import Order
from erpnext.setup.doctype.employee.employee import get_holiday_list_for_employee

# Users pending leave requests
@frappe.whitelist()
def get_pending_leaves(user):
    leave_apps = frappe.get_all("Leave Application",
        filters={
            "status": ["!=","Approved"],
            "employee": frappe.db.get_value("Employee", {"user_id": user}, "name")
        },
        fields=["from_date", "to_date", "leave_type", "posting_date", "description","status","name"]
    )
    results = []
    for app in leave_apps:
        from_date = frappe.utils.getdate(app.from_date)
        to_date = frappe.utils.getdate(app.to_date)
        today = frappe.utils.getdate(frappe.utils.nowdate())
        if app.status == "Rejected" and to_date <= today:
            continue
        days = date_diff(to_date, from_date) + 1
        period = f"{formatdate(from_date, 'MMM dd')} - {formatdate(to_date, 'MMM dd, yyyy')} ({days} days)"
        result = {
            "period": period,
            "type": app.leave_type,
            "requestedOn": formatdate(app.posting_date, "MMM dd, yyyy"),
            "note": app.description or "",
            # "status": "Pending"
            "status": "Pending" if app.status == "Open" else app.status,
            "id":app.name
        }
        results.append(result)

    past_leave_apps = frappe.get_all("Leave Application",
        filters={
            "status":"Approved",
            "to_date":["<",frappe.utils.getdate()],
            "employee": frappe.db.get_value("Employee", {"user_id": user}, "name")
        },
        fields=["from_date", "to_date", "leave_type", "posting_date", "description"]
    )
    past_leaves = []
    for app in past_leave_apps:
        from_date = frappe.utils.getdate(app.from_date)
        to_date = frappe.utils.getdate(app.to_date)
        days = date_diff(to_date, from_date) + 1
        period = f"{formatdate(from_date, 'MMM dd')} - {formatdate(to_date, 'MMM dd, yyyy')} " if from_date != to_date else f"{formatdate(from_date, 'MMM dd')} "
        result = {
            "date": period,
            "type": app.leave_type,
            "duration": f"{days} days" if days>1 else f"{days} day",
            "icon":""
        }
        past_leaves.append(result)
    
    upcoming_leave_apps = frappe.get_all("Leave Application",
        filters={
            "status":"Approved",
            "to_date":[">",frappe.utils.getdate()],
            "employee": frappe.db.get_value("Employee", {"user_id": user}, "name")
        },
        fields=["from_date", "to_date", "leave_type", "posting_date", "description","name"]
    )
    upcoming_leaves = []
    for app in upcoming_leave_apps:
        from_date = frappe.utils.getdate(app.from_date)
        to_date = frappe.utils.getdate(app.to_date)
        days = date_diff(to_date, from_date) + 1
        period = f"{formatdate(from_date, 'MMM dd')} - {formatdate(to_date, 'MMM dd, yyyy')} " if from_date != to_date else f"{formatdate(from_date, 'MMM dd')} "
        result = {
            "date": period,
            "type": app.leave_type,
            "duration": f"{days} days" if days>1 else f"{days} day",
            "icon":"",
            "name":app.name
        }
        upcoming_leaves.append(result)

    
    return {
            "pending_leaves":results,
            "past_leaves":past_leaves,
            "upcoming_leaves":upcoming_leaves
            # "leave_types":frappe.db.get_all("Leave Type",pluck="name")
        }   

@frappe.whitelist()
def get_holidays_for_employee(employee: str) -> list[dict]:
	holiday_list = get_holiday_list_for_employee(employee, raise_exception=False)
	if not holiday_list:
		return []

	Holiday = frappe.qb.DocType("Holiday")
	holidays = (
		frappe.qb.from_(Holiday)
		.select(Holiday.name, Holiday.holiday_date, Holiday.description,Holiday.holiday_type)
		.where((Holiday.parent == holiday_list) & (Holiday.weekly_off == 0) & (Holiday.holiday_date > frappe.utils.getdate()))
		.orderby(Holiday.holiday_date, order=Order.asc)
	).run(as_dict=True)

	for holiday in holidays:
		holiday["description"] = strip_html(holiday["description"] or "").strip()

	return holidays

@frappe.whitelist()
def submit(name,status):
    leave_application = frappe.get_doc("Leave Application",name)
    leave_application.status = status
    leave_application.save()
    leave_application.submit()

from frappe import _
from hrms.hr.utils import get_leave_period

@frappe.whitelist()
def optional_holiday(leave_type):
    company = frappe.db.get_value("Employee", {'user_id': frappe.session.user}, "company")
    leave_period = get_leave_period(frappe.utils.getdate(), frappe.utils.getdate(), company)

    if not leave_period:
        frappe.throw(_("Cannot find active Leave Period"))

    optional_holiday_list = frappe.db.get_value(
        "Leave Period", leave_period[0]["name"], "optional_holiday_list"
    )

    if not optional_holiday_list:
        frappe.throw(
            _("Optional Holiday List not set for leave period {0}").format(leave_period[0]["name"])
        )

    # Corrected query: fetch holidays from the 'holidays' child table inside 'Holiday List'
    holiday_list_doc = frappe.get_doc("Holiday List", optional_holiday_list)
    holiday_list = []
    for holiday in holiday_list_doc.holidays:
        holiday_list.append({
            "holiday_date": holiday.holiday_date,
            "description": holiday.description
        })

    is_optional_leave = frappe.db.get_value("Leave Type", leave_type, "is_optional_leave")

    return {
        "is_optional_leave": is_optional_leave,
        "holiday_list": holiday_list
    }
