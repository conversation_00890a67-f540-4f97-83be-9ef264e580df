import frappe
from frappe import _
from frappe.utils import flt, format_date

from inspira.inspira.custom.utils import get_fmt_money, get_fmt_percent


@frappe.whitelist()
def get_contract_dashboard_data(project_id=None):
    """
    Get all data needed for the Contract Dashboard
    Args:
        project_id: ID of the project
    Returns:
        dict: All data for the dashboard
    """
    project_doc = frappe.get_doc("IDP Project", project_id)
    contract_id = project_doc.contract

    if not contract_id:
        frappe.throw(_("Contract ID is required"))
    
    # Get contract document once
    contract_doc = frappe.get_doc("IDP Contract", contract_id)

    project_value = flt(contract_doc.project_value) or 0
    total_received = sum(flt(m.payment_received) for m in contract_doc.milestones)
    timesheet_cost = sum(flt(m.cost_incurred) for m in contract_doc.milestones)
    contract_link = f"{frappe.utils.get_url()}{contract_doc.contract_pdf or ''}" if contract_doc.contract_pdf else ""

    return {
        "contract": get_contract_info(contract_doc, project_value, total_received),
        "finance": get_finance_data(contract_doc, project_doc, project_value, total_received, timesheet_cost),
        "client": get_client_info(contract_doc),
        "milestones": get_milestones_data(contract_doc),
        "variables": get_variable_management_data(project_doc),
        "resources": get_resource_management_data(project_doc),
        "areas":get_areas_from_project(contract_doc),
        "ContractLink":{ "Link": contract_link  }
    }


def get_contract_info(contract, project_value, total_received):
    """
    Get basic contract information
    Args:
        contract: Contract document
    """    
    # Calculate percentage received
    percentage = 0
    if project_value > 0:
        percentage = (total_received / project_value) * 100

    status_doc = frappe.get_cached_doc("IDP Status Master", contract.status)
    
    return {
        "id": contract.name,
        "status": status_doc.status,
        "statusColor": status_doc.color,
        "type": contract.contract_type,
        "squareFeet": contract.square_feet,
        "category": contract.category,
        "classification": contract.classification,
        "subClassification": contract.sub_classification,
        "startDate": format_date(contract.start_date) if contract.start_date else None,
        "endDate": format_date(contract.end_date) if contract.end_date else None,
        "projectFee": get_fmt_money(project_value, currency="INR"),
        "totalReceived": get_fmt_money(total_received, currency="INR"),
        "balance": get_fmt_money(project_value - total_received, currency="INR"),
        "percentage": get_fmt_percent(percentage),
    }


def get_finance_data(contract, project, project_value, fee_received, timesheet_cost):
    """
    Get finance information for the contract
    Args:
        contract: Contract document
        project: Project data
    """    
    # Calculate percentage received
    percentage_received = 0
    if project_value > 0:
        percentage_received = (fee_received / project_value) * 100
    
    # Calculate profitability
    profitability = 0
    if fee_received > 0:
        profitability = ((fee_received - timesheet_cost) / fee_received) * 100
    
    # Determine budget status
    status = "In Budget"
    if timesheet_cost or fee_received:
        if timesheet_cost >= fee_received:
            status = "Cost Exceeded"
        elif timesheet_cost > (fee_received * 0.8):
            status = "Alert"
    
    # Calculate cost incurred percentage
    cost_incurred = 0
    if project_value > 0:
        cost_incurred = (timesheet_cost / project_value) * 100
    
    return {
        "costIncurred": get_fmt_percent(cost_incurred),
        "projectFee": get_fmt_money(project_value, currency="INR"),
        "feeReceived": get_fmt_money(fee_received, currency="INR"),
        "percentageReceived": get_fmt_percent(percentage_received),
        "balanceAmount": get_fmt_money(project_value - fee_received, currency="INR"),
        "timesheetCost": get_fmt_money(timesheet_cost, currency="INR"),
        "profitability": get_fmt_percent(profitability),
        "status": status
    }


def get_client_info(contract):
    customer_name = contract.party_name
    if not customer_name:
        return {"name": "Not Available", "email": "", "address": "", "phone": ""}

    contact_info = frappe.db.get_all("Contact", 
        fields=["first_name", "last_name", "email_id", "phone", "mobile_no"],
        filters=[["Dynamic Link","link_name","=", customer_name],["is_billing_contact","!=",1]],
        limit=1
    )
    address_info = frappe.db.get_all("Address",
        fields=["address_line1", "address_line2", "city", "state", "pincode"],
        filters=[["Dynamic Link","link_name","=", customer_name],['address_type',"!=","Billing"]],
        limit=1
    )

    contact = contact_info[0] if contact_info else {}
    address = address_info[0] if address_info else {}
    contact_name = f"{contact.get('first_name', '')} {contact.get('last_name', '')}".strip() or "Not Available"
    address_str = ", ".join(filter(None, [
        address.get('address_line1'), address.get('address_line2'), 
        address.get('city'), address.get('state'), address.get('pincode')
    ])) or "Not Available"
    contract = {
            "name": contact_name,
            "email": contact.get("email_id", ""),
            "address": address_str,
            "phone": contact.get("mobile_no") or contact.get("phone") or ""
        }
    
    contact_info = frappe.db.get_all("Contact", 
        fields=["first_name", "last_name", "email_id", "phone", "mobile_no"],
        filters=[["Dynamic Link","link_name","=", customer_name],["is_billing_contact","=",1]],
        limit=1
    )
    address_info = frappe.db.get_all("Address",
        fields=["address_line1", "address_line2", "city", "state", "pincode"],
        filters=[["Dynamic Link","link_name","=", customer_name],['address_type',"=","Billing"]],
        limit=1
    )

    contact = contact_info[0] if contact_info else {}
    address = address_info[0] if address_info else {}
    contact_name = f"{contact.get('first_name', '')} {contact.get('last_name', '')}".strip() or "Not Available"
    address_str = ", ".join(filter(None, [
        address.get('address_line1'), address.get('address_line2'), 
        address.get('city'), address.get('state'), address.get('pincode')
    ])) or "Not Available"
    billing = {
            "name": contact_name,
            "email": contact.get("email_id", ""),
            "address": address_str,
            "phone": contact.get("mobile_no") or contact.get("phone") or ""
        }
    return {
        "contact":contract,
        "billing":billing
    }


def get_milestones_data(contract):
    """
    Get milestones data for the contract
    Args:
        contract: Contract document
    """
    formatted_milestones = []
    
    for m in contract.milestones:
        # Get status name from status ID
        payment_status = m.payment_status
        payment_status_color = None
        if m.payment_status:
            status_name, payment_status_color = frappe.get_value("IDP Status Master", m.payment_status, ["status", "color"])
            if status_name:
                payment_status = status_name
        
        formatted_milestones.append({
            "id": m.name,
            "milestone": m.milestone,
            "feePercentage": get_fmt_percent(m.fee_),
            "feeAmount": get_fmt_money(m.fee_amount, currency="INR"),
            "completionPercentage": get_fmt_percent(m.completion_),
            "contractEndDate": format_date(m.contract_end_date) if m.contract_end_date else None,
            "plannedEndDate": format_date(m.planned_end_date) if m.planned_end_date else None,
            "actualEndDate": format_date(m.actual_end_date) if m.actual_end_date else None,
            "clientApprovalDate": m.client_approval_date.strftime('%Y-%m-%d') if m.client_approval_date else None,
            "invoiceDate": format_date(m.invoice_date) if m.invoice_date else None,
            "paymentReceived": get_fmt_money(m.payment_received, currency="INR"),
            "pendingPayment": get_fmt_money(m.payment_pending, currency="INR"),
            "paymentDate": format_date(m.payment_date) if m.payment_date else None,
            "paymentStatus": payment_status,
            "paymentStatusColor": payment_status_color,
            "costIncurred": get_fmt_money(m.cost_incurred, currency="INR"),
            "profitabilityPercentage":get_fmt_percent(m.profitability_)
        })
    
    return formatted_milestones


def get_variable_management_data(project_doc):
    """
    Get variable management data for the contract
    Args:
        contract: Contract document
    """
    formatted_variables = []
    
    if hasattr(project_doc, 'users'):
        for u in project_doc.users:
            if u.variable_:
                formatted_variables.append({
                    "name": u.full_name or "Not Specified",
                    "designation": u.designation or "Not Specified",
                    "variablePercentage": get_fmt_percent(u.variable_),
                    "variableCategory": "Finder's Fee",
                    "variableAmount": get_fmt_money(u.variable_amount, currency="INR"),
                    "status": "Not Specified"
                })
    
    return formatted_variables


def get_resource_management_data(project_doc):
    """
    Get resource management data for the contract
    Args:
        contract: Contract document
    """
    formatted_resources = []
    
    if hasattr(project_doc, 'users'):
        for u in project_doc.users:
            formatted_resources.append({
                "category": "Not Specified",
                "name": u.full_name or "Not Specified",
                "servicesOffered": "Not Specified",
                "email": u.email or ""
            })
    
    return formatted_resources

def get_areas_from_project(contract_doc):
    data = frappe.db.sql(f"""
        SELECT 
            area.floor,
            area.area,
            area.square_feet_area
        FROM `tabIDP Contract` AS c 
        JOIN `tabIDP Project` AS p ON p.contract = c.name
        JOIN `tabIDP Project Area` AS area ON area.parent = p.name
        WHERE c.name = %s
    """, (contract_doc.name,), as_dict=True)

    # Format keys to match frontend
    formatted_data = [
        {
            "floor": d["floor"],
            "area": d["area"],
            "Squarefeet": f"{d['square_feet_area']} sq ft"
        } for d in data
    ]

    return formatted_data
