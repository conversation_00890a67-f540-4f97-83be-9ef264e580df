import frappe
from frappe import _

TASK_DOCTYPE = "IDP Task"
ASSIGNEE_DOCTYPE = "IDP Task Assignee"


def fetch_tasks(
	filters: dict[str, str],
	fields: list[str],
	group_by: str | None = None,
	order_by: str | None = None,
) -> list[dict[str, str]]:
	"""Fetch tasks based on filters and fields."""
	return frappe.get_all(
		TASK_DOCTYPE,
		filters=filters,
		fields=fields,
		group_by=group_by,
		order_by=order_by,
	)


def split_assignees(assignees: str | None) -> list[str]:
	"""
	Split a comma-separated string of assignees into a list.
	"""
	assignees = frappe.parse_json(assignees)
	res = []
	for assignee in assignees:
		if assignee["value"]:
			res.append(assignee)
	return res


def format_date(date: str | None) -> str:
	"""
	Format date to 'dd-mm-yyyy' or return 'Not set' if date is None.
	"""
	return frappe.utils.format_date(date) if date else None


@frappe.whitelist()
def get_milestones(project_id: str) -> list[dict[str, str]]:
	"""
	subtabs on planner page
	"""
	filters = {"project": project_id, "is_milestone": 1, "type": "Milestone"}
	fields = ["name AS value", "subject AS label"]
	return fetch_tasks(filters, fields)


@frappe.whitelist()
def get_deliverables(milestone_id: str) -> list[dict[str, str]]:
	"""
	Fetch all deliverables, tasks, and subtasks for a given milestone (task_id).
	Uses the `parent_task` field to establish the hierarchy.
	"""
	try:
		filters = {"parent_task": milestone_id, "type": "Deliverables"}
		fields = ["idx", "name", "subject as label", "color"]
		# Fetch all deliverables linked to the milestone (parent_task = milestone_id)
		order_by = "idx"
		deliverables = fetch_tasks(filters=filters, fields=fields, order_by=order_by)

		response = []
		for deliverable in deliverables:
			tasks = get_tasks_for_deliverable(deliverable.name)
			response.append(
				{
					"idx": deliverable.idx,
					"id": deliverable.name,
					"name": deliverable.label,
					"color": deliverable.color,
					"isExpanded": False,
					"isEditing": False,
					"tasks": tasks,
				}
			)

		return response

	except Exception:
		frappe.log_error(frappe.get_traceback(), _("Deliverables API Error"))
		frappe.throw(_("An error occurred while fetching deliverables."))


def get_tasks_for_deliverable(deliverable_id: str) -> list:
	"""
	Fetch tasks linked to a deliverable (parent_task = deliverable_id).
	"""
	tasks = get_child_tasks_using_task_type(deliverable_id, "Task")

	task_details = []
	for task in tasks:
		subtasks = get_child_tasks_using_task_type(task["id"], "Sub Task")
		task_details.append(
			{
				**task,
				"subtasks": subtasks,
			}
		)

	return task_details


def get_child_tasks_using_task_type(task_id, task_type):
	# filters = {"parent_task": task_id, "type": task_type}
	# fields = [
	#     f"`tab{TASK_DOCTYPE}`.name",
	#     "subject",
	#     "status",
	#     "planned_end_date as dueDate",
	#     f"GROUP_CONCAT(JSON_OBJECT(id, `tab{ASSIGNEE_DOCTYPE}`.user)) as assignees",
	# ]
	# group_by = "name"
	# subtasks = fetch_tasks(filters=filters, fields=fields, group_by=group_by)

	final_query = f"""
        SELECT
            `tab{TASK_DOCTYPE}`.idx,
            `tab{TASK_DOCTYPE}`.name,
            `tab{TASK_DOCTYPE}`.subject,
            `tab{TASK_DOCTYPE}`.status,
            DATE_FORMAT(`tab{TASK_DOCTYPE}`.planned_start_date, '%Y-%m-%d') AS planned_start_date,
			DATE_FORMAT(`tab{TASK_DOCTYPE}`.planned_end_date, '%Y-%m-%d') AS planned_end_date,
			DATE_FORMAT(`tab{TASK_DOCTYPE}`.actual_start_date, '%Y-%m-%d') AS actual_start_date,
			DATE_FORMAT(`tab{TASK_DOCTYPE}`.actual_end_date, '%Y-%m-%d') AS actual_end_date,

            `tab{TASK_DOCTYPE}`.remarks,
            JSON_ARRAYAGG(
                JSON_OBJECT(
                    'value', `tab{ASSIGNEE_DOCTYPE}`.user,
                    'label', `tab{ASSIGNEE_DOCTYPE}`.user_name
                )
                ORDER BY `tab{ASSIGNEE_DOCTYPE}`.idx
            ) AS assignees
        FROM
            `tab{TASK_DOCTYPE}`
        LEFT JOIN
            `tab{ASSIGNEE_DOCTYPE}`
        ON
            `tab{TASK_DOCTYPE}`.name = `tab{ASSIGNEE_DOCTYPE}`.parent
        WHERE
            `tab{TASK_DOCTYPE}`.parent_task = '{task_id}'
            AND `tab{TASK_DOCTYPE}`.type = '{task_type}'
        GROUP BY
            `tab{TASK_DOCTYPE}`.name
        ORDER BY `tab{TASK_DOCTYPE}`.idx
    """

	subtasks = frappe.db.sql(final_query, as_dict=True)

	subtask_details = []
	for subtask in subtasks:
		subtask_details.append(
			{
				"idx": subtask.idx,
				"id": subtask.name,
				"name": subtask.subject,
				"status": subtask.status,
				"isExpanded": False,
				"planned_start_date": (subtask.planned_start_date),
				"planned_end_date": (subtask.planned_end_date),
				"actual_start_date": (subtask.actual_start_date),
				"actual_end_date": (subtask.actual_end_date),
				"assignees": split_assignees(subtask.assignees),
				"remarks": subtask.remarks,
			}
		)

	return subtask_details


@frappe.whitelist()
def update_task_idx(tasks):
	for task in tasks:
		frappe.db.set_value(TASK_DOCTYPE, task["name"], "idx", task["idx"])


@frappe.whitelist()
def delete_tasks_with_children(task_ids, deleted_tasks=None):
	try:
		if deleted_tasks is None:
			deleted_tasks = set()
		for task_id in task_ids:
			if task_id in deleted_tasks:
				pass

			try:
				task = frappe.get_doc(TASK_DOCTYPE, task_id)
			except frappe.exceptions.DoesNotExistError:
				continue

			# Collect child tasks
			task_list = [child_task.task for child_task in task.depends_on]

			# Clear dependencies and save the parent task
			task.depends_on = []
			if task.parent_task:
				parent_task = frappe.get_doc(TASK_DOCTYPE, task.parent_task)
				for child_task in parent_task.depends_on:
					if child_task.task == task_id:
						parent_task.remove(child_task)
						break
				parent_task.save()
				task.parent_task = None
			task.save()

			# Recursively call for each child task
			delete_tasks_with_children(task_list, deleted_tasks)

			frappe.delete_doc(TASK_DOCTYPE, task_id)
			deleted_tasks.add(task_id)
			# task.delete()
		return "Task deleted successfully."
	except Exception:
		frappe.log_error(frappe.get_traceback(), _("Task Deletion Error"))
		frappe.throw(_("An error occurred while deleting the task."))


@frappe.whitelist()
def duplicate_tasks_with_children(task_ids, parent_task=None):
	"""
	Function to duplicate a task and its children
	"""
	try:
		for task_id in task_ids:
			task = frappe.get_doc(TASK_DOCTYPE, task_id)
			new_task = frappe.copy_doc(task)
			new_task.idx = None
			new_task.name = None
			# new_task.status = "Open"
			new_task.depends_on = []
			if parent_task:
				new_task.parent_task = parent_task
			new_task.planned_start_date = None
			new_task.planned_end_date = None
			new_task.actual_start_date = None
			new_task.actual_end_date = None
			new_task.subject = f"{task.subject} (Copy)"
			new_task.save()

			# Duplicate child tasks
			task_list = [child_task.task for child_task in task.depends_on]
			duplicate_tasks_with_children(task_list, parent_task=new_task.name)

		return "Task duplicated successfully."
	except Exception:
		frappe.log_error(frappe.get_traceback(), _("Task Duplication Error"))
		frappe.throw(_("An error occurred while duplicating the task."))


@frappe.whitelist()
def move_tasks(tasks):
	for task in tasks:
		parent_task_id = frappe.db.get_value(TASK_DOCTYPE, task["docname"], "parent_task")
		parent_task = frappe.get_doc(TASK_DOCTYPE, parent_task_id)
		for child_task in parent_task.depends_on:
			if child_task.task == task["docname"]:
				parent_task.remove(child_task)
				break
		parent_task.save()

		task_doc = frappe.get_doc(TASK_DOCTYPE, task["docname"])
		task_doc.parent_task = task["parent_task"]
		task_doc.save()
