import frappe
from frappe.tests.utils import FrappeTestCase

from inspira.inspira.api.projects.project_planner import (
	fetch_tasks,
	format_date,
	get_deliverables,
	get_milestones,
	split_assignees,
)

TASK_DOCTYPE = "IDP Task"
ASSIGNEE_DOCTYPE = "IDP Task Assignee"
PROJECT_DOCTYPE = "IDP Project"
USER_DOCTYPE = "User"


class TestProjectPlanner(FrappeTestCase):
	@classmethod
	def setUpClass(cls):
		super().setUpClass()

		# Create test project
		cls.project = frappe.get_doc(
			{
				"doctype": PROJECT_DOCTYPE,
				"project_name": "Test Project",
			}
		).insert(ignore_permissions=True)

		# Create test users
		cls.user_names = [
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
		]
		for user in cls.user_names:
			frappe.get_doc(
				{
					"doctype": USER_DOCTYPE,
					"email": user,
					"first_name": user,
					"send_welcome_email": 0,
					"thread_notify": 0,
					"role_profile_name": "Sales",
				}
			).insert(ignore_permissions=True)

		# Create test hierarchy
		cls.create_tasks_hierarchy()

	@classmethod
	def tearDownClass(cls):
		# Cleanup all test data
		frappe.db.delete(TASK_DOCTYPE, {"project": cls.project.name})
		frappe.db.delete(PROJECT_DOCTYPE, {"name": cls.project.name})
		for user in cls.user_names:
			frappe.db.delete(USER_DOCTYPE, user)

		super().tearDownClass()

	@classmethod
	def create_tasks_hierarchy(cls):
		"""Create the complete task hierarchy once"""
		# Milestones
		cls.milestone1 = cls.create_task("Milestone 1", "Milestone", cls.project.name, is_milestone=1)
		cls.milestone2 = cls.create_task("Milestone 2", "Milestone", cls.project.name, is_milestone=1)

		# Deliverables
		cls.deliverable1 = cls.create_task(
			"Deliverable 1",
			"Deliverables",
			cls.project.name,
			parent_task=cls.milestone1.name,
		)
		cls.deliverable2 = cls.create_task(
			"Deliverable 2",
			"Deliverables",
			cls.project.name,
			parent_task=cls.milestone1.name,
		)

		# Tasks
		cls.task1 = cls.create_task(
			"Task 1",
			"Task",
			cls.project.name,
			parent_task=cls.deliverable1.name,
			assignees=[cls.user_names[0], cls.user_names[1]],
		)
		cls.task2 = cls.create_task("Task 2", "Task", cls.project.name, parent_task=cls.deliverable1.name)

		# Subtasks
		cls.subtask1 = cls.create_task(
			"Subtask 1",
			"Sub Task",
			cls.project.name,
			parent_task=cls.task1.name,
			assignees=[cls.user_names[2]],
		)

	@classmethod
	def create_task(
		cls,
		subject,
		task_type,
		project_id,
		is_milestone=0,
		parent_task=None,
		assignees=None,
	):
		"""Class method to create tasks with assignees"""
		task = frappe.get_doc(
			{
				"doctype": TASK_DOCTYPE,
				"subject": subject,
				"type": task_type,
				"project": project_id,
				"is_milestone": is_milestone,
				"parent_task": parent_task,
			}
		).insert(ignore_permissions=True)

		if assignees:
			for user in assignees:
				frappe.get_doc(
					{
						"doctype": ASSIGNEE_DOCTYPE,
						"parent": task.name,
						"parenttype": TASK_DOCTYPE,
						"parentfield": "assignees",
						"user": user,
					}
				).insert(ignore_permissions=True)
		return task

	def test_get_milestones(self):
		result = get_milestones(project_id=self.project.name)
		self.assertEqual(len(result), 2)
		self.assertEqual(result[0]["label"], "Milestone 1")
		self.assertEqual(result[1]["label"], "Milestone 2")

	def test_get_deliverables(self):
		result = get_deliverables(milestone_id=self.milestone1.name)
		self.assertEqual(len(result), 2)
		self.assertEqual(result[0]["name"], "Deliverable 1")
		self.assertEqual(result[0]["tasks"][0]["name"], "Task 1")
		self.assertEqual(result[0]["tasks"][0]["subtasks"][0]["name"], "Subtask 1")
		self.assertEqual(result[0]["tasks"][0]["subtasks"][0]["assignees"], [self.user_names[2]])

	def test_split_assignees(self):
		self.assertEqual(split_assignees("User 1,User 2"), ["User 1", "User 2"])
		self.assertEqual(split_assignees(None), [])

	def test_format_date(self):
		self.assertEqual(format_date("2024-03-04"), "04-03-2024")
		self.assertEqual(format_date(None), "Not set")

	def test_fetch_tasks(self):
		filters = {"type": "Milestone", "project": self.project.name}
		fields = ["name", "subject"]
		tasks = fetch_tasks(filters, fields)
		self.assertEqual(len(tasks), 2)
		self.assertEqual(tasks[0]["subject"], "Milestone 1")
