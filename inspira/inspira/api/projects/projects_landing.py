import frappe
from frappe import _
from frappe.utils import format_date

@frappe.whitelist()
def get_projects_data(filters=None):
    """
    Get projects data for the landing page
    Args:
        filters: Optional filters for projects
    Returns:
        dict: Projects data for the landing page
    """
    # Parse filters if provided
    filter_conditions = {}
    if filters:
        try:
            filter_conditions = frappe.parse_json(filters)
        except Exception:
            frappe.log_error(frappe.get_traceback(), _("Error parsing filters"))
    
    # Get projects
    projects_data = get_projects(filter_conditions)
    
    # Get tabs count
    tabs_data = get_tabs_count()
    
    return {
        "projects": projects_data,
        "tabs": tabs_data
    }

def get_projects(filters=None):
    """
    Get projects list with relevant details
    Args:
        filters: Filters to apply
    Returns:
        list: List of projects with details
    """
    if not filters:
        filters = {}
    
    # Base filters - only show active projects by default
    base_filters = {"status": ["!=", "Cancelled"]}
    
    # Apply status filter if provided
    if "status" in filters:
        if filters["status"]:
            base_filters["status"] = filters["status"]
    
    # Apply search filter if provided
    search_query = filters.get("search", "")
    search_condition = ""
    if search_query:
        search_condition = """
            AND (
                p.project_name LIKE %(search1)s 
                OR p.name LIKE %(search2)s
            )
        """
    
    # Apply member filter if provided
    member_condition = ""
    if filters.get("member"):
        member_condition = "AND pu.user = %(member)s"

    # Apply user filter
    if "System Manager" not in frappe.get_roles():
        member_condition = f"AND pu.user = '{frappe.session.user}'"
    
    # Build query
    query = f"""
        SELECT 
            p.name as id,
            p.project_name as name,
            p.status,
            ROUND(p.percent_complete, 2) as progress,
            p.expected_start_date as start_date,
            p.expected_end_date as end_date,
            DATEDIFF(p.expected_end_date, CURDATE()) as days_left,
            (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'value', apu.email,
                        'label', apu.full_name
                    )
                    ORDER BY apu.idx
                )
                FROM `tabIDP Project User` apu
                WHERE apu.parent = p.name
            ) AS assignees
        FROM 
            `tabIDP Project` p
        LEFT JOIN
            `tabIDP Project User` pu ON pu.parent = p.name
        WHERE 
            p.status = %(status)s
            AND (p.contract IS NOT NULL AND p.contract != '')
            {search_condition}
            {member_condition}
        GROUP BY 
            p.name
        ORDER BY 
            p.modified DESC
    """
    
    # Prepare parameters
    params = {
        "status": filters.get("status", "Open")
    }
    
    # Add search parameters if needed
    if search_query:
        params["search1"] = f"%{search_query}%"
        params["search2"] = f"%{search_query}%"
    
    # Add member parameter if needed
    if filters.get("member"):
        params["member"] = filters["member"]

    projects = frappe.db.sql(query, params, as_dict=True)
    
    # Process projects to add additional data
    for project in projects:
        # Format dates
        project["start_date"] = format_date(project["start_date"]) if project["start_date"] else ""
        project["end_date"] = format_date(project["end_date"]) if project["end_date"] else ""
        
        # Get project members
        project["assignees"] = frappe.parse_json(project["assignees"])
        
        # Calculate days left
        if project["days_left"] is None:
            project["days_left"] = 0
        elif project["days_left"] < 0:
            project["days_left"] = 0
    
    return projects

def get_tabs_count():
    """
    Get count of projects by status for tabs
    Returns:
        list: List of tabs with counts
    """
    counts = []
    if "System Manager" in frappe.get_roles():
        counts = frappe.db.sql("""
            SELECT 
                status, COUNT(*) as count
            FROM 
                `tabIDP Project` p
            WHERE 
                status IN ('Open', 'Completed', 'On Hold', 'Cancelled')
                AND (p.contract IS NOT NULL AND p.contract != '')
            GROUP BY 
                status
        """, as_dict=True)
    else:
        counts = frappe.db.sql("""
            SELECT 
                status, COUNT(*) as count
            FROM 
                `tabIDP Project` p
            LEFT JOIN
                `tabIDP Project User` pu ON pu.parent = p.name
            WHERE 
                status IN ('Open', 'Completed', 'On Hold', 'Cancelled')
                AND (p.contract IS NOT NULL AND p.contract != '')
                AND pu.user = %(user)s
            GROUP BY 
                status
        """, {"user": frappe.session.user}, as_dict=True)
        
    # Create tabs with counts
    tabs = [
        {"name": "Active", "count": 0},
        {"name": "Complete", "count": 0},
        {"name": "On hold", "count": 0},
        {"name": "Dropped", "count": 0}
    ]
    
    # Map status to tab name
    status_map = {
        "Open": "Active",
        "Completed": "Complete",
        "On Hold": "On hold",
        "Cancelled": "Dropped"
    }
    
    # Update counts
    for count in counts:
        tab_name = status_map.get(count.status)
        if tab_name:
            for tab in tabs:
                if tab["name"] == tab_name:
                    tab["count"] = count.count
                    break
    
    return tabs

@frappe.whitelist()
def get_milestone_project_data():
    user = frappe.session.user
    today = frappe.utils.getdate(frappe.utils.nowdate())
    reports_to = frappe.db.get_value("Employee",{'user_id':user},"name")
    team = frappe.db.sql(f"""
        SELECT
            emp.user_id
        FROM `tabEmployee` AS emp
        WHERE emp.reports_to = %s
    """,reports_to,as_dict=True)
    all_users = [user] + [u["user_id"] for u in team if u.get("user_id")]
    placeholders = ','.join(['%s'] * len(all_users))
    data = frappe.db.sql("""
        SELECT
            p.project_name AS project,
            p.expected_start_date AS startDate,
            m.contract_end_date AS endDate,
            m.milestone,
            DATEDIFF(m.contract_end_date, %s) AS daysLeft,
            ROUND(m.completion_, 2) AS progress
        FROM `tabIDP Project` AS p
        JOIN `tabIDP Contract` AS c ON p.contract = c.name
        JOIN `tabIDP Milestones` AS m ON m.parent = c.name
        JOIN `tabIDP Project User` AS u ON u.parent = p.name
        WHERE u.user = %s
            AND m.contract_end_date >= %s
            AND m.completion_ < 100
        ORDER BY m.contract_end_date ASC
    """, (today,user, today), as_dict=True)
    
    team_query = f"""
        SELECT DISTINCT
            p.project_name AS name,
            p.expected_start_date AS startDate,
            m.contract_end_date AS endDate,
            m.milestone,
            DATEDIFF( %s,m.contract_end_date) AS daysDelayed,
            u.user,
            ROUND(m.completion_, 2) AS progress
        FROM `tabIDP Project` AS p
        JOIN `tabIDP Contract` AS c ON p.contract = c.name
        JOIN `tabIDP Milestones` AS m ON m.parent = c.name
        JOIN `tabIDP Project User` AS u ON u.parent = p.name
        WHERE u.user IN ({placeholders})
            AND m.contract_end_date <= %s
            AND m.completion_ < 100
        GROUP BY p.project_name, m.contract_end_date,m.milestone
        ORDER BY m.contract_end_date ASC
    """

    team_args = [today] + all_users + [today]
    team_data = frappe.db.sql(team_query, team_args, as_dict=True)
    

    # Fetch unique projects for status summary
    unique_projects = frappe.db.sql("""
        SELECT DISTINCT
            p.name AS project,
            p.expected_end_date as endDate
        FROM `tabIDP Project` AS p
        JOIN `tabIDP Project User` AS u ON u.parent = p.name
        WHERE u.user = %s
    """, (user,), as_dict=True)

    delayed_projects = 0
    on_track_projects = 0
    for proj in unique_projects:
        if frappe.utils.getdate(proj['endDate']) < today:
            delayed_projects += 1
        else:
            on_track_projects += 1

    total_projects = delayed_projects + on_track_projects
    delayed_percentage = round((delayed_projects / total_projects) * 100) if total_projects else 0
    on_track_percentage = 100 - delayed_percentage if total_projects else 0
    
    return {
    "projects": data,
    "stats": {
        "delayedProjects": delayed_projects,
        "onTrackProjects": on_track_projects,
        "delayedPercentage": delayed_percentage,
        "onTrackPercentage": on_track_percentage
    },
    "team_data":team_data
}
