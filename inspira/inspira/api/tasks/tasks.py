import frappe

from inspira.inspira.api.hrms.hr_utils import get_reportees
from frappe.utils import getdate, nowdate, formatdate


@frappe.whitelist()
# def get_tasks(user_id=None, user_ids=None):
#     base_query = f"""
#         SELECT
#             task.name AS id,
#             task.subject,
#             task.group,
#             task.status,
#             status.status AS status,
#             task.priority,
#             DATE_FORMAT(task.planned_start_date, '%d-%b-%Y') AS planned_start_date,
#             DATE_FORMAT(task.planned_end_date, '%d-%b-%Y') AS planned_end_date,
#             DATE_FORMAT(task.actual_start_date, '%d-%b-%Y') AS actual_start_date,
#             DATE_FORMAT(task.actual_end_date, '%d-%b-%Y') AS actual_end_date,
#             task.project,
#             task.project_name AS project,
#             task.remarks,
#             JSON_ARRAYAGG(
#                 JSON_OBJECT(
#                     'value', all_assignees.user,
#                     'label', all_assignees.user_name
#                 )
#             ) AS assignees
#         FROM
#             `tabIDP Task` task
#         JOIN
#             `tabIDP Task Assignee` assignee
#         ON
#             task.name = assignee.parent
#         LEFT JOIN
#             `tabIDP Task Assignee` all_assignees
#         ON
#             task.name = all_assignees.parent
#         LEFT JOIN
#             `tabIDP Status Master` AS status
#         ON 
#             status.name = task.status
#     """
#     where_query = ""
#     if user_id:
#         where_query = f"WHERE assignee.user = '{user_id}'"
#     elif user_ids:
#         formatted_user_ids = ", ".join(f"'{user}'" for user in user_ids)
#         where_query = f"WHERE (assignee.user IN ({formatted_user_ids}) OR assignee.user IS NULL)"
#     query = base_query + where_query + """
#         GROUP BY
#             task.name
#         ORDER BY
#             task.creation
#     """
#     tasks = frappe.db.sql(query, as_dict=True)
#     for task in tasks:
#         task["assignees"] = frappe.parse_json(task["assignees"])
#     return tasks

def get_tasks(user_id=None, user_ids=None):
    base_query = f"""
        SELECT
            task.name AS id,
            task.subject,
            task.group,
            task.status,
            status.status AS status,
            status.color AS statusColor,
            task.priority,
            DATE_FORMAT(task.planned_start_date, '%Y-%m-%d') AS planned_start_date,
            DATE_FORMAT(task.planned_end_date, '%Y-%m-%d') AS planned_end_date,
            DATE_FORMAT(task.actual_start_date, '%Y-%m-%d') AS actual_start_date,
            DATE_FORMAT(task.actual_end_date, '%Y-%m-%d') AS actual_end_date,
            task.project AS projectID,
            task.project_name AS project,
            task.remarks,
            (
                SELECT JSON_ARRAYAGG(
                    JSON_OBJECT(
                        'value', a.user,
                        'label', a.user_name
                    )
                )
                FROM `tabIDP Task Assignee` a
                WHERE a.parent = task.name
            ) AS assignees
        FROM
            `tabIDP Task` task
        LEFT JOIN
            `tabIDP Task Assignee` assignee
        ON
            task.name = assignee.parent
        LEFT JOIN
            `tabIDP Task Assignee` all_assignees
        ON
            task.name = all_assignees.parent
        LEFT JOIN
            `tabIDP Status Master` AS status
        ON 
            status.name = task.status
    """

    where_query = ""
    if user_id:
        where_query = f"WHERE assignee.user = '{user_id}'"
    elif user_ids:
        formatted_user_ids = ", ".join(f"'{user}'" for user in user_ids)
        formatted_creations = ", ".join(f"'{user}'" for user in user_ids + [frappe.session.user])
        where_query = f"""
            WHERE task.group != 'Personal'
            # OR task.owner IN ({formatted_creations})
            # OR (
            #     EXISTS (
            #         SELECT 1 FROM `tabIDP Task Assignee` assignee
            #         WHERE assignee.parent = task.name AND assignee.user IN ({formatted_user_ids})
            #     )
            #     # OR NOT EXISTS (
            #     #     SELECT 1 FROM `tabIDP Task Assignee` assignee
            #     #     WHERE assignee.parent = task.name
            #     # )
            # )
            AND (
                EXISTS (
                    SELECT 1 FROM `tabIDP Task Assignee` assignee
                    WHERE assignee.parent = task.name AND assignee.user IN ({formatted_user_ids})
                )
                OR (
                    NOT EXISTS (
                        SELECT 1 FROM `tabIDP Task Assignee` assignee
                        WHERE assignee.parent = task.name
                    )
                    AND task.owner IN ({formatted_creations})
                )
            )
            
        """

    query = base_query + where_query + """
        GROUP BY
            task.name
        ORDER BY
            task.creation
    """
    tasks = frappe.db.sql(query, as_dict=True)
    for task in tasks:
        task["assignees"] = frappe.parse_json(task["assignees"])
    return tasks


@frappe.whitelist()
def get_all_tasks():
    reportees = get_reportees(frappe.session.user)
    user_ids = [reportee.user_id for reportee in reportees]
    # user_ids.append(frappe.session.user)
    return get_tasks(user_ids=user_ids)

from datetime import timedelta

@frappe.whitelist()
def task_details():
    today = frappe.utils.getdate(frappe.utils.nowdate())
    task_data = get_tasks(user_id = frappe.session.user)
    start_of_week = today - timedelta(days=today.weekday())  # Monday
    end_of_week = start_of_week + timedelta(days=6)          # Sunday
    past_due = 0
    this_week = 0
    due_later = 0
    no_due_date = 0
    result = {
        "overdue": [],
        "todo": []
    }
    for task in task_data:
        task_obj = {
            "name": task.subject,
            "group": task.group,
            "project": task.project or "-",
            "dueDate": formatdate(task.planned_end_date, "dd MMM yyyy") if task.planned_end_date else "-",
            "taskStatus": task.status,
            "statusColor": task.statusColor
        }

        if task.planned_end_date and getdate(task.planned_end_date) < today:
            result["overdue"].append(task_obj)
        elif "to do" in task.status.lower() or "in progress" in task.status.lower():
            result["todo"].append(task_obj)

        due_date = task.planned_end_date
        if not due_date:
            no_due_date += 1
        else:
            due_date = getdate(due_date)
            if due_date < today:
                past_due += 1
            elif start_of_week <= due_date <= end_of_week:
                this_week += 1
            elif due_date > end_of_week:
                due_later += 1

    taskStatus = {
        "pastDue": past_due,
        "thisWeek": this_week,
        "dueLater": due_later,
        "noDueDate": no_due_date
    }

    return result ,taskStatus