import frappe
from frappe.utils import get_datetime
from datetime import datetime

@frappe.whitelist()
def get_attendance_requests_reporting_to_user():
    import frappe
    from datetime import datetime

    session_user = frappe.session.user
    employee_id = frappe.db.get_value("Employee", {"user_id": session_user}, "name")
    if not employee_id:
        return []

    def format_time(dt):
        if not dt:
            return "--:--"
        try:
            dt_obj = datetime.strptime(str(dt), "%Y-%m-%d %H:%M:%S.%f")
        except ValueError:
            dt_obj = datetime.strptime(str(dt), "%Y-%m-%d %H:%M:%S")
        return dt_obj.strftime("%I:%M %p")

    results = []

    # Attendance Request Query
    attendance_query = """
        SELECT
            ar.name AS id,
            e.name AS employee_id,
            ar.from_date AS date,
            CONCAT('(', DATEDIFF(ar.to_date, ar.from_date) + 1, ' day)') AS duration,
            IFNULL(ar.duty_type, 'Regularization') AS type,
            ar.creation AS requested_on,
            ar.reason,
            ar.docstatus,
            e.default_shift,
            ar.explanation,
            (SELECT MIN(ci.time)
            FROM `tabEmployee Checkin` ci
            WHERE ci.employee = e.name
            AND DATE(ci.time) = ar.from_date
            AND ci.log_type = 'IN'
            ) AS first_checkin,
            (SELECT MAX(ci.time)
            FROM `tabEmployee Checkin` ci
            WHERE ci.employee = e.name
            AND DATE(ci.time) = ar.from_date
            AND ci.log_type = 'OUT'
            ) AS last_checkout,
            'Attendance Request' as doctype
        FROM `tabAttendance Request` ar 
        JOIN `tabEmployee` e ON e.name = ar.employee
        WHERE ar.workflow_state = 'Draft' AND e.reports_to = %s
    """
    attendance_results = frappe.db.sql(attendance_query, (employee_id,), as_dict=True)

    for row in attendance_results:
        date_obj = datetime.strptime(str(row.date), "%Y-%m-%d")
        requested_on_obj = datetime.strptime(str(row.requested_on), "%Y-%m-%d %H:%M:%S.%f")

        results.append({
            "doctype": row.doctype,
            "id": row.id,
            "employeeId": row.employee_id,
            "date": date_obj.strftime("%b %d, %Y"),
            "duration": row.duration,
            "type": row.type,
            "requestedOn": requested_on_obj.strftime("%b %d, %Y"),
            "reason": row.explanation,
            "status": "Pending" if row.docstatus == 0 else "Approved" if row.docstatus == 1 else "Rejected",
            "details": {
                "shift": row.default_shift or "",
                "note": row.explanation or "",
                "checkInTime": format_time(row.first_checkin),
                "checkOutTime": format_time(row.last_checkout),
                "adjustedCheckIn": format_time(row.first_checkin),
                "adjustedCheckOut": format_time(row.last_checkout),
            }
        })

    # IDP Outdoor Duty Request Query
    idp_query = """
        SELECT
            idr.name AS id,
            e.name AS employee_id,
            idr.date AS date,
            NULL AS duration,
            idr.duty_type AS type,
            idr.creation AS requested_on,
            idr.reason,
            idr.docstatus,
            idr.duty_type as default_shift,
            idr.reason,
            (SELECT MIN(ci.time)
            FROM `tabEmployee Checkin` ci
            WHERE ci.employee = e.name
            AND DATE(ci.time) = idr.date
            AND ci.log_type = 'IN'
            ) AS first_checkin,
            (SELECT MAX(ci.time)
            FROM `tabEmployee Checkin` ci
            WHERE ci.employee = e.name
            AND DATE(ci.time) = idr.date
            AND ci.log_type = 'OUT'
            ) AS last_checkout,
            idr.in_time as first_checkin,
            idr.out_time as last_checkout,
            idr.project,
            'IDP Outdoor Duty Request' as doctype
        FROM `tabIDP Outdoor Duty Request` idr
        JOIN `tabEmployee` e ON e.name = idr.employee
        WHERE idr.workflow_state = 'Draft' AND e.reports_to = %s
    """
    idp_results = frappe.db.sql(idp_query, (employee_id,), as_dict=True)

    for row in idp_results:
        date_obj = datetime.strptime(str(row.date), "%Y-%m-%d")
        requested_on_obj = datetime.strptime(str(row.requested_on), "%Y-%m-%d %H:%M:%S.%f")

        results.append({
            "doctype": row.doctype,
            "id": row.id,
            "employeeId": row.employee_id,
            "date": date_obj.strftime("%b %d, %Y"),
            "duration": row.duration or "(1 day)",
            "type": row.type or "Outdoor Duty",
            "requestedOn": requested_on_obj.strftime("%b %d, %Y"),
            "reason": row.explanation,
            "status": "Pending" if row.docstatus == 0 else "Approved" if row.docstatus == 1 else "Rejected",
            "details": {
                "shift": row.default_shift or "",
                "note": row.explanation or "",
                "checkInTime": (row.first_checkin),
                "checkOutTime": (row.last_checkout),
                "adjustedCheckIn": (row.first_checkin),
                "adjustedCheckOut": (row.last_checkout),
            }
        })

    # Optionally, sort combined results by date or requested_on descending
    results.sort(key=lambda r: datetime.strptime(r["requestedOn"], "%b %d, %Y"), reverse=True)

    return results

@frappe.whitelist()
def get_employees_reporting_to_user():
    session_user = frappe.session.user

    # Get the employee ID of the session user
    manager_id = frappe.db.get_value("Employee", {"user_id": session_user}, "name")
    if not manager_id:
        return []

    # SQL to fetch employee data
    query = """
        SELECT
            e.name AS id,
            e.employee_name AS name,
            e.designation AS role
        FROM `tabEmployee` e
        WHERE e.reports_to = %s
    """

    employees = frappe.db.sql(query, (manager_id,), as_dict=True)

    # Format and add "initial"
    formatted = []
    for emp in employees:
        initial = emp.name[0].upper() if emp.name else ""
        formatted.append({
            "id": emp.id,
            "name": emp.name,
            "role": emp.role,
            "initial": initial
        })

    return formatted


@frappe.whitelist()
def get_shift_data_for_reportees():
    import json
    from frappe.utils import format_time
    from collections import defaultdict

    user = frappe.session.user
    manager_emp_id = frappe.db.get_value("Employee", {"user_id": user}, "name")
    if not manager_emp_id:
        return {}

    employees = frappe.db.sql("""
        SELECT e.name AS employee_id, e.employee_name, e.default_shift
        FROM `tabEmployee` e
        WHERE e.reports_to = %s
    """, (manager_emp_id,), as_dict=True)

    result = defaultdict(list)

    for emp in employees:
        emp_id = emp.employee_id

        # Get check-in data
        checkins = frappe.db.sql("""
            SELECT 
                DATE(time) AS date,
                MIN(time) AS first_in,
                MAX(time) AS last_out,
                shift
            FROM `tabEmployee Checkin`
            WHERE employee = %s
            GROUP BY DATE(time)
        """, (emp_id,), as_dict=True)
        checkin_map = {str(row["date"]): row for row in checkins}

        # Get leave and present attendance data
        attendance = frappe.db.sql("""
            SELECT
                attendance_date AS date,
                status,
                leave_type
            FROM `tabAttendance`
            WHERE employee = %s AND docstatus = 1
        """, (emp_id,), as_dict=True)

        attendance_map = {}
        for row in attendance:
            date_str = str(row["date"])
            attendance_map[date_str] = {
                "status": row["status"],
                "leave_type": row.get("leave_type")
            }

        all_dates = set(checkin_map.keys()).union(attendance_map.keys())

        for date in sorted(all_dates):
            entry = {"date": date}

            if date in checkin_map:
                row = checkin_map[date]
                entry["type"] = row.get("shift") or "CHECKIN"
                entry["time"] = f"{format_time(row['first_in'])} - {format_time(row['last_out'])}"

            elif date in attendance_map:
                att = attendance_map[date]
                # Directly reflect the actual status
                status = att["status"]
                if status == "On Leave":
                    entry["type"] = att["leave_type"] or "On Leave"
                else:
                    entry["type"] = status  # Can be "Half Day", "Absent", etc.
                entry["time"] = ""

            result[emp_id].append(entry)

    return result
# API for Attenance Page
# file: custom_app/api/employee_attendance.py
# @frappe.whitelist()
# def get_all_employee_attendance():
#     from frappe.utils import getdate, formatdate, flt
#     from collections import defaultdict
#     employee = frappe.db.get_value("Employee",{'User_id':frappe.session.user},"name")
#     attendance = frappe.get_all("Attendance", filters={"employee": employee}, fields=[
#         "attendance_date", "status", "working_hours"
#     ])
    
#     checkins = frappe.get_all("Employee Checkin", filters={"employee": employee}, fields=[
#         "time", "log_type"
#     ], order_by="time asc")

#     # Group check-ins by date
#     day_data = defaultdict(lambda: {"checkIn": "", "checkOut": "", "hours": 0, "onTimeStatus": "", "status": ""})

#     for a in attendance:
#         day = str(a.attendance_date)
#         day_data[day]["status"] = a.status.lower()  # present/absent/holiday
#         day_data[day]["hours"] = flt(a.working_hours)

#     for c in checkins:
#         date_str = str(getdate(c.time))
#         time_str = c.time.strftime("%H:%M")
#         if c.log_type == "IN" and not day_data[date_str]["checkIn"]:
#             day_data[date_str]["checkIn"] = time_str
#         elif c.log_type == "OUT":
#             day_data[date_str]["checkOut"] = time_str

#     # Assume 09:00 as standard check-in for "On Time" calculation
#     for day, data in day_data.items():
#         if data["checkIn"]:
#             if data["checkIn"] <= "09:10":
#                 data["onTimeStatus"] = "On Time"
#             else:
#                 data["onTimeStatus"] = "Late"

#     # Organize by month
#     result = defaultdict(lambda: {"month": "", "days": []})
#     for day in sorted(day_data.keys()):
#         dt = getdate(day)
#         key = dt.strftime("%B %Y")  # e.g., March 2025
#         result[key]["month"] = key
#         result[key]["days"].append({
#             "date": day,
#             "checkIn": day_data[day]["checkIn"],
#             "checkOut": day_data[day]["checkOut"],
#             "hours": day_data[day]["hours"],
#             "onTimeStatus": day_data[day]["onTimeStatus"],
#             "status": day_data[day]["status"]
#         })

#     return list(result.values())  # array of month-wise grouped data

# @frappe.whitelist()
# def get_all_employee_attendance():
#     from frappe.utils import getdate
#     from collections import defaultdict
#     from datetime import datetime

#     employee = frappe.db.get_value("Employee", {'user_id': frappe.session.user}, "name")
#     today = getdate()

#     # Step 1: Get employee's holiday list
#     holiday_list = frappe.db.get_value("Employee", employee, "holiday_list")

#     holidays = frappe.db.sql(f"""
#         SELECT holiday_date
#         FROM `tabHoliday`
#         WHERE parent = %s
#     """, (holiday_list,), as_dict=True)
#     holiday_dates = {str(h.holiday_date) for h in holidays}

#     # Step 2: Attendance records
#     attendance = frappe.db.sql(f"""
#         SELECT
#             a.attendance_date,
#             a.status,
#             a.late_entry,
#             a.early_exit,
#             a.leave_type,
#             a.working_hours
#         FROM `tabAttendance` a
#         WHERE a.employee = %s
#     """, (employee,), as_dict=True)

#     # Step 3: Checkins
#     checkins = frappe.db.sql(f"""
#         SELECT
#             DATE(time) AS log_date,
#             TIME(time) AS log_time,
#             log_type
#         FROM `tabEmployee Checkin`
#         WHERE employee = %s
#         ORDER BY time ASC
#     """, (employee,), as_dict=True)

#     # Step 4: Map check-in and check-out
#     checkin_map = defaultdict(lambda: {"checkIn": "", "checkOut": ""})
#     for c in checkins:
#         date_str = str(c["log_date"])
#         time_str = str(c["log_time"])[:5]
#         if c["log_type"] == "IN" and not checkin_map[date_str]["checkIn"]:
#             checkin_map[date_str]["checkIn"] = time_str
#         elif c["log_type"] == "OUT":
#             checkin_map[date_str]["checkOut"] = time_str

#     # Step 5: Build union of all relevant dates
#     all_dates = set()
#     all_dates.update(str(a.attendance_date) for a in attendance)
#     all_dates.update(checkin_map.keys())
#     all_dates.update(holiday_dates)

#     # Step 6: Construct day-wise data
#     day_data = defaultdict(lambda: {"checkIn": "", "checkOut": "", "onTimeStatus": "", "status": ""})
    
#     for a in attendance:
#         date_str = str(a.attendance_date)
#         status = a.status.lower()
#         if status in ["present", "half day", "work from home"]:
#             day_data[date_str]["status"] = "present"
#         elif status == "absent":
#             day_data[date_str]["status"] = "absent"
#         elif a.leave_type:
#             day_data[date_str]["status"] = "on leave"
#         else:
#             day_data[date_str]["status"] = "unknown"

#         # On-time logic
#         if a.late_entry:
#             day_data[date_str]["onTimeStatus"] = "Late Entry"
#         elif a.early_exit:
#             day_data[date_str]["onTimeStatus"] = "Early Exit"
#         elif status in ['present', 'half day', 'work from home']:
#             day_data[date_str]["onTimeStatus"] = "On Time"
#         else:
#             day_data[date_str]["onTimeStatus"] = ""

#     # Step 7: Add check-ins
#     for date_str, times in checkin_map.items():
#         day_data[date_str]["checkIn"] = times["checkIn"]
#         day_data[date_str]["checkOut"] = times["checkOut"]

#     # Step 8: Add holiday status where applicable
#     for date_str in all_dates:
#         if date_str not in day_data or not day_data[date_str]["status"]:
#             if date_str in holiday_dates:
#                 day_data[date_str]["status"] = "holiday"
#                 # Don't override checkIn/Out if already present
#                 if date_str in checkin_map:
#                     day_data[date_str]["checkIn"] = checkin_map[date_str]["checkIn"]
#                     day_data[date_str]["checkOut"] = checkin_map[date_str]["checkOut"]
#                 day_data[date_str]["onTimeStatus"] = ""

#     # Step 9: Group by month
#     result = defaultdict(lambda: {"month": "", "days": []})
#     for day in sorted(all_dates):
#         dt = getdate(day)
#         month_key = dt.strftime("%B %Y")
#         result[month_key]["month"] = month_key
#         result[month_key]["days"].append({
#             "date": day,
#             "checkIn": day_data[day]["checkIn"],
#             "checkOut": day_data[day]["checkOut"],
#             "status": day_data[day]["status"],
#             "onTimeStatus": day_data[day]["onTimeStatus"]
#         })

#     return list(result.values())

@frappe.whitelist()
def get_all_employee_attendance():
    from frappe.utils import getdate
    from collections import defaultdict
    from datetime import datetime

    employee = frappe.db.get_value("Employee", {'user_id': frappe.session.user}, "name")
    today = getdate()

    # Step 1: Get holiday list
    holiday_list = frappe.db.get_value("Employee", employee, "holiday_list")
    holidays = frappe.db.sql("""
        SELECT holiday_date
        FROM `tabHoliday`
        WHERE parent = %s
    """, (holiday_list,), as_dict=True)
    holiday_dates = {str(h.holiday_date) for h in holidays}

    # Step 2: Attendance records
    attendance = frappe.db.sql("""
        SELECT
            a.attendance_date,
            a.status,
            a.late_entry,
            a.early_exit,
            a.leave_type,
            a.working_hours
        FROM `tabAttendance` a
        WHERE a.employee = %s
        AND a.docstatus = 1
    """, (employee,), as_dict=True)

    # Step 3: Checkins
    checkins = frappe.db.sql("""
        SELECT
            DATE(time) AS log_date,
            TIME(time) AS log_time,
            log_type
        FROM `tabEmployee Checkin`
        WHERE employee = %s
        ORDER BY time ASC
    """, (employee,), as_dict=True)

    # Step 4: Checkin/out mapping
    checkin_map = defaultdict(lambda: {"checkIn": None, "checkOut": None})
    for c in checkins:
        date_str = str(c["log_date"])
        time_str = str(c["log_time"])[:5]
        if c["log_type"] == "IN" and not checkin_map[date_str]["checkIn"]:
            checkin_map[date_str]["checkIn"] = time_str
        elif c["log_type"] == "OUT":
            checkin_map[date_str]["checkOut"] = time_str

    # Step 5: Collect all dates
    all_dates = set()
    all_dates.update(str(a.attendance_date) for a in attendance)
    all_dates.update(checkin_map.keys())
    all_dates.update(holiday_dates)

    # Step 6: Compile day-wise data
    day_data = defaultdict(lambda: {
        "checkIn": None,
        "checkOut": None,
        "onTimeStatus": "",
        "status": "",
        "working_hours": 0
    })

    for a in attendance:
        date_str = str(a.attendance_date)
        status = a.status.lower()

        # Determine status
        if status in ["present", "half day", "work from home"]:
            day_data[date_str]["status"] = "present"
        elif status == "absent":
            day_data[date_str]["status"] = "absent"
        elif a.leave_type:
            day_data[date_str]["status"] = "on leave"
        else:
            day_data[date_str]["status"] = "unknown"

        # Determine onTimeStatus
        if a.late_entry:
            day_data[date_str]["onTimeStatus"] = "Late Entry"
        elif a.early_exit:
            day_data[date_str]["onTimeStatus"] = "Early Exit"
        elif status in ['present', 'half day', 'work from home']:
            day_data[date_str]["onTimeStatus"] = "On Time"
        elif status == 'on leave':
            day_data[date_str]["onTimeStatus"] = "Leave"
        elif status == 'absent':
            day_data[date_str]["onTimeStatus"] = "Absent"
        else:
            day_data[date_str]["onTimeStatus"] = ""

        day_data[date_str]["working_hours"] = float(a.working_hours or 0)

    # Step 7: Merge checkin data
    for date_str, times in checkin_map.items():
        day_data[date_str]["checkIn"] = times["checkIn"]
        day_data[date_str]["checkOut"] = times["checkOut"]

    # Step 8: Add holidays with checkins if any
    for date_str in all_dates:
        if not day_data[date_str]["status"]:
            if date_str in holiday_dates:
                day_data[date_str]["status"] = "holiday"
                day_data[date_str]["onTimeStatus"] = ""
                # Leave checkIn/out as None if not present

    # Step 9: Group by month
    result = defaultdict(lambda: {"month": "", "days": []})
    for day in sorted(all_dates):
        dt = getdate(day)
        month_key = dt.strftime("%B %Y")
        result[month_key]["month"] = month_key
        result[month_key]["days"].append({
            "date": day,
            "checkIn": day_data[day]["checkIn"],
            "checkOut": day_data[day]["checkOut"],
            "status": day_data[day]["status"],
            "onTimeStatus": day_data[day]["onTimeStatus"],
            "hours": round(day_data[day]["working_hours"])
        })

    return list(result.values())

# ********************************************************* Landing Page API *************************************************************************
import frappe
from frappe.utils import getdate, formatdate
from datetime import datetime
from collections import defaultdict

@frappe.whitelist()
def get_employee_attendance(employee_id=None):
    employee_id = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, "name")
    if not employee_id:
        frappe.throw("Employee ID is required")

    # Fetch all attendance records for the employee
    records = frappe.get_all("Attendance",
        filters={"employee": employee_id,"docstatus":1},
        fields=["attendance_date", "status"]
    )

    if not records:
        return []

    # Organize data by month
    attendance_by_month = defaultdict(lambda: {"days": [], "present_count": 0, "total_count": 0})

    for rec in records:
        date_obj = getdate(rec.attendance_date)
        month_key = date_obj.strftime("%B %Y")
        original_status = rec.status.lower()

        # Normalize status
        if original_status == "work from home":
            status = "present"
        elif original_status == "on leave":
            status = "absent"
        elif original_status == "half day":
            status = "half-day"
        else:
            status = original_status

        # Add the day entry
        attendance_by_month[month_key]["days"].append({
            "date": rec.attendance_date,
            "status": status
        })

        # Count for percentage
        if status in ["present", "half-day"]:
            attendance_by_month[month_key]["present_count"] += 1

        attendance_by_month[month_key]["total_count"] += 1

    # Format the final result
    result = []
    for month, data in attendance_by_month.items():
        percentage = 0
        if data["total_count"] > 0:
            percentage = round((data["present_count"] / data["total_count"]) * 100)

        result.append({
            "month": month,
            "days": data["days"],
            "percentage": percentage
        })

    # Sort by month (optional)
    result.sort(key=lambda x: datetime.strptime(x["month"], "%B %Y"))

    return result

import frappe
from frappe.utils import nowdate, get_datetime, get_first_day, get_time
from datetime import datetime, timedelta, time

@frappe.whitelist()
def get_employee_tracking_data():
    employee = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, "name")
    from_date = get_first_day(nowdate())
    to_date = nowdate()

    # Get employee shift hours
    shift_hours = 9  # Default
    hours = 8
    shift = frappe.db.get_value("Employee", employee, "default_shift")
    if shift:
        # Get shift times from Frappe
        start_time, end_time = frappe.db.get_value("Shift Type", shift, ["start_time", "end_time"])

        # If start_time or end_time is a timedelta, convert to time
        if isinstance(start_time, timedelta):
            start_time = (datetime.min + start_time).time()
        if isinstance(end_time, timedelta):
            end_time = (datetime.min + end_time).time()

        # Combine with today's date
        start_dt = datetime.combine(datetime.today(), start_time)
        end_dt = datetime.combine(datetime.today(), end_time)

        # Handle shifts that pass midnight
        if end_dt < start_dt:
            end_dt += timedelta(days=1)

        duration = end_dt - start_dt
        hours = duration.total_seconds() / 3600
    # Get attendance records of current month till date
    attendance_records = frappe.get_all("Attendance", 
        filters={"employee": employee, "attendance_date": ["between", [from_date, to_date]]},
        fields=["name", "attendance_date", "status", "late_entry","early_exit", "working_hours"]
    )

    # if not attendance_records:
    #     return {
    #         "avgHours": 0,
    #         "onTimeArrival": 0,
    #         "checkInTime": None or '-- : --',
    #         "checkOutTime": None or '-- : --',
    #         "status": "No Data"
    #     }

    total_working_hours = 0
    late_days = 0
    present_days = 0
    attendance_dates = set()

    for rec in attendance_records:
        if rec.status in ["Present","Half Day","Work From Home"]:
            present_days += 1
            total_working_hours += rec.working_hours or 0
            if rec.late_entry or rec.early_exit:
                # frappe.log_error("Late Entry / early exit")
                late_days += 1
            attendance_dates.add(rec.attendance_date)

    # Calculate average hours
    total_days = len(attendance_dates)
    avg_hours_percentage = (total_working_hours / (total_days * hours)) if total_days else 0
    # frappe.log_error(f"{total_days} - {late_days} / {total_days}")
    # On time arrival
    on_time_arrival = ((total_days - late_days) / total_days) * 100 if total_days else 0

    # Get today's first check-in and last check-out
    checkins = frappe.get_all("Employee Checkin", 
        filters={"employee": employee, "time": ["between", [nowdate() + " 00:00:00", nowdate() + " 23:59:59"]]},
        fields=["time", "log_type"],
        order_by="time asc"
    )

    check_in_time = None
    check_out_time = None
    if checkins:
        for log in checkins:
            if log["log_type"] == "IN" and not check_in_time:
                check_in_time = get_datetime(log["time"]).strftime('%I:%M %p')
            if log["log_type"] == "OUT":
                check_out_time = get_datetime(log["time"]).strftime('%I:%M %p')

    return {
        "avgHours": round(avg_hours_percentage, 1),
        "onTimeArrival": round(on_time_arrival, 1),
        "checkInTime": check_in_time or '-- : --',
        "checkOutTime": check_out_time or '-- : --',
        "status": "CHECKED IN" if check_out_time is None else "CHECKED OUT"
    }


@frappe.whitelist()
def get_checkin_data(employee, date):
    # Fetch all check-ins for the employee for the selected date
    records = frappe.db.sql("""
        SELECT ec.log_type, ec.time
        FROM `tabEmployee Checkin` AS ec
        WHERE ec.employee = %s AND DATE(ec.time) = %s
        ORDER BY ec.time
    """, (employee, date), as_dict=1)

    # Prepare structured data
    checkins = []
    checkouts = []

    for record in records:
        if record.log_type == 'IN':
            # checkins.append(record.time)
            checkins.append(frappe.utils.get_time(record.time).strftime('%H:%M'))
        elif record.log_type == 'OUT':
            # checkouts.append(record.time)
            checkouts.append(frappe.utils.get_time(record.time).strftime('%H:%M'))

    shift = frappe.db.get_value("Employee",employee,"default_shift")
    start_time = frappe.db.get_value("Shift Type",shift,"start_time")
    end_time = frappe.db.get_value("Shift Type",shift,"end_time")
    return {
        'checkins': checkins,
        'checkouts': checkouts,
        'end_time':frappe.utils.get_time(end_time).strftime('%H:%M'),
        'start_time':frappe.utils.get_time(start_time).strftime('%H:%M')
    }