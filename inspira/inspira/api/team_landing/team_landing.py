import frappe

from inspira.inspira.custom.utils import get_fmt_money


@frappe.whitelist()
def get_team_details():
    reports_to = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, "name")
    reimbursement_entries = frappe.db.sql(f"""
        SELECT ad.name
        FROM `tabExpense Claim` AS ad
        JOIN `tabEmployee` AS emp ON emp.name = ad.employee
        WHERE emp.expense_approver = %s AND ad.docstatus = 0
    """, frappe.session.user, as_dict=True)

    leave_requests = frappe.db.sql(f"""
        SELECT la.name
        FROM `tabLeave Application` AS la 
        WHERE la.docstatus = 0
        AND la.leave_approver = %s
    """,frappe.session.user,as_dict=True)

    regularization = frappe.db.sql(f"""
        SELECT la.name
        FROM `tabAttendance Request` AS la 
        JOIN `tabEmployee` AS e ON e.name = la.employee
        WHERE la.docstatus = 0
        AND e.reports_to = %s
    """,reports_to,as_dict=True)

    outdoor_duty = frappe.db.sql(f"""
        SELECT la.name
        FROM `tabIDP Outdoor Duty Request` AS la 
        JOIN `tabEmployee` AS e ON e.name = la.employee
        WHERE la.docstatus = 0
        AND e.reports_to = %s
    """,reports_to,as_dict=True)

    timesheet = frappe.db.sql(f"""
        SELECT la.name
        FROM `tabIDP Timesheet` AS la 
        JOIN `tabEmployee` AS e ON e.name = la.employee
        WHERE la.docstatus = 0
        AND e.reports_to = %s
    """,reports_to,as_dict=True)
    employees = frappe.db.get_all("Employee",{"reports_to":reports_to,"status":"Active"},"name")
    return {
        "action_items": [
            {
                "label": "Leave Requests",
                "count": len(leave_requests)
            },
            {
                "label": "Regularization",
                "count": len(regularization)
            },
            {
                "label": "Outdoor Duty",
                "count": len(outdoor_duty)
            },
            {
                "label": "Timesheet",
                "count": len(timesheet)
            },
            {
                "label": "Reimbursement",
                "count": len(reimbursement_entries)
            },
        ],
        "employeesWithOverdueTasks":get_overdue_tasks_by_user(),
        "projections":get_projections(),
        "tracking_details":get_tracking_details(),
        "employee_letters":get_employee_letters(),
        "fees_collection":get_fee_collection_data(),
        'employees':len(employees),
        "loggedHoursData":get_logged_hours_data()
    }


# def get_overdue_tasks_by_user():
#     from frappe.utils import getdate, nowdate

#     user = frappe.session.user
#     today = getdate(nowdate())

#     # Step 1: Get team user_ids
#     reports_to = frappe.db.get_value("Employee", {"user_id": user}, "name")
#     team = frappe.db.sql("""
#         SELECT emp.user_id, emp.employee_name
#         FROM `tabEmployee` emp
#         WHERE emp.reports_to = %s
#     """, reports_to, as_dict=True)

#     team_users = [{"user_id": user, "employee_name": frappe.db.get_value("Employee", {"user_id": user}, "employee_name")}]
#     team_users += [t for t in team if t.get("user_id")]

#     all_user_ids = [u["user_id"] for u in team_users]
#     placeholders = ','.join(['%s'] * len(all_user_ids))

#     # Step 2: Fetch assigned and overdue counts
#     task_query = f"""
#         SELECT
#             ta.user AS user,
#             COUNT(*) AS assigned,
#             SUM(CASE WHEN t.planned_end_date < %s AND t.status != 'Completed' THEN 1 ELSE 0 END) AS overdue
#         FROM `tabIDP Task` t
#         JOIN `tabIDP Task Assignee` ta
#         WHERE ta.user IN ({placeholders})
#         GROUP BY ta.user
#     """

#     task_data = frappe.db.sql(task_query, [today] + all_user_ids, as_dict=True)

#     # Step 3: Map names + counts
#     result = []
#     user_id_to_name = {u["user_id"]: u["employee_name"] for u in team_users}

#     for row in task_data:
#         result.append({
#             "name": user_id_to_name.get(row["user"], row["user"]),
#             "assigned": row["assigned"],
#             "overdue": row["overdue"]
#         })

#     return result

# @frappe.whitelist()
def get_overdue_tasks_by_user():
    from frappe.utils import getdate, nowdate

    user = frappe.session.user
    today = getdate(nowdate())

    # Step 1: Get team user_ids and names
    reports_to = frappe.db.get_value("Employee", {"user_id": user}, "name")
    team = frappe.db.sql("""
        SELECT emp.user_id, emp.employee_name
        FROM `tabEmployee` emp
        WHERE emp.reports_to = %s
    """, reports_to, as_dict=True)

    # Include current user
    current_emp_name = frappe.db.get_value("Employee", {"user_id": user}, "employee_name")
    team_users = [{"user_id": user, "employee_name": current_emp_name}] + [t for t in team if t.get("user_id")]
    all_user_ids = [u["user_id"] for u in team_users]

    if not all_user_ids:
        return []

    placeholders = ','.join(['%s'] * len(all_user_ids))

    # Step 2: Fetch task counts per user from Task Assignee child table
    task_query = f"""
        SELECT
            ta.user AS user,
            COUNT(*) AS assigned,
            SUM(CASE WHEN t.planned_end_date < %s AND t.status != 'Completed' THEN 1 ELSE 0 END) AS overdue
        FROM `tabIDP Task` t
        JOIN `tabIDP Task Assignee` ta ON ta.parent = t.name
        WHERE ta.user IN ({placeholders})
        GROUP BY ta.user
    """

    task_data = frappe.db.sql(task_query, [today] + all_user_ids, as_dict=True)

    # Step 3: Map to name + count structure
    user_id_to_name = {u["user_id"]: u["employee_name"] for u in team_users}
    result = []

    for row in task_data:
        result.append({
            "name": user_id_to_name.get(row["user"], row["user"]),
            "assigned": row["assigned"],
            "overdue": row["overdue"]
        })

    return result

def get_projections():
    from frappe.utils import getdate,get_first_day, get_last_day
    from erpnext.accounts.utils import get_fiscal_year

    today = getdate()
    fiscal_year = get_fiscal_year(today)
    fiscal_start, fiscal_end = fiscal_year[1], fiscal_year[2]

    result = frappe.db.sql(f"""
        SELECT SUM(im.payment_pending)
        FROM `tabIDP Project` AS ip
        JOIN `tabIDP Project User` AS ipu ON ipu.parent = ip.name
        JOIN `tabIDP Contract` AS ic ON ic.name = ip.contract
        JOIN `tabIDP Milestones` AS im ON ic.name = im.parent
        WHERE ipu.user = %s
        AND im.contract_end_date BETWEEN %s AND %s
    """, (frappe.session.user, fiscal_start, fiscal_end))

    total_projection = result[0][0] if result and result[0][0] else 0
    month_start = today.replace(day=1)
    month_start = get_first_day(today)
    month_end = get_last_day(today)

    result = frappe.db.sql("""
        SELECT SUM(im.payment_pending)
        FROM `tabIDP Project` AS ip
        JOIN `tabIDP Project User` AS ipu ON ipu.parent = ip.name
        JOIN `tabIDP Contract` AS ic ON ic.name = ip.contract
        JOIN `tabIDP Milestones` AS im ON ic.name = im.parent
        WHERE ipu.user = %s
        AND im.contract_end_date BETWEEN %s AND %s
    """, (frappe.session.user, month_start, month_end))

    monthly_projection = result[0][0] if result and result[0][0] else 0 
    return {
        "remainingFY": get_fmt_money(total_projection),
        "currentMonth":get_fmt_money(monthly_projection)
    }

def get_tracking_details():
    from frappe.utils import getdate

    today = getdate()

    # Get manager's employee ID
    reports_to = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, "name")
    if not reports_to:
        return {"error": "Manager not found."}

    # Run raw SQL to get counts of late entries, early exits, and leaves
    data = frappe.db.sql("""
        SELECT 
            COUNT(*) AS total_marked,
            SUM(CASE WHEN a.late_entry = 1 THEN 1 ELSE 0 END) AS late_entry_count,
            SUM(CASE WHEN a.early_exit = 1 THEN 1 ELSE 0 END) AS early_exit_count,
            SUM(CASE WHEN a.status = 'On Leave' THEN 1 ELSE 0 END) AS leave_count
        FROM `tabEmployee` e
        JOIN `tabAttendance` a
            ON e.name = a.employee 
            AND a.attendance_date = %s
        WHERE e.reports_to = %s
            AND e.status != 'Inactive'
    """, (today, reports_to), as_dict=True)
    outdoor_duty = len(frappe.db.get_all("IDP Outdoor Duty Request",{'date':today,"docstatus":1}))
    
    row = data[0] if data else {}

    # Prepare the format for frontend
    tracking_metrics = [
        {"label": "Early Checkouts", "value": row.get("early_exit_count") or 0},
        {"label": "Late Check-ins", "value": row.get("late_entry_count") or 0},
        {"label": "On Leave", "value": row.get("leave_count") or 0},
        {"label": "Outdoor Duty", "value": outdoor_duty}
    ]

    return {
        "date": today,
        "tracking_metrics": tracking_metrics
    }

def get_employee_letters():
    from frappe.utils import getdate

    today = getdate()

    # Get manager's employee ID
    reports_to = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, "name")
    if not reports_to:
        return {"error": "Manager not found."}

    # --- Get 'On Leave' Employees ---
    on_leave = frappe.db.sql("""
        SELECT DISTINCT e.employee_name
        FROM `tabEmployee` e
        JOIN `tabAttendance` a ON e.name = a.employee
        WHERE a.attendance_date = %s
        AND a.status = 'On Leave'
        AND e.reports_to = %s
        AND e.status != 'Inactive'
    """, (today, reports_to), as_dict=True)

    # --- Get 'Outdoor Duty' Employees ---
    outdoor = frappe.db.sql("""
        SELECT DISTINCT e.employee_name
        FROM `tabEmployee` e
        JOIN `tabIDP Outdoor Duty Request` odr ON e.name = odr.employee
        WHERE odr.date = %s
        AND odr.docstatus = 1
        AND e.reports_to = %s
        AND e.status != 'Inactive'
    """, (today, reports_to), as_dict=True)

    def extract_letters(data):
        letters = [name["employee_name"][0].upper() for name in data if name.get("employee_name")]
        letters = sorted(set(letters))  # Unique & sorted
        return {
            "letters": letters[:4],
            "additional": max(0, len(letters) - 4)
        }

    return {
        "onLeaveData": extract_letters(on_leave),
        "onSiteData": extract_letters(outdoor)
    }

# def get_fee_collection_data():
#     from frappe.utils import getdate, flt
#     from erpnext.accounts.utils import get_fiscal_year

#     today = getdate()
#     fiscal_year = get_fiscal_year(today)
#     fiscal_start, fiscal_end = fiscal_year[1], fiscal_year[2]
#     contracts = frappe.db.sql(f"""
#         SELECT ip.contract
#         FROM `tabIDP Project` AS ip 
#         JOIN `tabIDP Project User` AS ipu ON ipu.parent = ip.name
#         WHERE ipu.user = %S
#     """,(frappe.session.user),as_dict = True)
#     result = frappe.db.sql("""
#         SELECT 
#             SUM(si.grand_total) AS total_invoiced,
#             SUM(si.outstanding_amount) AS total_outstanding
#         FROM `tabSales Invoice` AS si
#         JOIN `tabIDP Contract` AS ic ON ic.name = si.contract
#         WHERE posting_date BETWEEN %s AND %s
#         AND docstatus = 1
#     """, (fiscal_start, fiscal_end), as_dict=True)

#     data = result[0] if result else {}
#     invoiced = flt(data.get("total_invoiced", 0))
#     balance = flt(data.get("total_outstanding", 0))
#     collected = invoiced - balance

#     percent_collected = round((collected / invoiced) * 100, 2) if invoiced else 0
#     percent_balance = round((balance / invoiced) * 100, 2) if invoiced else 0

#     return {
#         "series": [percent_collected, percent_balance],
#         "labels": ['Fee Collected', 'Balance'],
#         "details": [
#             {"color": "#7e73a6", "label": "Invoiced", "value": f"{invoiced:,.0f}"},
#             {"color": "#7e73a6", "label": "Fee Collected", "value": f"{collected:,.0f}"},
#             {"color": "#7e73a6", "label": "Balance", "value": f"{balance:,.0f}"}
#         ]
#     }

def get_fee_collection_data():
    from frappe.utils import getdate, flt
    from erpnext.accounts.utils import get_fiscal_year

    today = getdate()
    fiscal_year = get_fiscal_year(today)
    fiscal_start, fiscal_end = fiscal_year[1], fiscal_year[2]

    # Step 1: Get contracts linked to this user via IDP Project
    contracts = frappe.db.sql("""
        SELECT ip.contract
        FROM `tabIDP Project` AS ip 
        JOIN `tabIDP Project User` AS ipu ON ipu.parent = ip.name
        WHERE ipu.user = %s
    """, (frappe.session.user,), as_dict=True)

    contract_list = [c.contract for c in contracts if c.contract]

    if not contract_list:
        return {
            "series": [0, 100],
            "labels": ['Fee Collected', 'Balance'],
            "details": [
                {"color": "#7e73a6", "label": "Invoiced", "value": "0"},
                {"color": "#7e73a6", "label": "Fee Collected", "value": "0"},
                {"color": "#7e73a6", "label": "Balance", "value": "0"}
            ]
        }

    # Step 2: Get invoiced, collected, and outstanding for user's contracts
    result = frappe.db.sql("""
        SELECT 
            SUM(si.grand_total) AS total_invoiced,
            SUM(si.outstanding_amount) AS total_outstanding
        FROM `tabSales Invoice` AS si
        WHERE si.contract IN %(contracts)s
        AND si.posting_date BETWEEN %(start)s AND %(end)s
        AND si.docstatus = 1
    """, {
        "contracts": tuple(contract_list),
        "start": fiscal_start,
        "end": fiscal_end
    }, as_dict=True)

    data = result[0] if result else {}
    invoiced = flt(data.get("total_invoiced", 0))
    balance = flt(data.get("total_outstanding", 0))
    collected = invoiced - balance

    percent_collected = round((collected / invoiced) * 100, 2) if invoiced else 0
    percent_balance = round((balance / invoiced) * 100, 2) if invoiced else 0

    return {
        "series": [percent_collected, percent_balance],
        "labels": ['Fee Collected', 'Balance'],
        "details": [
            {"color": "#7e73a6", "label": "Invoiced", "value": f"{get_fmt_money(invoiced)}"},
            {"color": "#7e73a6", "label": "Fee Collected", "value": f"{get_fmt_money(collected)}"},
            {"color": "#7e73a6", "label": "Balance", "value": f"{get_fmt_money(balance)}"}
        ]
    }

def get_logged_hours_data():
    from frappe.utils import flt

    reports_to = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, "name")
    employees = frappe.db.get_all("Employee", {"reports_to": reports_to, "status": "Active"}, pluck="name")

    if not employees:
        return {
            "series": [0, 0],
            "labels": ["Non Billable hours", "Billable hours"],
            "details": [
                {"color": "#9d91c4", "label": "Non Billable hours", "value": "0%"},
                {"color": "#7e73a6", "label": "Billable hours", "value": "0%"}
            ]
        }

    filters = {"employees": tuple(employees)}

    total_hours = frappe.db.sql("""
        SELECT SUM(itd.hours) AS total_hours
        FROM `tabIDP Timesheet` AS it
        JOIN `tabIDP Timesheet Detail` AS itd ON itd.parent = it.name
        WHERE it.employee IN %(employees)s
    """, filters, as_dict=True)[0].total_hours or 0

    billable_hours = frappe.db.sql("""
        SELECT SUM(itd.hours) AS total_hours
        FROM `tabIDP Timesheet` AS it
        JOIN `tabIDP Timesheet Detail` AS itd ON itd.parent = it.name
        JOIN `tabIDP Project` AS ip ON ip.name = itd.project
        WHERE it.employee IN %(employees)s AND ip.is_billable = 1
    """, filters, as_dict=True)[0].total_hours or 0

    non_billable_hours = total_hours - billable_hours

    billable_percent = round((billable_hours / total_hours) * 100, 2) if total_hours else 0
    non_billable_percent = 100 - billable_percent

    return {
        "series": [non_billable_percent, billable_percent],
        "labels": ["Non Billable hours", "Billable hours"],
        "details": [
            {"color": "#9d91c4", "label": "Non Billable hours", "value": f"{non_billable_percent}%"},
            {"color": "#7e73a6", "label": "Billable hours", "value": f"{billable_percent}%"}
        ]
    }
