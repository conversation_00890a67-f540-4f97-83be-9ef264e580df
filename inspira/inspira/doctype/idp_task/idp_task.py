# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

import frappe
from frappe import _, throw
from frappe.desk.form.assign_to import clear, close_all_assignments
from frappe.model.mapper import get_mapped_doc
from frappe.utils import add_days, cstr, date_diff, flt, get_link_to_form, getdate, today
from frappe.utils.data import format_date
from frappe.utils.nestedset import NestedSet

from inspira.inspira.custom.utils import get_status_name_from_id, get_status_id_from_status_name
from inspira.inspira.doctype.idp_contract.idp_contract import update_milestone_payment_status


class CircularReferenceError(frappe.ValidationError):
	pass


class IDPTask(NestedSet):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		from inspira.inspira.doctype.idp_task_depends_on.idp_task_depends_on import IDPTaskDependsOn

		actual_time: DF.Float
		closing_date: DF.Date | None
		color: DF.Color | None
		company: DF.Link | None
		completed_by: DF.Link | None
		completed_on: DF.Date | None
		department: DF.Link | None
		depends_on: DF.Table[IDPTaskDependsOn]
		depends_on_tasks: DF.Code | None
		description: DF.TextEditor | None
		duration: DF.Int
		expected_time: DF.Float
		is_group: DF.Check
		is_milestone: DF.Check
		is_template: DF.Check
		issue: DF.Link | None
		lft: DF.Int
		old_parent: DF.Data | None
		parent_task: DF.Link | None
		priority: DF.Literal["Low", "Medium", "High", "Urgent"]
		progress: DF.Percent
		project: DF.Link | None
		review_date: DF.Date | None
		rgt: DF.Int
		start: DF.Int
		status: DF.Literal[
			"Open", "Working", "Pending Review", "Overdue", "Template", "Completed", "Cancelled"
		]
		subject: DF.Data
		task_weight: DF.Float
		template_task: DF.Data | None
		total_billing_amount: DF.Currency
		total_costing_amount: DF.Currency
		type: DF.Link | None
	# end: auto-generated types

	nsm_parent_field = "parent_task"

	def get_customer_details(self):
		cust = frappe.db.sql("select customer_name from `tabCustomer` where name=%s", self.customer)
		if cust:
			ret = {"customer_name": (cust and cust[0][0]) or ""}
			return ret

	def compute_leaf_percent(self):
		mapping = {
			'To Do': 0,
			'In Progress': 0,
			'On Hold': 50,
			'Done': 100
		}
		status = get_status_name_from_id(self.status)
		return mapping.get(status, 0)

	def validate(self):
		previous = self.get_doc_before_save()
		if (previous and previous.actual_end_date != self.actual_end_date) or (self.actual_end_date and not self.completed_on):
			self.completed_on = self.actual_end_date
			self.completed_by = frappe.session.user

		# Updating milestone planned end date
		previous = self.get_doc_before_save()
		if previous and previous.completed_on != self.completed_on:
			milestone = get_root_task(self.name)
			milestone_doc = frappe.get_doc("IDP Task", milestone)
			if milestone_doc.is_milestone and milestone_doc.milestone_id:
				actual_end_date = frappe.db.get_value("IDP Milestones", milestone_doc.milestone_id, "actual_end_date")
				if not actual_end_date or (actual_end_date and getdate(actual_end_date) < getdate(self.completed_on)):
					frappe.db.set_value("IDP Milestones", milestone_doc.milestone_id, "actual_end_date", self.completed_on, update_modified=False)

		if previous and previous.planned_end_date != self.planned_end_date:
			milestone = get_root_task(self.name)
			milestone_doc = frappe.get_doc("IDP Task", milestone)
			if milestone_doc.is_milestone and milestone_doc.milestone_id:
				planned_end_date = frappe.db.get_value("IDP Milestones", milestone_doc.milestone_id, "planned_end_date")
				if not planned_end_date or (planned_end_date and getdate(planned_end_date) < getdate(self.planned_end_date)):
					frappe.db.set_value("IDP Milestones", milestone_doc.milestone_id, "planned_end_date", self.planned_end_date, update_modified=False)

		self.completion_ = self.compute_leaf_percent()

		self.validate_dates()
		self.validate_progress()
		self.validate_status()
		self.update_depends_on()
		self.validate_dependencies_for_template_task()
		self.validate_completed_on()

	def validate_dates(self):
		self.validate_from_to_dates("planned_start_date", "planned_end_date")
		self.validate_from_to_dates("actual_start_date", "actual_end_date")
		self.validate_parent_expected_end_date()
		self.validate_parent_project_dates()

	def validate_parent_expected_end_date(self):
		if not self.parent_task or not self.planned_end_date:
			return

		parent_exp_end_date = frappe.db.get_value("IDP Task", self.parent_task, "planned_end_date")
		if not parent_exp_end_date:
			return

		if getdate(self.planned_end_date) > getdate(parent_exp_end_date):
			frappe.throw(
				_(
					"Expected End Date should be less than or equal to parent task's Expected End Date {0}."
				).format(format_date(parent_exp_end_date)),
				frappe.exceptions.InvalidDates,
			)

	def validate_parent_project_dates(self):
		if not self.project or frappe.flags.in_test:
			return

		if project_end_date := frappe.db.get_value("IDP Project", self.project, "expected_end_date"):
			project_end_date = getdate(project_end_date)
			for fieldname in ("planned_start_date", "planned_end_date", "actual_start_date", "actual_end_date"):
				task_date = self.get(fieldname)
				if task_date and date_diff(project_end_date, getdate(task_date)) < 0:
					frappe.throw(
						_("{0}'s {1} cannot be after {2}'s Expected End Date.").format(
							frappe.bold(frappe.get_desk_link("IDP Task", self.name)),
							_(self.meta.get_label(fieldname)),
							frappe.bold(frappe.get_desk_link("IDP Project", self.project)),
						),
						frappe.exceptions.InvalidDates,
					)

	def validate_status(self):
		if not self.status:
			self.status = get_status_id_from_status_name("Task", "To Do")
		if self.is_template and self.status != "Template":
			self.status = "Template"
		if self.status != self.get_db_value("status") and self.status == "Completed":
			for d in self.depends_on:
				if frappe.db.get_value("IDP Task", d.task, "status") not in ("Completed", "Cancelled"):
					frappe.throw(
						_(
							"Cannot complete task {0} as its dependant task {1} are not completed / cancelled."
						).format(frappe.bold(self.name), frappe.bold(d.task))
					)

			close_all_assignments(self.doctype, self.name)

	def validate_progress(self):
		if flt(self.progress or 0) > 100:
			frappe.throw(_("Progress % for a task cannot be more than 100."))

		if self.status == "Completed":
			self.progress = 100

	def validate_dependencies_for_template_task(self):
		if self.is_template:
			self.validate_parent_template_task()
			self.validate_depends_on_tasks()

	def validate_parent_template_task(self):
		if self.parent_task:
			if not frappe.db.get_value("IDP Task", self.parent_task, "is_template"):
				parent_task_format = (
					f"""<a href="/app/idp---task/{self.parent_task}">{self.parent_task}</a>"""
				)
				frappe.throw(_("Parent Task {0} is not a Template Task").format(parent_task_format))

	def validate_depends_on_tasks(self):
		if self.depends_on:
			for task in self.depends_on:
				if not frappe.db.get_value("IDP Task", task.task, "is_template"):
					dependent_task_format = f"""<a href="/app/idp---task/{task.task}">{task.task}</a>"""
					frappe.throw(_("Dependent Task {0} is not a Template Task").format(dependent_task_format))

	def validate_completed_on(self):
		status = get_status_name_from_id(self.status)
		if status == "Done" and self.type == "Task" and not self.completed_on:
			frappe.throw("Please update Actual End Date first")
			return

		if self.completed_on and getdate(self.completed_on) > getdate():
			frappe.throw(_("Completed On cannot be greater than Today"))

	def update_depends_on(self):
		depends_on_tasks = ""
		for d in self.depends_on:
			if d.task and d.task not in depends_on_tasks:
				depends_on_tasks += d.task + ","
		self.depends_on_tasks = depends_on_tasks

	def update_nsm_model(self):
		frappe.utils.nestedset.update_nsm(self)

	def on_update(self):
		self.update_nsm_model()
		self.check_recursion()
		self.reschedule_dependent_tasks()
		self.update_project()
		self.unassign_todo()
		self.populate_depends_on()
		self.update_completion_percentage()

	def update_completion_percentage(self):
		previous = self.get_doc_before_save()
		if not previous or self.status != self.get_doc_before_save().status and self.type == 'Task':
			# rollup_parent(self.name, self.parent_task, self.completion_)
			frappe.enqueue(
				rollup_parent,
				task_name=self.name,
				parent_task=self.parent_task,
				completion_=self.completion_,
				queue="long",
				is_async=True,
				enqueue_after_commit=True
			)

	def unassign_todo(self):
		if self.status == "Completed":
			close_all_assignments(self.doctype, self.name)
		if self.status == "Cancelled":
			clear(self.doctype, self.name)

	def update_time_and_costing(self):
		tl = frappe.db.sql(
			"""select min(from_time) as start_date, max(to_time) as end_date,
			sum(billing_amount) as total_billing_amount, sum(costing_amount) as total_costing_amount,
			sum(hours) as time from `tabTimesheet Detail` where task = %s and docstatus=1""",
			self.name,
			as_dict=1,
		)[0]
		if self.status == "Open":
			self.status = "Working"
		self.total_costing_amount = tl.total_costing_amount
		self.total_billing_amount = tl.total_billing_amount
		self.actual_time = tl.time
		self.actual_start_date = tl.start_date
		self.actual_end_date = tl.end_date

	def update_project(self):
		if self.project and not self.flags.from_project:
			frappe.get_cached_doc("IDP Project", self.project).update_project()

	def check_recursion(self):
		if self.flags.ignore_recursion_check:
			return
		check_list = [["task", "parent"], ["parent", "task"]]
		for d in check_list:
			task_list, count = [self.name], 0
			while len(task_list) > count:
				tasks = frappe.db.sql(
					" select {} from `tabIDP Task Depends On` where {} = {} ".format(d[0], d[1], "%s"),
					cstr(task_list[count]),
				)
				count = count + 1
				for b in tasks:
					if b[0] == self.name:
						frappe.throw(_("Circular Reference Error"), CircularReferenceError)
					if b[0]:
						task_list.append(b[0])

				if count == 15:
					break

	def reschedule_dependent_tasks(self):
		end_date = self.planned_end_date or self.actual_end_date
		if end_date:
			for task_name in frappe.db.sql(
				"""
				select name from `tabIDP Task` as parent
				where parent.project = %(project)s
					and parent.name in (
						select parent from `tabIDP Task Depends On` as child
						where child.task = %(task)s and child.project = %(project)s)
			""",
				{"project": self.project, "task": self.name},
				as_dict=1,
			):
				task = frappe.get_doc("IDP Task", task_name.name)
				if (
					task.planned_start_date
					and task.planned_start_date
					and task.planned_start_date < getdate(end_date)
					and task.status == "Open"
				):
					task_duration = date_diff(task.planned_end_date, task.planned_start_date)
					task.planned_start_date = add_days(end_date, 1)
					task.planned_end_date = add_days(task.planned_start_date, task_duration)
					task.flags.ignore_recursion_check = True
					task.save()

	def has_webform_permission(self):
		project_user = frappe.db.get_value(
			"IDP Project User", {"parent": self.project, "user": frappe.session.user}, "user"
		)
		if project_user:
			return True

	def populate_depends_on(self):
		if self.parent_task:
			parent = frappe.get_doc("IDP Task", self.parent_task)
			if self.name not in [row.task for row in parent.depends_on]:
				parent.append(
					"depends_on",
					{"doctype": "IDP Task Depends On", "task": self.name, "subject": self.subject},
				)
				parent.save()

	def on_trash(self):
		if check_if_child_exists(self.name):
			throw(_("Child Task exists for this Task. You can not delete this Task."))

		self.update_nsm_model()

	def after_delete(self):
		self.update_project()

	def update_status(self):
		if self.status not in ("Cancelled", "Completed") and self.planned_end_date:
			from datetime import datetime

			if self.planned_end_date < datetime.now().date():
				self.db_set("status", "Overdue", update_modified=False)
				self.update_project()


@frappe.whitelist()
def check_if_child_exists(name):
	child_tasks = frappe.get_all("IDP Task", filters={"parent_task": name})
	child_tasks = [get_link_to_form("IDP Task", task.name) for task in child_tasks]
	return child_tasks


@frappe.whitelist()
@frappe.validate_and_sanitize_search_inputs
def get_project(doctype, txt, searchfield, start, page_len, filters):
	from erpnext.controllers.queries import get_match_cond

	meta = frappe.get_meta(doctype)
	searchfields = meta.get_search_fields()
	search_columns = ", " + ", ".join(searchfields) if searchfields else ""
	search_cond = " or " + " or ".join(field + " like %(txt)s" for field in searchfields)

	return frappe.db.sql(
		f""" select name {search_columns} from `tabIDP Project`
		where %(key)s like %(txt)s
			%(mcond)s
			{search_cond}
		order by name
		limit %(page_len)s offset %(start)s""",
		{
			"key": searchfield,
			"txt": "%" + txt + "%",
			"mcond": get_match_cond(doctype),
			"start": start,
			"page_len": page_len,
		},
	)


@frappe.whitelist()
def set_multiple_status(names, status):
	names = frappe.parse_json(names)
	for name in names:
		task = frappe.get_doc("IDP Task", name)
		task.status = status
		task.save()


def set_tasks_as_overdue():
	tasks = frappe.get_all(
		"IDP Task",
		filters={"status": ["not in", ["Cancelled", "Completed"]]},
		fields=["name", "status", "review_date"],
	)
	for task in tasks:
		if task.status == "Pending Review":
			if getdate(task.review_date) > getdate(today()):
				continue
		frappe.get_doc("IDP Task", task.name).update_status()


@frappe.whitelist()
def make_timesheet(source_name, target_doc=None, ignore_permissions=False):
	def set_missing_values(source, target):
		target.parent_project = source.project
		target.append(
			"time_logs",
			{
				"hours": source.actual_time,
				"completed": source.status == "Completed",
				"project": source.project,
				"task": source.name,
			},
		)

	doclist = get_mapped_doc(
		"IDP Task",
		source_name,
		{"IDP Task": {"doctype": "Timesheet"}},
		target_doc,
		postprocess=set_missing_values,
		ignore_permissions=ignore_permissions,
	)

	return doclist


@frappe.whitelist()
def get_children(doctype, parent, task=None, project=None, is_root=False):
	filters = [["docstatus", "<", "2"]]

	if task:
		filters.append(["parent_task", "=", task])
	elif parent and not is_root:
		# via expand child
		filters.append(["parent_task", "=", parent])
	else:
		filters.append(['ifnull(`parent_task`, "")', "=", ""])

	if project:
		filters.append(["project", "=", project])

	tasks = frappe.get_list(
		doctype,
		fields=["name as value", "subject as title", "is_group as expandable"],
		filters=filters,
		order_by="name",
	)

	# return tasks
	return tasks


@frappe.whitelist()
def add_node():
	from frappe.desk.treeview import make_tree_args

	args = frappe.form_dict
	args.update({"name_field": "subject"})
	args = make_tree_args(**args)

	if args.parent_task == "All Tasks" or args.parent_task == args.project:
		args.parent_task = None

	frappe.get_doc(args).insert()


@frappe.whitelist()
def add_multiple_tasks(data, parent):
	data = frappe.parse_json(data)
	new_doc = {"doctype": "IDP Task", "parent_task": parent if parent != "All Tasks" else ""}
	new_doc["project"] = frappe.db.get_value("IDP Task", {"name": parent}, "project") or ""

	for d in data:
		if not d.get("subject"):
			continue
		new_doc["subject"] = d.get("subject")
		new_task = frappe.get_doc(new_doc)
		new_task.insert()


def on_doctype_update():
	frappe.db.add_index("IDP Task", ["lft", "rgt"])

def rollup_parent(task_name, parent_task=None, completion_=None, is_root_call=True):
	if completion_ is not None:
		frappe.db.set_value('IDP Task', task_name, 'completion_', flt(completion_), update_modified=False)

	if not parent_task:
		parent_task = frappe.db.get_value('IDP Task', task_name, 'parent_task')
	if not parent_task:
		return

	parent = frappe.get_cached_doc('IDP Task', parent_task)

	# Fetch children
	children = frappe.get_all(
		'IDP Task',
		filters={'parent_task': parent.name},
		fields=['name', 'completion_', 'type']
	)

	if not children:
		return

	c_sum = sum(c.completion_ for c in children)
	avg = c_sum / len(children)

	# Set status
	status_done = get_status_id_from_status_name("Task", "Done")
	status_in_progress = get_status_id_from_status_name("Task", "In Progress")

	parent.status = status_done if avg == 100 else status_in_progress
	parent.completion_ = avg

	# Clear completion dates if not complete
	if avg < 100:
		parent.completed_on = None
		parent.actual_end_date = None

	# Check if all subtasks are completed
	if parent.type in ["Deliverables", "Milestone"]:
		tasks = frappe.get_all(
			"IDP Task",
			filters={"parent_task": parent.name},
			fields=["completed_on", "completion_"]
		)
		completed_dates = [t.completed_on for t in tasks if t.completed_on]
		if tasks and all(t.completion_ == 100 for t in tasks) and completed_dates:
			max_completion_date = max(completed_dates)
			parent.completed_on = max_completion_date
			parent.actual_end_date = max_completion_date

	parent.save(ignore_permissions=True)

	# Recurse upward
	rollup_parent(parent.name, completion_=avg, is_root_call=False)

	# Final call update
	if is_root_call:
		update_milestone_completion(task_name)


def update_milestone_completion(task_name):
	parent_task = get_root_task(task_name)
	task_doc = frappe.get_doc("IDP Task", parent_task)

	if task_doc.is_milestone and task_doc.milestone_id:
		all_milestones = frappe.get_all(
			"IDP Task",
			filters={"milestone_id": task_doc.milestone_id},
			fields=["completion_", "completed_on"]
		)

		if not all_milestones:
			return

		milestone_completion = sum(m.completion_ for m in all_milestones) / len(all_milestones)
		all_completed = all(m.completion_ == 100 for m in all_milestones)
		project_dates = [m.completed_on for m in all_milestones if m.completed_on]

		update_json = {
			"completion_": milestone_completion,
			"actual_end_date": None
		}
		if all_completed and project_dates:
			update_json["actual_end_date"] = max(project_dates)

		# frappe.db.set_value("IDP Milestones", task_doc.milestone_id, update_json, update_modified=False)
		contract_doc = frappe.get_doc("IDP Contract", task_doc.contract)
		
		# Update contract completion based on milestone fee %
		# total_completion = 0
		total_weight = 0
		weighted_completion_sum = 0
		for milestone in contract_doc.milestones:
			if milestone.name == task_doc.milestone_id:
				milestone.completion_ = milestone_completion
				milestone.actual_end_date = update_json["actual_end_date"]
			# total_completion += milestone.completion_
			weight = milestone.fee_ or 0
			total_weight += weight
			weighted_completion_sum += (milestone.completion_ or 0) * weight

		# contract_doc.completion_ = total_completion / len(contract_doc.milestones) if contract_doc.milestones else 0
		if total_weight > 0:
			contract_doc.completion_ = weighted_completion_sum / total_weight
		else:
			contract_doc.completion_ = 0
		if contract_doc.completion_ == 100:
			contract_doc.completion_date = frappe.utils.getdate()
		contract_doc.save(ignore_permissions=True)
		frappe.db.set_value("IDP Project", task_doc.project, "percent_complete", contract_doc.completion_, update_modified=False)
		if contract_doc.completion_ == 100:
			frappe.db.set_value("IDP Project",task_doc.project,"completion_date", frappe.utils.getdate(), update_modified=False)
		update_milestone_payment_status(task_doc.milestone_id)


def get_root_task(task_name):
	while True:
		parent = frappe.db.get_value("IDP Task", task_name, "parent_task")
		if not parent:
			return task_name
		task_name = parent
