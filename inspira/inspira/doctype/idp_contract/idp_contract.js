// Copyright (c) 2025, Agkiya Labs and contributors
// For license information, please see license.txt

frappe.ui.form.on("IDP Contract", {
	refresh(frm) {
		frm.set_query("contract_template", function () {
			return {
				filters: [
					[ "contract_type", "=", frm.doc.contract_type]
				],
			};
		});

		setTimeout(() => {
			$(
				"button.btn.btn-new.btn-secondary.btn-xs.icon-btn[data-doctype='IDP Project']"
			).hide();
		});

		setTimeout(() => {
			$(
				"button.btn.btn-new.btn-secondary.btn-xs.icon-btn[data-doctype='Sales Invoice']"
			).hide();
		});

		frm.trigger("add_buttons");
	},

	add_buttons(frm) {
		frm.add_custom_button(
			__("Project"),
			() => {
			  frm.trigger("create_project");
			},
			__("Create")
		  );
	},

	create_project(frm) {
		let d = new frappe.ui.Dialog({
			title: __("Create Project"),
			fields: [
				{
					fieldname: "project_name",
					fieldtype: "Data",
					label: "Project Name",
					reqd: 1,
				},
			],
			primary_action: function () {
				frappe.call({
					method: "inspira.inspira.doctype.idp_project.idp_project.create_project",
					args: {
						contract: frm.doc.name,
						project_name: d.get_value('project_name'),
					},
					callback: function (r) {
						if (r && r.message) {
						frm.refresh();
						}
					},
				});
				d.hide();
			  },
			primary_action_label: __("Create Project"),
		}).show();
	},

	contract_type: function (frm) {
		frm.set_value("contract_template", '');
	},

	project_value: function (frm) {
		update_fee_amount_all(frm);
	},

	contract_template: function (frm) {
		if (frm.doc.contract_template) {
			frappe.call({
				method: "frappe.client.get",
				args: {
					doctype: "IDP Contract Template",
					name: frm.doc.contract_template,
				},
				callback(r) {
					if(r.message) {
						frm.set_value("milestones", []);
						var template = r.message;
						keysToDelete = ['creation', 'docstatus', 'doctype', 'idx', 'modified', 'modified_by', 'name', 'owner', 'parent', 'parentfield', 'parenttype'];
						template.milestones.forEach(m => {
							keysToDelete.forEach(k => {
								delete m[k];
							});

							frm.add_child('milestones', m);
						});
						update_fee_amount_all(frm, "fee_");
						frm.refresh_field('milestones');
					}
				}
			});
		} else {
			frm.set_value("milestones", []);
		}
	},

	contract_terms_template: function (frm) {
		if (frm.doc.contract_terms_template) {
			frappe.call({
				method:
					"inspira.inspira.doctype.idp_contract_terms_template.idp_contract_terms_template.get_contract_template",
				args: {
					template_name: frm.doc.contract_terms_template,
					doc: frm.doc,
				},
				callback: function (r) {
					if (r && r.message) {
						let contract_terms_template = r.message.contract_terms_template;
						frm.set_value("contract_terms", r.message.contract_terms);
						frm.set_value(
							"requires_fulfilment",
							contract_terms_template.requires_fulfilment
						);

						if (frm.doc.requires_fulfilment) {
							// Populate the fulfilment terms table from a contract template, if any
							r.message.contract_terms_template.fulfilment_terms.forEach(
								(element) => {
									let d = frm.add_child("fulfilment_terms");
									d.requirement = element.requirement;
								}
							);
							frm.refresh_field("fulfilment_terms");
						}
					}
				},
			});
		} else {
			frm.set_value("contract_terms", "");
		}
	},
});


frappe.ui.form.on("IDP Milestones", {
	fee_(frm, cdt, cdn) {
		var milestone = frappe.get_doc(cdt, cdn);
		milestone.fee_amount =  update_fee_amount(frm, cdt, cdn, "fee_")
		frm.refresh_field("milestones");
	},

	fee_amount(frm, cdt, cdn) {
		var milestone = frappe.get_doc(cdt, cdn);
		milestone.fee_ = update_fee_amount(frm, cdt, cdn, "fee_amount")
		frm.refresh_field("milestones");
	}
});

function update_fee_amount_all(frm, based_on) {
	frm.doc.milestones.forEach(m => {
		m.fee_amount = update_fee_amount(frm, m.doctype, m.name, based_on)
	});
	frm.refresh_field("milestones");
}

function update_fee_amount(frm, cdt, cdn, based_on) { 
	if (based_on == "fee_") {
		var row = locals[cdt][cdn];
		return row.fee_ * (frm.doc.project_value || 0) / 100;
		
	} else {
		var row = locals[cdt][cdn];
		return row.fee_amount * 100 / (frm.doc.project_value || 0);
	}
}