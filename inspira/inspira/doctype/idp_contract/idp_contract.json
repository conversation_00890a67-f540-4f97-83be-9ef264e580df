{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2025-02-12 16:44:29.146408", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["party_type", "party_name", "status", "is_signed", "party_user", "fulfilment_status", "square_feet", "category", "classification", "sub_classification", "cb_party", "company", "project_value", "project_link", "contract_type", "contract_template", "contract_pdf", "deal", "completion_", "completion_date", "contract_lead", "sb_terms", "start_date", "cb_date", "end_date", "section_break_huyf", "milestones", "sb_signee", "signee", "signed_on", "cb_user", "ip_address", "sb_contract", "contract_terms_template", "contract_terms", "sb_fulfilment", "requires_fulfilment", "fulfilment_deadline", "fulfilment_terms", "authorised_by_section", "signee_company", "signed_by_company", "sb_references", "document_type", "cb_links", "document_name", "amended_from"], "fields": [{"default": "Customer", "fieldname": "party_type", "fieldtype": "Select", "label": "Party Type", "options": "Customer\nSupplier\nEmployee", "reqd": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "is_signed", "fieldtype": "Check", "hidden": 1, "label": "Signed", "no_copy": 1}, {"fieldname": "cb_party", "fieldtype": "Column Break"}, {"fieldname": "party_name", "fieldtype": "Dynamic Link", "in_standard_filter": 1, "label": "Party Name", "options": "party_type", "reqd": 1}, {"fieldname": "party_user", "fieldtype": "Link", "label": "Party User", "options": "User"}, {"allow_on_submit": 1, "fieldname": "status", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "link_filters": "[[\"IDP Status Master\",\"form\",\"=\",\"Contract\"]]", "no_copy": 1, "options": "IDP Status Master", "reqd": 1}, {"allow_on_submit": 1, "fieldname": "fulfilment_status", "fieldtype": "Select", "hidden": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Fulfilment Status", "no_copy": 1, "options": "N/A\nUnfulfilled\nPartially Fulfilled\nFulfilled\nLapsed"}, {"fieldname": "sb_terms", "fieldtype": "Section Break", "label": "Contract Period"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "cb_date", "fieldtype": "Column Break"}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date"}, {"depends_on": "eval:doc.is_signed==1", "fieldname": "sb_signee", "fieldtype": "Section Break", "label": "<PERSON>ee <PERSON>"}, {"allow_on_submit": 1, "fieldname": "signee", "fieldtype": "Data", "in_global_search": 1, "label": "Signee", "no_copy": 1}, {"allow_on_submit": 1, "fieldname": "signed_on", "fieldtype": "Datetime", "in_list_view": 1, "label": "Signed On", "no_copy": 1}, {"fieldname": "cb_user", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "fieldname": "ip_address", "fieldtype": "Data", "label": "IP Address", "no_copy": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "eval:doc.docstatus==0", "fieldname": "sb_contract", "fieldtype": "Section Break", "label": "Contract Details"}, {"fieldname": "contract_template", "fieldtype": "Link", "label": "Contract Template", "options": "IDP Contract Template"}, {"fieldname": "contract_terms", "fieldtype": "Text Editor", "in_list_view": 1, "label": "Contract Terms"}, {"fieldname": "sb_fulfilment", "fieldtype": "Section Break", "label": "Fulfilment Details"}, {"default": "0", "fieldname": "requires_fulfilment", "fieldtype": "Check", "label": "Requires Fulfilment"}, {"depends_on": "eval:doc.requires_fulfilment==1", "fieldname": "fulfilment_deadline", "fieldtype": "Date", "label": "Fulfilment Deadline"}, {"allow_on_submit": 1, "depends_on": "eval:doc.requires_fulfilment==1", "fieldname": "fulfilment_terms", "fieldtype": "Table", "label": "Fulfilment Terms", "options": "IDP Contract Fulfilment Checklist"}, {"fieldname": "authorised_by_section", "fieldtype": "Section Break", "label": "Authorised By"}, {"fieldname": "signee_company", "fieldtype": "Signature", "label": "Signee (Company)"}, {"fieldname": "signed_by_company", "fieldtype": "Link", "label": "Signed By (Company)", "options": "User", "read_only": 1}, {"collapsible": 1, "fieldname": "sb_references", "fieldtype": "Section Break", "label": "References"}, {"fieldname": "document_type", "fieldtype": "Select", "label": "Document Type", "options": "\nQuotation\nIDP Project\nSales Order\nPurchase Order\nSales Invoice\nPurchase Invoice"}, {"fieldname": "cb_links", "fieldtype": "Column Break"}, {"fieldname": "document_name", "fieldtype": "Dynamic Link", "in_global_search": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Document Name", "options": "document_type"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "IDP Contract", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "section_break_huyf", "fieldtype": "Section Break", "label": "Milestones"}, {"fieldname": "milestones", "fieldtype": "Table", "label": "Milestones", "options": "IDP Milestones"}, {"fieldname": "project_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Project Value", "reqd": 1}, {"fieldname": "project_link", "fieldtype": "Data", "label": "Project Link"}, {"fieldname": "contract_type", "fieldtype": "Select", "label": "Contract Type", "options": "\nDelivery\nRetainer", "reqd": 1}, {"fieldname": "contract_terms_template", "fieldtype": "Link", "label": "Contract Terms Template", "options": "IDP Contract Terms Template"}, {"fieldname": "square_feet", "fieldtype": "Float", "label": "Square Feet", "non_negative": 1, "reqd": 1}, {"fieldname": "category", "fieldtype": "Select", "label": "Category", "options": "\nInterior\nArchitecture", "reqd": 1}, {"fieldname": "classification", "fieldtype": "Select", "label": "Classification", "options": "\nResidential\nCommercial", "reqd": 1}, {"fieldname": "sub_classification", "fieldtype": "Link", "label": "Sub Classification", "options": "IDP Sub Classification", "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "deal", "fieldtype": "Link", "label": "Deal", "options": "CRM Deal", "read_only": 1}, {"fieldname": "contract_pdf", "fieldtype": "Attach", "label": "Contract PDF"}, {"fieldname": "completion_", "fieldtype": "Percent", "label": "Completion %", "read_only": 1}, {"fieldname": "contract_lead", "fieldtype": "Table MultiSelect", "label": "Contract Lead", "options": "IDP Contract Lead"}, {"fieldname": "completion_date", "fieldtype": "Date", "label": "Completion Date", "read_only": 1}], "grid_page_length": 50, "links": [{"link_doctype": "IDP Project", "link_fieldname": "contract"}, {"link_doctype": "Sales Invoice", "link_fieldname": "contract"}], "modified": "2025-07-03 11:31:54.350060", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Contract", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Purchase Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "HR Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1, "track_seen": 1}