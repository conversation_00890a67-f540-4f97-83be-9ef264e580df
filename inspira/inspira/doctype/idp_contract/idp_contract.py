# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

import frappe
from frappe import _
from frappe.model.document import Document
from frappe.utils import getdate, nowdate
from frappe.model.naming import make_autoname
from erpnext.accounts.utils import get_fiscal_year


from inspira.inspira.custom.utils import get_label_from_fieldname, get_status_id_from_status_name
from inspira.inspira.doctype.idp_project.idp_project import create_specific_milestones

class IDPContract(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		from inspira.inspira.doctype.idp_contract_fulfilment_checklist.idp_contract_fulfilment_checklist import (
			IDPContractFulfilmentChecklist,
		)

		amended_from: DF.Link | None
		contract_template: DF.Link | None
		document_name: DF.DynamicLink | None
		document_type: DF.Literal[
			"",
			"Quotation",
			"IDP Project",
			"Sales Order",
			"Purchase Order",
			"Sales Invoice",
			"Purchase Invoice",
		]
		end_date: DF.Date | None
		fulfilment_deadline: DF.Date | None
		fulfilment_status: DF.Literal["N/A", "Unfulfilled", "Partially Fulfilled", "Fulfilled", "Lapsed"]
		fulfilment_terms: DF.Table[IDPContractFulfilmentChecklist]
		ip_address: DF.Data | None
		is_signed: DF.Check
		party_name: DF.DynamicLink
		party_type: DF.Literal["Customer", "Supplier", "Employee"]
		party_user: DF.Link | None
		requires_fulfilment: DF.Check
		signed_by_company: DF.Link | None
		signed_on: DF.Datetime | None
		signee: DF.Data | None
		start_date: DF.Date | None
		status: DF.Link | None
	# end: auto-generated types

	def get_financial_year_short(self):
		fy = get_fiscal_year(nowdate(), as_dict=True)
		start_year = fy['year_start_date'].year % 100
		end_year = fy['year_end_date'].year % 100
		return f"{start_year:02d}{end_year:02d}"  # e.g., 2526

	def get_client_code(self):
		return ''.join(filter(str.isalpha, self.party_name.upper()))[:3]  # Only alphabets, 3 letters


	def autoname(self):
		fy_code = self.get_financial_year_short()

		# Sequence will reset per fiscal year
		sequence = make_autoname(f"{fy_code}/.####")

		if not self.party_name:
			frappe.throw("Customer name is required to generate document name")

		client_code = self.get_client_code()
		self.name = f"{sequence}/{client_code}"


	def validate(self):
		self.update_values()
		self.validate_percentage()
		self.validate_dates()
		self.update_fulfilment_status()
		self.validate_milestones()

	def update_values(self):
		payment_status = get_status_id_from_status_name("MIlestone Payment", "Not Due")
		due_status = get_status_id_from_status_name("MIlestone Payment", "Due")
		for milestone in self.milestones:
			if not milestone.payment_status:
				milestone.payment_status = payment_status

			if milestone.payment_pending != milestone.fee_amount - (milestone.payment_received or 0):
				milestone.payment_pending = milestone.fee_amount - (milestone.payment_received or 0)
			if milestone.client_approval_date and milestone.payment_status == payment_status:
				milestone.payment_status = due_status
		

	def validate_milestones(self):
		# Do not allow removal of milestones from child table once project is created
		projects = frappe.get_all("IDP Project", filters={"contract": self.name}, page_length=1)
		if projects:
			if not self.is_new():
				# Fetch old doc to compare
				old = self.get_doc_before_save()
				old_children = {d.name for d in old.milestones}
				new_children = {d.name for d in self.milestones if d.name}

				deleted_rows = old_children - new_children
				if deleted_rows:
					frappe.throw("Deleting milestones after project creation is not allowed.")

		for row in self.milestones:
			if self.end_date and frappe.utils.getdate(row.contract_end_date) > frappe.utils.getdate(self.end_date):
				frappe.throw("Milestone end date cannot be greater than the contract end date.")
		
	def validate_percentage(self):
		update_fee = False
		if not self.is_new():
			old_doc = self.get_doc_before_save()
			if old_doc.project_value != self.project_value:
				# If project value updated, update the fees as well
				update_fee = True
		milestone_total_fee_amount = 0
		milestone_total_fee_per = 0
		errors = ''
		per_fields = ['fee_', 'completion_']
		for milestone in self.milestones:
			if update_fee:
				milestone.fee_amount = milestone.fee_ * self.project_value / 100
			milestone_total_fee_amount += milestone.fee_amount
			milestone_total_fee_per += milestone.fee_
			for field in per_fields:
				if milestone.get(field) and (milestone.get(field) > 100 or milestone.get(field) < 0):
					field_label = get_label_from_fieldname(milestone.doctype, field)
					errors +=(f"Row #{milestone.idx} {milestone.milestone} {field_label} {milestone.get(field)} should be between 0 and 100 </br>")

		if milestone_total_fee_amount > float(self.project_value):
			errors += f"Total milestone fee amount {milestone_total_fee_amount} is greater than project value {self.project_value} </br>"
		
		if milestone_total_fee_per > 100:
			errors += f"Total milestone fee percentage {milestone_total_fee_per} is greater than 100 </br>"

		if errors:
			frappe.throw(errors)

	def on_update(self):
		if not self.is_new():
			old_doc = self.get_doc_before_save()
			new_rows = []
			if old_doc:
				old_names = {d.name for d in old_doc.milestones}
				new_rows = [d for d in self.milestones if not d.name or d.name not in old_names]

			if new_rows:
				create_specific_milestones(self.name, new_rows)
				frappe.msgprint("Milestones created in projects")

	def before_submit(self):
		self.signed_by_company = frappe.session.user

	def before_update_after_submit(self):
		self.update_fulfilment_status()

	def validate_dates(self):
		if self.end_date and self.end_date < self.start_date:
			frappe.throw(_("End Date cannot be before Start Date."))

	def update_fulfilment_status(self):
		fulfilment_status = "N/A"

		if self.requires_fulfilment:
			fulfilment_progress = self.get_fulfilment_progress()

			if not fulfilment_progress:
				fulfilment_status = "Unfulfilled"
			elif fulfilment_progress < len(self.fulfilment_terms):
				fulfilment_status = "Partially Fulfilled"
			elif fulfilment_progress == len(self.fulfilment_terms):
				fulfilment_status = "Fulfilled"

			if fulfilment_status != "Fulfilled" and self.fulfilment_deadline:
				now_date = getdate(nowdate())
				deadline_date = getdate(self.fulfilment_deadline)

				if now_date > deadline_date:
					fulfilment_status = "Lapsed"

		self.fulfilment_status = fulfilment_status

	def get_fulfilment_progress(self):
		return len([term for term in self.fulfilment_terms if term.fulfilled])


def get_status(start_date, end_date):
	"""
	Get a Contract's status based on the start, current and end dates

	Args:
	        start_date (str): The start date of the contract
	        end_date (str): The end date of the contract

	Returns:
	        str: 'Active' if within range, otherwise 'Inactive'
	"""

	if not end_date:
		return "Active"

	start_date = getdate(start_date)
	end_date = getdate(end_date)
	now_date = getdate(nowdate())

	return "Active" if start_date <= now_date <= end_date else "Inactive"


def update_status_for_contracts():
	"""
	Run the daily hook to update the statuses for all signed
	and submitted Contracts
	"""

	contracts = frappe.get_all(
		"Contract",
		filters={"is_signed": True, "docstatus": 1},
		fields=["name", "start_date", "end_date"],
	)

	for contract in contracts:
		status = get_status(contract.get("start_date"), contract.get("end_date"))

		frappe.db.set_value("IDP Contract", contract.get("name"), "status", status)


def update_milestone_payment_status(milestone_id):
    """
    Update payment status for milestone based on invoice and payment conditions
    Args:
        milestone_id: ID of the milestone
    """
    # Get milestone details
    milestone = frappe.get_doc("IDP Milestones", milestone_id)
    
    # Check if invoice exists for this milestone
    invoice_exists = frappe.db.exists(
        "Sales Invoice Item",
        {
            "milestone": milestone_id,
            "docstatus": 1
        }
    )
    
    if not invoice_exists:
        # No invoice raised - check completion
        if milestone.completion_ >= 100:
            payment_status = get_status_id_from_status_name("MIlestone Payment", "Due")
        else:
            payment_status = get_status_id_from_status_name("MIlestone Payment", "Not Due")
    else:
        # Invoice raised - check payment status
        if not milestone.payment_received:
            payment_status = get_status_id_from_status_name("MIlestone Payment", "Payment Pending")
        elif milestone.payment_received < milestone.fee_amount:
            payment_status = get_status_id_from_status_name("MIlestone Payment", "Partial Received")
        else:
            payment_status = get_status_id_from_status_name("MIlestone Payment", "Received")
    
    # Update payment status if changed
    if milestone.payment_status != payment_status:
        frappe.db.set_value(
            "IDP Milestones",
            milestone_id,
            "payment_status",
            payment_status,
            update_modified=False
        )
