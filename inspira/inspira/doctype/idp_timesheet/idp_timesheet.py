# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

from datetime import timedelta

import frappe
from erpnext.controllers.queries import get_match_cond
from erpnext.setup.utils import get_exchange_rate
from frappe import _
from frappe.model.document import Document
from frappe.utils import add_to_date, flt, get_datetime, time_diff_in_hours

from inspira.inspira.custom.utils import get_hrs_from_timedelta, time_to_seconds


class OverlapError(frappe.ValidationError):
	pass


class OverWorkLoggedError(frappe.ValidationError):
	pass


class IDPTimesheet(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		from inspira.inspira.doctype.idp_timesheet_detail.idp_timesheet_detail import (
			IDPTimesheetDetail,
		)

		amended_from: DF.Link | None
		base_total_billable_amount: DF.Currency
		base_total_billed_amount: DF.Currency
		base_total_costing_amount: DF.Currency
		company: DF.Link | None
		currency: DF.Link | None
		customer: DF.Link | None
		department: DF.Link | None
		employee: DF.Link | None
		employee_name: DF.Data | None
		end_date: DF.Date | None
		exchange_rate: DF.Float
		naming_series: DF.Literal["TS-.YYYY.-"]
		note: DF.TextEditor | None
		parent_project: DF.Link | None
		per_billed: DF.Percent
		sales_invoice: DF.Link | None
		start_date: DF.Date | None
		status: DF.Literal["Draft", "Submitted", "Billed", "Payslip", "Completed", "Cancelled"]
		time_logs: DF.Table[IDPTimesheetDetail]
		title: DF.Data | None
		total_billable_amount: DF.Currency
		total_billable_hours: DF.Float
		total_billed_amount: DF.Currency
		total_billed_hours: DF.Float
		total_costing_amount: DF.Currency
		total_hours: DF.Float
		user: DF.Link | None
	# end: auto-generated types

	def validate(self):
		if not self.employee:
			frappe.throw("Please set employee")
		if not self.designation:
			frappe.throw("Designation not set for Employee {self.employee} - {self.employee_name}")
		self.set_status()
		self.validate_dates()
		self.calculate_hours()
		self.populate_time_logs()
		# self.update_efforts_in_project()
		self.validate_time_logs()
		self.update_cost()
		self.calculate_total_amounts()
		self.calculate_percentage_billed()
		self.set_dates()

		if self.docstatus == 0:
			for wt in self.weekly_timesheets:
				filters = {
					"parent": wt.project,
					"designation": self.designation,
				}
				budgeted_hours = frappe.db.get_value("IDP Designation Project Effort Mapping", filters, "pending_no_of_hours")
				if budgeted_hours:
					wt.budgeted_hours = budgeted_hours

	def populate_time_logs(self):
		days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
		if self.is_new() or not self.is_child_table_same("weekly_timesheets"):
			self.time_logs = []
			for wt in self.weekly_timesheets:
				wt.total_hours = 0

				for index, day in enumerate(days):
					hours = time_to_seconds(getattr(wt, day) or "00:00:00")
					wt.total_hours += get_hrs_from_timedelta(hours)
					if hours:
						# time is represented in a 24-hour format, but it goes from 00:00:00 to 23:59:59
						if hours > time_to_seconds("23:59:59"):
							frappe.throw(f"Weekly Timesheet has more than 24 hours for {day.title()}")
						self.append(
							"time_logs",
							{
								"project": wt.project,
								"from_time": add_to_date(wt.from_date, days=index),
								"hours": hours,
							},
						)

	def update_efforts_in_project(self):
		if self.docstatus == 1:
			for wt in self.weekly_timesheets:
				if wt.project:
					project_doc = frappe.get_doc("IDP Project", wt.project)
					for em in project_doc.effort_mapping:
						if em.designation == self.designation:
							em.actual_no_of_hours_worked += wt.total_hours
							em.pending_no_of_hours = em.expected_no_of_hours - em.actual_no_of_hours_worked
							project_doc.save(ignore_permissions=True)
							wt.budgeted_hours = em.pending_no_of_hours
							self.save(ignore_permissions=True) # Since this is ran after submit
							break

					user_found = False
					total_costing = 0
					if not project_doc.is_billable:
						continue
					for employee_milestone_costing in project_doc.employee_milestone_costing:
						if employee_milestone_costing.employee_id == self.employee and employee_milestone_costing.milestone == wt.milestone:
							if not employee_milestone_costing.employee_id:
								employee_milestone_costing.employee_id = self.employee

							# if not employee_milestone_costing.hourly_seat_cost:
							# Fetching user_id and hourly_seat_cost from Employee
							user_id, hourly_seat_cost = None, 0
							res = frappe.db.get_value("Employee", self.employee, ["user_id", "hourly_seat_cost"])
							if res:
								user_id, hourly_seat_cost = res
							employee_milestone_costing.user_id = user_id
							employee_milestone_costing.hourly_seat_cost = hourly_seat_cost

							employee_milestone_costing.timesheet_hrs += wt.total_hours
							employee_milestone_costing.total_amount = (
								employee_milestone_costing.timesheet_hrs
								* employee_milestone_costing.hourly_seat_cost
							)
							project_doc.save(ignore_permissions=True)
							total_costing = employee_milestone_costing.total_amount
							user_found = True
							break

					if not user_found:
						user_id, hourly_seat_cost = None, 0
						res = frappe.db.get_value(
							"Employee", self.employee, ["user_id", "hourly_seat_cost"]
						)
						if res:
							user_id, hourly_seat_cost = res

						project_doc.append(
							"employee_milestone_costing",
							{
								"user_id": user_id,
								"employee_id": self.employee,
								"milestone": wt.milestone,
								"hourly_seat_cost": hourly_seat_cost,
								"timesheet_hrs": wt.total_hours,
								"total_amount": wt.total_hours * hourly_seat_cost,
							},
						)
						project_doc.save(ignore_permissions=True)
					
					total_costing = 0
					for employee_milestone_costing in project_doc.employee_milestone_costing:
						if employee_milestone_costing.total_amount:
							total_costing += employee_milestone_costing.total_amount

					if total_costing and project_doc.contract:
						contract_doc = frappe.get_doc("IDP Contract", project_doc.contract)
						for milestone in contract_doc.milestones:
							if milestone.idp_task == wt.milestone:
								milestone.cost_incurred = total_costing
								milestone.payment_pending = total_costing
								break
						contract_doc.save(ignore_permissions=True)


		# if self.is_new() or not self.is_child_table_same("weekly_timesheets"):
		# 	previous = self.get_doc_before_save()
		# 	prev_project_wise_worked_hrs = {}
		# 	cur_project_wise_worked_hrs = {}

		# 	# Calculate current project hours
		# 	for wt in self.weekly_timesheets:
		# 		if wt.project not in cur_project_wise_worked_hrs:
		# 			cur_project_wise_worked_hrs[wt.project] = wt.total_hours
		# 		else:
		# 			cur_project_wise_worked_hrs[wt.project] += wt.total_hours

		# 	# Calculate previous project hours
		# 	if previous:
		# 		for wt in previous.weekly_timesheets:
		# 			if wt.project not in prev_project_wise_worked_hrs:
		# 				prev_project_wise_worked_hrs[wt.project] = wt.total_hours
		# 			else:
		# 				prev_project_wise_worked_hrs[wt.project] += wt.total_hours

		# 	# Handle both removed and added/modified projects
		# 	all_projects = set(
		# 		list(prev_project_wise_worked_hrs.keys()) + list(cur_project_wise_worked_hrs.keys())
		# 	)

		# 	for project in all_projects:
		# 		prev_hours = prev_project_wise_worked_hrs.get(project, 0)
		# 		cur_hours = cur_project_wise_worked_hrs.get(project, 0)
		# 		hours_diff = cur_hours - prev_hours

		# 		if hours_diff != 0:  # Only process if there's a change
		# 			project_doc = frappe.get_doc("IDP Project", project)
		# 			for em in project_doc.effort_mapping:
		# 				if em.designation == self.designation:
		# 					em.actual_no_of_hours_worked += hours_diff
		# 					em.pending_no_of_hours = em.expected_no_of_hours - em.actual_no_of_hours_worked
		# 					# Commenting this check as per discussion with Abhas
		# 					# Check if pending hours is negative then throw error
		# 					# if em.pending_no_of_hours < 0:
		# 					# 	frappe.throw(
		# 					# 		f"Pending hours for {em.designation} in project {project} is negative {em.pending_no_of_hours}"
		# 					# 	)
		# 					for wt in self.weekly_timesheets:
		# 						if wt.project == project:
		# 							wt.budgeted_hours = em.pending_no_of_hours
		# 							break
		# 					project_doc.save(ignore_permissions=True)
		# 					break

	def calculate_hours(self):
		for row in self.time_logs:
			if row.to_time and row.from_time:
				row.hours = time_diff_in_hours(row.to_time, row.from_time)

	def calculate_total_amounts(self):
		self.total_hours = 0.0
		self.total_billable_hours = 0.0
		self.total_billed_hours = 0.0
		self.total_billable_amount = self.base_total_billable_amount = 0.0
		self.total_costing_amount = self.base_total_costing_amount = 0.0
		self.total_billed_amount = self.base_total_billed_amount = 0.0

		for d in self.get("time_logs"):
			self.update_billing_hours(d)
			self.update_time_rates(d)

			self.total_hours += flt(d.hours)
			self.total_costing_amount += flt(d.costing_amount)
			self.base_total_costing_amount += flt(d.base_costing_amount)
			if d.is_billable:
				self.total_billable_hours += flt(d.billing_hours)
				self.total_billable_amount += flt(d.billing_amount)
				self.base_total_billable_amount += flt(d.base_billing_amount)
				self.total_billed_amount += flt(d.billing_amount) if d.sales_invoice else 0.0
				self.base_total_billed_amount += flt(d.base_billing_amount) if d.sales_invoice else 0.0
				self.total_billed_hours += flt(d.billing_hours) if d.sales_invoice else 0.0

	def calculate_percentage_billed(self):
		self.per_billed = 0
		if self.total_billed_amount > 0 and self.total_billable_amount > 0:
			self.per_billed = (self.total_billed_amount * 100) / self.total_billable_amount
		elif self.total_billed_hours > 0 and self.total_billable_hours > 0:
			self.per_billed = (self.total_billed_hours * 100) / self.total_billable_hours

	def update_billing_hours(self, args):
		if args.is_billable:
			if flt(args.billing_hours) == 0.0:
				args.billing_hours = args.hours
			elif flt(args.billing_hours) > flt(args.hours):
				frappe.msgprint(
					_("Warning - Row {0}: Billing Hours are more than Actual Hours").format(args.idx),
					indicator="orange",
					alert=True,
				)
		else:
			args.billing_hours = 0

	def set_status(self):
		self.status = {"0": "Draft", "1": "Submitted", "2": "Cancelled"}[str(self.docstatus or 0)]

		if flt(self.per_billed, self.precision("per_billed")) >= 100.0:
			self.status = "Billed"

		if self.sales_invoice:
			self.status = "Completed"

	def set_dates(self):
		if self.docstatus < 2 and self.time_logs:
			start_date = min(d.from_date for d in self.weekly_timesheets)
			end_date = max(d.to_date for d in self.weekly_timesheets)

			if start_date and end_date:
				self.start_date = start_date
				self.end_date = end_date

	def before_cancel(self):
		self.set_status()

	def on_cancel(self):
		self.update_task_and_project()
		
		for wt in self.weekly_timesheets:
			if wt.project:
				project_doc = frappe.get_doc("IDP Project", wt.project)
				for em in project_doc.effort_mapping:
					if em.designation == self.designation:
						em.actual_no_of_hours_worked -= wt.total_hours
						em.pending_no_of_hours = em.expected_no_of_hours - em.actual_no_of_hours_worked
						project_doc.save(ignore_permissions=True)
						break

		total_costing = 0
		for employee_milestone_costing in project_doc.employee_milestone_costing:
			if employee_milestone_costing.employee_id == self.employee and employee_milestone_costing.milestone == wt.milestone:
				employee_milestone_costing.timesheet_hrs -= wt.total_hours
				total_costing = employee_milestone_costing.total_amount
				employee_milestone_costing.total_amount = (
					employee_milestone_costing.timesheet_hrs
					* employee_milestone_costing.hourly_seat_cost
				)
				project_doc.save(ignore_permissions=True)
				break

		if project_doc.contract:
			contract_doc = frappe.get_doc("IDP Contract", project_doc.contract)
			for milestone in contract_doc.milestones:
				if milestone.idp_task == wt.milestone:
					milestone.cost_incurred -= total_costing
					milestone.payment_pending -= total_costing
					break
			contract_doc.save(ignore_permissions=True)

	def on_submit(self):
		self.validate_mandatory_fields()
		self.update_task_and_project()
		self.update_efforts_in_project()
		self.update_shift()


	def update_shift(self):
		shift_hours = 0
		shift = frappe.db.get_value("Employee", self.employee, "default_shift")
		if shift:
			shift_doc = frappe.get_doc("Shift Type", shift)
			shift_hours = time_diff_in_hours(shift_doc.end_time, shift_doc.start_time)

		for row in self.time_logs:
			if shift:
				row.shift = shift
				row.shift_hours = shift_hours

	def validate_mandatory_fields(self):
		for data in self.time_logs:
			if not data.from_time and not data.to_time:
				frappe.throw(_("Row {0}: From Time and To Time is mandatory.").format(data.idx))

			# if not data.activity_type and self.employee:
			# 	frappe.throw(_("Row {0}: Activity Type is mandatory.").format(data.idx))

			if flt(data.hours) == 0.0:
				frappe.throw(_("Row {0}: Hours value must be greater than zero.").format(data.idx))

	def update_task_and_project(self):
		tasks, projects = [], []

		for data in self.time_logs:
			if data.task and data.task not in tasks:
				task = frappe.get_doc("IDP Task", data.task)
				task.update_time_and_costing()
				task.save(ignore_permissions=True)
				tasks.append(data.task)

			if data.project and data.project not in projects:
				projects.append(data.project)

		for project in projects:
			project_doc = frappe.get_doc("IDP Project", project)
			project_doc.update_project()
			project_doc.save(ignore_permissions=True)

	def validate_dates(self):
		for data in self.time_logs:
			if data.from_time and data.to_time and time_diff_in_hours(data.to_time, data.from_time) < 0:
				frappe.throw(_("To date cannot be before from date"))

	def validate_time_logs(self):
		for data in self.get("time_logs"):
			self.set_to_time(data)
			# Disabling Overlap check as per discussion with Abhas
			# self.validate_overlap(data)
			self.set_project(data)
			self.validate_project(data)

	def set_to_time(self, data):
		if not (data.from_time and data.hours):
			return

		if isinstance(data.hours, timedelta):
			data.hours = get_hrs_from_timedelta(data.hours)

		_to_time = get_datetime(add_to_date(data.from_time, hours=data.hours, as_datetime=True))
		if data.to_time != _to_time:
			data.to_time = _to_time

	def validate_overlap(self, data):
		settings = frappe.get_single("Projects Settings")
		self.validate_overlap_for("user", data, self.user, settings.ignore_user_time_overlap)
		self.validate_overlap_for("employee", data, self.employee, settings.ignore_employee_time_overlap)

	def set_project(self, data):
		data.project = data.project or frappe.db.get_value("IDP Task", data.task, "project")

	def validate_project(self, data):
		if self.parent_project and self.parent_project != data.project:
			frappe.throw(
				_("Row {0}: Project must be same as the one set in the Timesheet: {1}.").format(
					data.idx, self.parent_project
				)
			)

	def validate_overlap_for(self, fieldname, args, value, ignore_validation=False):
		if not value or ignore_validation:
			return

		existing = self.get_overlap_for(fieldname, args, value)
		if existing:
			frappe.throw(
				_("Row {0}: From Time and To Time of {1} is overlapping with {2}").format(
					args.idx, self.name, existing.name
				),
				OverlapError,
			)

	def get_overlap_for(self, fieldname, args, value):
		timesheet = frappe.qb.DocType("IDP Timesheet")
		timelog = frappe.qb.DocType("IDP Timesheet Detail")

		from_time = get_datetime(args.from_time)
		to_time = get_datetime(args.to_time)

		existing = (
			frappe.qb.from_(timesheet)
			.join(timelog)
			.on(timelog.parent == timesheet.name)
			.select(
				timesheet.name.as_("name"),
				timelog.from_time.as_("from_time"),
				timelog.to_time.as_("to_time"),
			)
			.where(
				(timelog.name != (args.name or "No Name"))
				& (timesheet.name != (args.parent or "No Name"))
				& (timesheet.docstatus < 2)
				& (timesheet[fieldname] == value)
				& (
					((from_time > timelog.from_time) & (from_time < timelog.to_time))
					| ((to_time > timelog.from_time) & (to_time < timelog.to_time))
					| ((from_time <= timelog.from_time) & (to_time >= timelog.to_time))
				)
			)
		).run(as_dict=True)

		if self.check_internal_overlap(fieldname, args):
			return self

		return existing[0] if existing else None

	def check_internal_overlap(self, fieldname, args):
		for time_log in self.time_logs:
			if not (time_log.from_time and time_log.to_time and args.from_time and args.to_time):
				continue

			from_time = get_datetime(time_log.from_time)
			to_time = get_datetime(time_log.to_time)
			args_from_time = get_datetime(args.from_time)
			args_to_time = get_datetime(args.to_time)

			if (
				(args.get(fieldname) == time_log.get(fieldname))
				and (args.idx != time_log.idx)
				and (
					(args_from_time > from_time and args_from_time < to_time)
					or (args_to_time > from_time and args_to_time < to_time)
					or (args_from_time <= from_time and args_to_time >= to_time)
				)
			):
				return True
		return False

	def update_cost(self):
		for data in self.time_logs:
			if data.activity_type or data.is_billable:
				rate = get_activity_cost(self.employee, data.activity_type)
				hours = data.billing_hours or 0
				costing_hours = data.billing_hours or data.hours or 0
				if rate:
					data.billing_rate = (
						flt(rate.get("billing_rate")) if flt(data.billing_rate) == 0 else data.billing_rate
					)
					data.costing_rate = (
						flt(rate.get("costing_rate")) if flt(data.costing_rate) == 0 else data.costing_rate
					)
					data.billing_amount = data.billing_rate * hours
					data.costing_amount = data.costing_rate * costing_hours

	def update_time_rates(self, ts_detail):
		if not ts_detail.is_billable:
			ts_detail.billing_rate = 0.0


@frappe.whitelist()
def get_projectwise_timesheet_data(project=None, parent=None, from_time=None, to_time=None):
	condition = ""
	if project:
		condition += "AND tsd.project = %(project)s "
	if parent:
		condition += "AND tsd.parent = %(parent)s "
	if from_time and to_time:
		condition += "AND CAST(tsd.from_time as DATE) BETWEEN %(from_time)s AND %(to_time)s"

	query = f"""
		SELECT
			tsd.name as name,
			tsd.parent as time_sheet,
			tsd.from_time as from_time,
			tsd.to_time as to_time,
			tsd.billing_hours as billing_hours,
			tsd.billing_amount as billing_amount,
			tsd.activity_type as activity_type,
			tsd.description as description,
			ts.currency as currency,
			tsd.project_name as project_name
		FROM `tabIDP Timesheet Detail` tsd
			INNER JOIN `tabIDP Timesheet` ts
			ON ts.name = tsd.parent
		WHERE
			tsd.parenttype = 'IDP Timesheet'
			AND tsd.docstatus = 1
			AND tsd.is_billable = 1
			AND tsd.sales_invoice is NULL
			{condition}
		ORDER BY tsd.from_time ASC
	"""

	filters = {
		"project": project,
		"parent": parent,
		"from_time": from_time,
		"to_time": to_time,
	}

	return frappe.db.sql(query, filters, as_dict=1)


@frappe.whitelist()
def get_timesheet_detail_rate(timelog, currency):
	timelog_detail = frappe.db.sql(
		f"""SELECT tsd.billing_amount as billing_amount,
		ts.currency as currency FROM `tabIDP Timesheet Detail` tsd
		INNER JOIN `tabIDP Timesheet` ts ON ts.name=tsd.parent
		WHERE tsd.name = '{timelog}'""",
		as_dict=1,
	)[0]

	if timelog_detail.currency:
		exchange_rate = get_exchange_rate(timelog_detail.currency, currency)

		return timelog_detail.billing_amount * exchange_rate
	return timelog_detail.billing_amount


@frappe.whitelist()
@frappe.validate_and_sanitize_search_inputs
def get_timesheet(doctype, txt, searchfield, start, page_len, filters):
	if not filters:
		filters = {}

	condition = ""
	if filters.get("project"):
		condition = "and tsd.project = %(project)s"

	return frappe.db.sql(
		f"""select distinct tsd.parent from `tabIDP Timesheet Detail` tsd,
			`tabIDP Timesheet` ts where
			ts.status in ('Submitted', 'Payslip') and tsd.parent = ts.name and
			tsd.docstatus = 1 and ts.total_billable_amount > 0
			and tsd.parent LIKE %(txt)s {condition}
			order by tsd.parent limit %(page_len)s offset %(start)s""",
		{
			"txt": "%" + txt + "%",
			"start": start,
			"page_len": page_len,
			"project": filters.get("project"),
		},
	)


@frappe.whitelist()
def get_timesheet_data(name, project):
	data = None
	if project and project != "":
		data = get_projectwise_timesheet_data(project, name)
	else:
		data = frappe.get_all(
			"IDP Timesheet",
			fields=[
				"(total_billable_amount - total_billed_amount) as billing_amt",
				"total_billable_hours as billing_hours",
			],
			filters={"name": name},
		)
	return {
		"billing_hours": data[0].billing_hours if data else None,
		"billing_amount": data[0].billing_amt if data else None,
		"timesheet_detail": data[0].name if data and project and project != "" else None,
	}


@frappe.whitelist()
def make_sales_invoice(source_name, item_code=None, customer=None, currency=None):
	target = frappe.new_doc("Sales Invoice")
	timesheet = frappe.get_doc("IDP Timesheet", source_name)

	if not timesheet.total_billable_hours:
		frappe.throw(_("Invoice can't be made for zero billing hour"))

	if timesheet.total_billable_hours == timesheet.total_billed_hours:
		frappe.throw(_("Invoice already created for all billing hours"))

	hours = flt(timesheet.total_billable_hours) - flt(timesheet.total_billed_hours)
	billing_amount = flt(timesheet.total_billable_amount) - flt(timesheet.total_billed_amount)
	billing_rate = billing_amount / hours

	target.company = timesheet.company
	target.project = timesheet.parent_project
	if customer:
		target.customer = customer
		default_price_list = frappe.get_value("Customer", customer, "default_price_list")
		if default_price_list:
			target.selling_price_list = default_price_list

	if currency:
		target.currency = currency

	if item_code:
		target.append("items", {"item_code": item_code, "qty": hours, "rate": billing_rate})

	for time_log in timesheet.time_logs:
		if time_log.is_billable:
			target.append(
				"timesheets",
				{
					"time_sheet": timesheet.name,
					"project_name": time_log.project_name,
					"from_time": time_log.from_time,
					"to_time": time_log.to_time,
					"billing_hours": time_log.billing_hours,
					"billing_amount": time_log.billing_amount,
					"timesheet_detail": time_log.name,
					"activity_type": time_log.activity_type,
					"description": time_log.description,
				},
			)

	target.run_method("calculate_billing_amount_for_timesheet")
	target.run_method("set_missing_values")

	return target


@frappe.whitelist()
def get_activity_cost(employee=None, activity_type=None, currency=None):
	base_currency = frappe.defaults.get_global_default("currency")
	rate = frappe.db.get_values(
		"Activity Cost",
		{"employee": employee, "activity_type": activity_type},
		["costing_rate", "billing_rate"],
		as_dict=True,
	)
	if not rate:
		rate = frappe.db.get_values(
			"Activity Type",
			{"activity_type": activity_type},
			["costing_rate", "billing_rate"],
			as_dict=True,
		)
		if rate and currency and currency != base_currency:
			exchange_rate = get_exchange_rate(base_currency, currency)
			rate[0]["costing_rate"] = rate[0]["costing_rate"] * exchange_rate
			rate[0]["billing_rate"] = rate[0]["billing_rate"] * exchange_rate

	return rate[0] if rate else {}


@frappe.whitelist()
def get_events(start, end, filters=None):
	"""Returns events for Gantt / Calendar view rendering.
	:param start: Start date-time.
	:param end: End date-time.
	:param filters: Filters (JSON).
	"""
	filters = frappe.parse_json(filters)
	from frappe.desk.calendar import get_event_conditions

	conditions = get_event_conditions("IDP Timesheet", filters)

	return frappe.db.sql(
		"""select `tabIDP Timesheet Detail`.name as name,
			`tabIDP Timesheet Detail`.docstatus as status, `tabIDP Timesheet Detail`.parent as parent,
			from_time as start_date, hours, activity_type,
			`tabIDP Timesheet Detail`.project, to_time as end_date,
			CONCAT(`tabIDP Timesheet Detail`.parent, ' (', ROUND(hours,2),' hrs)') as title
		from `tabIDP Timesheet Detail`, `tabIDP Timesheet`
		where `tabIDP Timesheet Detail`.parent = `tabIDP Timesheet`.name
			and `tabIDP Timesheet`.docstatus < 2
			and (from_time <= %(end)s and to_time >= %(start)s) {conditions} {match_cond}
		""".format(conditions=conditions, match_cond=get_match_cond("IDP Timesheet")),
		{"start": start, "end": end},
		as_dict=True,
		update={"allDay": 0},
	)


def get_timesheets_list(doctype, txt, filters, limit_start, limit_page_length=20, order_by="modified"):
	user = frappe.session.user
	# find customer name from contact.
	customer = ""
	timesheets = []

	contact = frappe.db.exists("Contact", {"user": user})
	if contact:
		# find customer
		contact = frappe.get_doc("Contact", contact)
		customer = contact.get_link_for("Customer")

	if customer:
		sales_invoices = [
			d.name for d in frappe.get_all("Sales Invoice", filters={"customer": customer})
		] or [None]
		projects = [d.name for d in frappe.get_all("IDP Project", filters={"customer": customer})]
		# Return timesheet related data to web portal.
		timesheets = frappe.db.sql(
			f"""
			SELECT
				ts.name, tsd.activity_type, ts.status, ts.total_billable_hours,
				COALESCE(ts.sales_invoice, tsd.sales_invoice) AS sales_invoice, tsd.project
			FROM `tabIDP Timesheet` ts, `tabIDP Timesheet Detail` tsd
			WHERE tsd.parent = ts.name AND
				(
					ts.sales_invoice IN %(sales_invoices)s OR
					tsd.sales_invoice IN %(sales_invoices)s OR
					tsd.project IN %(projects)s
				)
			ORDER BY `end_date` ASC
			LIMIT {limit_page_length} offset {limit_start}
		""",
			dict(sales_invoices=sales_invoices, projects=projects),
			as_dict=True,
		)  # nosec

	return timesheets


def get_list_context(context=None):
	return {
		"show_sidebar": True,
		"show_search": True,
		"no_breadcrumbs": True,
		"title": _("Timesheets"),
		"get_list": get_timesheets_list,
		"row_template": "templates/includes/timesheet/timesheet_row.html",
	}
