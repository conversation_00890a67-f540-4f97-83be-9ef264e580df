<h5 style="margin-top: 0px;">Activity Summary</h5>
<h6 style="margin-bottom: 25px;">{{ __("Total hours: {0}", [flt(sum, 2) ]) }}</h6>
{% for d in data %}
<div class="row">
	<div class="col-xs-4">
		<a class="small time-sheet-link" data-activity_type="{{ d.activity_type || "" }}">
			{{ d.activity_type || __("Unknown") }}</a>
	</div>
	<div class="col-xs-8">
		<span class="inline-graph">
			<span class="inline-graph-half">
			</span>
			<span class="inline-graph-half" title="{{ __("hours") }}">
				<span class="inline-graph-count">
					{{ __("{0} hours", [flt(d.total_hours, 2)]) }}
				</span>
				<span class="inline-graph-bar">
					<span class="inline-graph-bar-inner dark"
						style="width: {{ cint(d.total_hours/max_count * 100) }}%">
					</span>
				</span>
			</span>
		</span>
	</div>
</div>
{% endfor %}
