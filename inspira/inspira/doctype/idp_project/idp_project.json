{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-01-25 12:38:05.874017", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["naming_series", "project_name", "status", "project_type", "is_active", "percent_complete_method", "percent_complete", "completion_date", "column_break_5", "project_template", "expected_start_date", "expected_end_date", "priority", "department", "contract", "project_value", "section_break_izix", "project_area", "customer_details", "customer", "column_break_14", "sales_order", "users_section", "users", "copied_from", "effort_mapping", "employee_milestone_costing", "section_break0", "notes", "section_break_18", "actual_start_date", "actual_time", "column_break_20", "actual_end_date", "project_details", "estimated_costing", "total_costing_amount", "total_purchase_cost", "company", "is_billable", "column_break_28", "total_sales_amount", "total_billable_amount", "total_billed_amount", "total_consumed_material_cost", "cost_center", "margin", "gross_margin", "column_break_37", "per_gross_margin", "monitor_progress", "collect_progress", "holiday_list", "frequency", "from_time", "to_time", "first_email", "second_email", "daily_time_to_send", "day_to_send", "weekly_time_to_send", "column_break_45", "message"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "options": "PROJ-.####", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "project_name", "fieldtype": "Data", "label": "Project Name", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"default": "Open", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "Open\nCompleted\nCancelled", "search_index": 1}, {"fieldname": "project_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Project Type", "oldfieldname": "project_type", "oldfieldtype": "Data", "options": "IDP Project Type"}, {"fieldname": "is_active", "fieldtype": "Select", "label": "Is Active", "oldfieldname": "is_active", "oldfieldtype": "Select", "options": "Yes\nNo"}, {"default": "Task Completion", "fieldname": "percent_complete_method", "fieldtype": "Select", "label": "% Complete Method", "options": "Manual\nTask Completion\nTask Progress\nTask Weight"}, {"bold": 1, "fieldname": "percent_complete", "fieldtype": "Percent", "label": "% Completed", "no_copy": 1, "precision": "2", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"allow_in_quick_entry": 1, "fieldname": "project_template", "fieldtype": "Link", "label": "From Template", "options": "Project Template", "set_only_once": 1}, {"bold": 1, "fieldname": "expected_start_date", "fieldtype": "Date", "label": "Expected Start Date", "oldfieldname": "project_start_date", "oldfieldtype": "Date"}, {"bold": 1, "fieldname": "expected_end_date", "fieldtype": "Date", "in_list_view": 1, "label": "Expected End Date", "oldfieldname": "completion_date", "oldfieldtype": "Date"}, {"fieldname": "priority", "fieldtype": "Select", "in_standard_filter": 1, "label": "Priority", "oldfieldname": "priority", "oldfieldtype": "Select", "options": "Medium\nLow\nHigh"}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Department"}, {"collapsible": 1, "fieldname": "customer_details", "fieldtype": "Section Break", "label": "Customer Details", "oldfieldtype": "Section Break", "options": "fa fa-user"}, {"fieldname": "customer", "fieldtype": "Link", "in_global_search": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "search_index": 1}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"fieldname": "sales_order", "fieldtype": "Link", "label": "Sales Order", "options": "Sales Order"}, {"collapsible": 1, "fieldname": "users_section", "fieldtype": "Section Break", "label": "Users"}, {"description": "Project will be accessible on the website to these users", "fieldname": "users", "fieldtype": "Table", "label": "Users", "options": "IDP Project User"}, {"fieldname": "copied_from", "fieldtype": "Data", "hidden": 1, "label": "Copied From", "read_only": 1}, {"collapsible": 1, "fieldname": "section_break0", "fieldtype": "Section Break", "label": "Notes", "oldfieldtype": "Section Break", "options": "fa fa-list"}, {"fieldname": "notes", "fieldtype": "Text Editor", "label": "Notes", "oldfieldname": "notes", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "fieldname": "section_break_18", "fieldtype": "Section Break", "label": "Start and End Dates"}, {"fieldname": "actual_start_date", "fieldtype": "Date", "label": "Actual Start Date (via Timesheet)", "read_only": 1}, {"fieldname": "actual_time", "fieldtype": "Float", "label": "Actual Time in Hours (via Timesheet)", "read_only": 1}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"fieldname": "actual_end_date", "fieldtype": "Date", "label": "Actual End Date (via Timesheet)", "oldfieldname": "act_completion_date", "oldfieldtype": "Date", "read_only": 1}, {"collapsible": 1, "fieldname": "project_details", "fieldtype": "Section Break", "label": "Costing and Billing", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "estimated_costing", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Estimated Cost", "oldfieldname": "project_value", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency"}, {"fieldname": "total_costing_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Costing Amount (via Timesheet)", "read_only": 1}, {"fieldname": "total_purchase_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Purchase Cost (via Purchase Invoice)", "read_only": 1}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "total_sales_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Sales Amount (via Sales Order)", "read_only": 1}, {"fieldname": "total_billable_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Billable Amount (via Timesheet)", "read_only": 1}, {"fieldname": "total_billed_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Billed Amount (via Sales Invoice)", "read_only": 1}, {"fieldname": "total_consumed_material_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Consumed Material Cost (via Stock Entry)", "read_only": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Default Cost Center", "options": "Cost Center"}, {"collapsible": 1, "fieldname": "margin", "fieldtype": "Section Break", "label": "<PERSON><PERSON>", "oldfieldtype": "Column Break", "width": "50%"}, {"fieldname": "gross_margin", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON>", "oldfieldname": "gross_margin_value", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "read_only": 1}, {"fieldname": "column_break_37", "fieldtype": "Column Break"}, {"fieldname": "per_gross_margin", "fieldtype": "Percent", "label": "Gross Margin %", "oldfieldname": "per_gross_margin", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "read_only": 1}, {"collapsible": 1, "fieldname": "monitor_progress", "fieldtype": "Section Break", "label": "Monitor Progress"}, {"default": "0", "fieldname": "collect_progress", "fieldtype": "Check", "label": "Collect Progress", "search_index": 1}, {"depends_on": "collect_progress", "fieldname": "holiday_list", "fieldtype": "Link", "label": "Holiday List", "options": "Holiday List"}, {"depends_on": "eval:doc.collect_progress == true", "fieldname": "frequency", "fieldtype": "Select", "label": "Frequency To Collect Progress", "options": "Hourly\nTwice Daily\nDaily\nWeekly"}, {"depends_on": "eval:(doc.frequency == \"Hourly\" && doc.collect_progress)", "fieldname": "from_time", "fieldtype": "Time", "label": "From Time"}, {"depends_on": "eval:(doc.frequency == \"Hourly\" && doc.collect_progress)", "fieldname": "to_time", "fieldtype": "Time", "label": "To Time"}, {"depends_on": "eval:(doc.frequency == \"Twice Daily\" && doc.collect_progress == true)\n\n", "fieldname": "first_email", "fieldtype": "Time", "label": "First Email"}, {"depends_on": "eval:(doc.frequency == \"Twice Daily\" && doc.collect_progress == true)", "fieldname": "second_email", "fieldtype": "Time", "label": "Second Email"}, {"depends_on": "eval:(doc.frequency == \"Daily\" && doc.collect_progress == true)", "fieldname": "daily_time_to_send", "fieldtype": "Time", "label": "Daily Time to send"}, {"depends_on": "eval:(doc.frequency == \"Weekly\" && doc.collect_progress == true)", "fieldname": "day_to_send", "fieldtype": "Select", "label": "Day to Send", "options": "Monday\nTuesday\nWednesday\nThursday\nFriday\nSaturday\nSunday"}, {"depends_on": "eval:(doc.frequency == \"Weekly\" && doc.collect_progress == true)", "fieldname": "weekly_time_to_send", "fieldtype": "Time", "label": "Weekly Time to send"}, {"fieldname": "column_break_45", "fieldtype": "Column Break"}, {"depends_on": "collect_progress", "description": "Message will be sent to the users to get their status on the Project", "fieldname": "message", "fieldtype": "Text", "label": "Message", "mandatory_depends_on": "collect_progress"}, {"fieldname": "section_break_izix", "fieldtype": "Section Break"}, {"fieldname": "project_area", "fieldtype": "Table", "label": "Project Area", "options": "IDP Project Area"}, {"fieldname": "contract", "fieldtype": "Link", "label": "Contract", "options": "IDP Contract", "search_index": 1}, {"fieldname": "effort_mapping", "fieldtype": "Table", "label": "Effort Mapping", "options": "IDP Designation Project Effort Mapping"}, {"fieldname": "project_value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Project Value", "read_only": 1}, {"fieldname": "employee_milestone_costing", "fieldtype": "Table", "label": "Employee Milestone Costing", "options": "IDP Employee Timesheet Costing"}, {"default": "1", "fieldname": "is_billable", "fieldtype": "Check", "label": "Is Billable"}, {"fieldname": "completion_date", "fieldtype": "Date", "label": "Completion Date"}], "grid_page_length": 50, "icon": "fa fa-puzzle-piece", "index_web_pages_for_search": 1, "links": [], "max_attachments": 4, "modified": "2025-07-03 11:36:09.067325", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Project", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Projects User", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Desk User"}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Projects Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "report": 1, "role": "Employee", "select": 1, "share": 1}], "quick_entry": 1, "row_format": "Dynamic", "search_fields": "project_name,customer, status, priority, is_active", "show_name_in_global_search": 1, "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "project_name", "track_changes": 1, "track_seen": 1}