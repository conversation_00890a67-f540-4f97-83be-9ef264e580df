// Copyright (c) 2025, Agkiya Labs and contributors
// For license information, please see license.txt

frappe.ui.form.on("IDP Project", {
  setup(frm) {
    // frm.make_methods = {
    //   Timesheet: () => {
    //     open_form(frm, "IDP Timesheet", "IDP Timesheet Detail", "time_logs");
    //   },
    //   "Purchase Order": () => {
    //     open_form(frm, "Purchase Order", "Purchase Order Item", "items");
    //   },
    //   "Purchase Receipt": () => {
    //     open_form(frm, "Purchase Receipt", "Purchase Receipt Item", "items");
    //   },
    //   "Purchase Invoice": () => {
    //     open_form(frm, "Purchase Invoice", "Purchase Invoice Item", "items");
    //   },
    // };
  },
  onload: function (frm) {
    const so = frm.get_docfield("sales_order");
    so.get_route_options_for_new_doc = () => {
      if (frm.is_new()) return {};
      return {
        customer: frm.doc.customer,
        project_name: frm.doc.name,
      };
    };

    frm.set_query("user", "users", function () {
      return {
        query:
          "inspira.inspira.doctype.idp_project.idp_project.get_users_for_project",
      };
    });

    frm.set_query("department", function (doc) {
      return {
        filters: {
          company: doc.company,
        },
      };
    });

    // sales order
    frm.set_query("sales_order", function () {
      var filters = {
        project: ["in", frm.doc.__islocal ? [""] : [frm.doc.name, ""]],
        company: frm.doc.company,
      };

      if (frm.doc.customer) {
        filters["customer"] = frm.doc.customer;
      }

      return {
        filters: filters,
      };
    });

    frm.set_query("cost_center", () => {
      return {
        filters: {
          company: frm.doc.company,
        },
      };
    });
  },

  refresh: function (frm) {
    // if (frm.doc.__islocal) {
    //   frm.web_link && frm.web_link.remove();
    // } else {
    //   frm.add_web_link("/idp-project?project=" + encodeURIComponent(frm.doc.name));

    //   frm.trigger("show_dashboard");
    // }
    frm.trigger("set_custom_buttons");
  },

  set_custom_buttons: function (frm) {
    if (!frm.is_new()) {
      // if (!frm.doc.contract) {
      //   frm.add_custom_button(
      //     __("Contract"),
      //     () => {
      //       frm.trigger("create_contract");
      //     },
      //     __("Create")
      //   );
      // }

      // frm.add_custom_button(
      //   __("Milestone"),
      //   () => {
      //     frm.trigger("create_contract");
      //   },
      //   __("Create")
      // );
      // frm.add_custom_button(
      //   __("Duplicate Project with Tasks"),
      //   () => {
      //     frm.events.create_duplicate(frm);
      //   },
      //   __("Actions")
      // );

      // frm.add_custom_button(
      //   __("Update Total Purchase Cost"),
      //   () => {
      //     frm.events.update_total_purchase_cost(frm);
      //   },
      //   __("Actions")
      // );

      // frm.trigger("set_project_status_button");

      if (frappe.model.can_read("IDP Task")) {
        frm.add_custom_button(
          __("Gantt Chart"),
          function () {
            frappe.route_options = {
              project: frm.doc.name,
            };
            frappe.set_route("List", "IDP Task", "Gantt");
          },
          __("View")
        );

        frm.add_custom_button(
          __("Kanban Board"),
          () => {
            frappe
              .call(
                "inspira.inspira.doctype.idp_project.idp_project.create_kanban_board_if_not_exists",
                {
                  project: frm.doc.name,
                }
              )
              .then(() => {
                frappe.set_route(
                  "List",
                  "IDP Task",
                  "Kanban",
                  frm.doc.project_name
                );
              });
          },
          __("View")
        );
      }
    }
  },

  // create_contract(frm) {
  //   frappe.new_doc('IDP Contract', {
  //     project_link: frm.doc.name,
  // });
    // frappe.set_route('Form', 'IDP Contract', contract.name);

    // let d = new frappe.ui.Dialog({
    //   title: __("Create Contract"),
    //   fields: [
        // {
        //   fieldname: "customer",
        //   fieldtype: "Link",
        //   label: "Customer",
        //   reqd: 1,
        //   options: "Customer",
        // },
    //     {
    //       fieldname: "status",
    //       fieldtype: "Link",
    //       label: "Status",
    //       reqd: 1,
    //       options: "IDP Status Master",
    //     },
    //     {
    //       fieldname: "square_feet",
    //       fieldtype: "Float",
    //       label: "Square Feet",
    //       reqd: 1,
    //     },
    //     {
    //       fieldname: "category",
    //       fieldtype: "Select",
    //       label: "Category",
    //       reqd: 1,
    //       options: "\nInterior\nArchitecture",
    //     },
    //     {
    //       fieldname: "classification",
    //       fieldtype: "Select",
    //       label: "Classification",
    //       reqd: 1,
    //       options: "\nResidential\nCommercial",
    //     },
    //     {
    //       fieldname: "sub_classification",
    //       fieldtype: "Link",
    //       label: "Sub Classification",
    //       reqd: 1,
    //       options: "IDP Sub Classification",
    //       get_query: function () {
		// 				return {
		// 					filters: [["classification", "=", d.get_value("classification")]],
		// 				};
		// 			},
    //     },
    //     {
    //       fieldname: "contract_type",
    //       fieldtype: "Select",
    //       label: "Contract Type",
    //       reqd: 1,
    //       options: "\nDelivery\nRetainer",
    //     },
    //     {
    //       fieldname: "project_value",
    //       fieldtype: "Currency",
    //       label: "Project Value",
    //       reqd: 0,
    //     },
    //   ],
    //   primary_action: function () {
    //     console.log(d.get_values())
    //     frappe.call({
    //       method: "inspira.inspira.doctype.idp_project.idp_project.create_contract",
    //       args: {
    //         project: frm.doc.name,
    //         contract_json: d.get_values(),
    //       },
    //       callback: function (r) {
    //         if (r && r.message) {
    //           frm.refresh();
    //         }
    //       },
    //     });
    //     d.hide();
    //   },
    //   primary_action_label: __("Create Contract"),
    // }).show();
  // }

  // update_total_purchase_cost: function (frm) {
  //   frappe.call({
  //     method:
  //       "inspira.inspira.doctype.idp_project.idp_project.recalculate_project_total_purchase_cost",
  //     args: { project: frm.doc.name },
  //     freeze: true,
  //     freeze_message: __("Recalculating Purchase Cost against this Project..."),
  //     callback: function (r) {
  //       if (r && !r.exc) {
  //         frappe.msgprint(__("Total Purchase Cost has been updated"));
  //         frm.refresh();
  //       }
  //     },
  //   });
  // },

  // set_project_status_button: function (frm) {
  //   frm.add_custom_button(
  //     __("Set Project Status"),
  //     () => {
        // let d = new frappe.ui.Dialog({
        //   title: __("Set Project Status"),
        //   fields: [
        //     {
        //       fieldname: "status",
        //       fieldtype: "Select",
        //       label: "Status",
        //       reqd: 1,
        //       options: "Completed\nCancelled",
        //     },
        //   ],
        //   primary_action: function () {
        //     frm.events.set_status(frm, d.get_values().status);
        //     d.hide();
        //   },
        //   primary_action_label: __("Set Project Status"),
        // }).show();
  //     },
  //     __("Actions")
  //   );
  // },

  // create_duplicate: function (frm) {
  //   return new Promise((resolve) => {
  //     frappe.prompt("Project Name", (data) => {
  //       frappe
  //         .xcall(
  //           "inspira.inspira.doctype.idp_project.idp_project.create_duplicate_project",
  //           {
  //             prev_doc: frm.doc,
  //             project_name: data.value,
  //           }
  //         )
  //         .then(() => {
  //           frappe.set_route("Form", "IDP Project", data.value);
  //           frappe.show_alert(__("Duplicate project has been created"));
  //         });
  //       resolve();
  //     });
  //   });
  // },

//   set_status: function (frm, status) {
//     frappe.confirm(
//       __("Set Project and all Tasks to status {0}?", [status.bold()]),
//       () => {
//         frappe
//           .xcall(
//             "inspira.inspira.doctype.idp_project.idp_project.set_project_status",
//             {
//               project: frm.doc.name,
//               status: status,
//             }
//           )
//           .then(() => {
//             frm.reload_doc();
//           });
//       }
//     );
//   },
});


frappe.ui.form.on('IDP Project User', {
	variable_(frm, cdt, cdn) {
		var row = locals[cdt][cdn];
		row.variable_amount =  update_variable_amount(frm, cdt, cdn, "variable_")
		frm.refresh_field("users");
	},

	variable_amount(frm, cdt, cdn) {
		var row = locals[cdt][cdn];
		row.variable_ = update_variable_amount(frm, cdt, cdn, "variable_amount")
		frm.refresh_field("users");
	}
})

function update_variable_amount(frm, cdt, cdn, based_on) { 
	if (based_on == "variable_") {
		var row = locals[cdt][cdn];
		return row.variable_ * frm.doc.project_value / 100;
		
	} else {
		var row = locals[cdt][cdn];
		return row.variable_amount * 100 / frm.doc.project_value;
	}
}

// function open_form(frm, doctype, child_doctype, parentfield) {
//   frappe.model.with_doctype(doctype, () => {
//     let new_doc = frappe.model.get_new_doc(doctype);

//     // add a new row and set the project
//     let new_child_doc = frappe.model.get_new_doc(child_doctype);
//     new_child_doc.project = frm.doc.name;
//     new_child_doc.parent = new_doc.name;
//     new_child_doc.parentfield = parentfield;
//     new_child_doc.parenttype = doctype;
//     new_doc[parentfield] = [new_child_doc];
//     new_doc.project = frm.doc.name;

//     frappe.ui.form.make_quick_entry(doctype, null, null, new_doc);
//   });
// }
