{"actions": [], "allow_rename": 1, "autoname": "format:{form}-{status}", "creation": "2025-04-23 19:53:52.877693", "doctype": "DocType", "engine": "InnoDB", "field_order": ["form", "status", "color"], "fields": [{"fieldname": "form", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Form", "options": "\nContract\nProject\nTask\nMIlestone Payment", "reqd": 1}, {"fieldname": "status", "fieldtype": "Data", "in_list_view": 1, "label": "Status", "reqd": 1}, {"fieldname": "color", "fieldtype": "Color", "in_list_view": 1, "label": "Color"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-06 20:01:07.786145", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Status Master", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Projects User", "select": 1, "share": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "status"}