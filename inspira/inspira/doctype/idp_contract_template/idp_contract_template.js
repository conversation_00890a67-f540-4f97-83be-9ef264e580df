// Copyright (c) 2025, Agkiya Labs and contributors
// For license information, please see license.txt

frappe.ui.form.on("IDP Contract Template", {
	refresh(frm) {
        
        frm.doc.milestone_names = (frm.doc.milestones || []).map(row => row.milestone);
        set_milestone_filters(frm)

        frm.doc.deliverable_names = (frm.doc.deliverables || []).map(row => row.deliverable);
        set_deliverable_options(frm);
	},
});

frappe.ui.form.on('IDP Milestones Template', {
	milestone(frm, cdt, cdn) {
		frm.doc.milestone_names = (frm.doc.milestones || []).map(row => row.milestone);
        set_milestone_filters(frm)
	},
    milestones_remove(frm, cdt, cdn) {
		frm.doc.milestone_names = (frm.doc.milestones || []).map(row => row.milestone);
        set_milestone_filters(frm)
	}
})

frappe.ui.form.on('IDP Deliverable Template', {
	deliverable(frm, cdt, cdn) {
        const row = locals[cdt][cdn];
        if (row.deliverable) {
            frm.doc.deliverable_names = (frm.doc.deliverables || []).map(row => row.deliverable);
            set_deliverable_options(frm)
        }
	},
    deliverables_remove(frm, cdt, cdn) {
        frm.doc.deliverable_names = (frm.doc.deliverables || []).map(row => row.deliverable);
		set_deliverable_options(frm)
	}
})

function set_milestone_filters(frm) {
    frm.set_query('milestone', 'deliverables', () => {
        return {
            filters: {
                name: ['in', frm.doc.milestone_names]
            }
        }
    })
}

function set_deliverable_options(frm) {
    frm.fields_dict.tasks.grid.update_docfield_property(
        "deliverable",
        "options",
        frm.doc.deliverable_names
    );
}