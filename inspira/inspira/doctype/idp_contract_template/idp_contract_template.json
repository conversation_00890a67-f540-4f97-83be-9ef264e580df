{"actions": [], "allow_rename": 1, "autoname": "field:template_name", "creation": "2025-04-23 20:09:13.631895", "doctype": "DocType", "engine": "InnoDB", "field_order": ["template_name", "contract_type", "section_break_alfu", "milestones", "deliverable_section", "deliverables", "tasks_section", "tasks"], "fields": [{"fieldname": "template_name", "fieldtype": "Data", "in_list_view": 1, "label": "Template Name", "reqd": 1, "unique": 1}, {"fieldname": "milestones", "fieldtype": "Table", "options": "IDP Milestones Template"}, {"fieldname": "contract_type", "fieldtype": "Select", "in_list_view": 1, "label": "Contract Type", "options": "\nDelivery\nRetainer", "reqd": 1}, {"fieldname": "section_break_alfu", "fieldtype": "Section Break", "label": "Milestones"}, {"fieldname": "deliverable_section", "fieldtype": "Section Break", "label": "Deliverable"}, {"fieldname": "tasks_section", "fieldtype": "Section Break", "label": "Tasks"}, {"fieldname": "deliverables", "fieldtype": "Table", "options": "IDP Deliverable Template"}, {"fieldname": "tasks", "fieldtype": "Table", "options": "IDP Task Template"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-09 22:21:27.397733", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Contract Template", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}