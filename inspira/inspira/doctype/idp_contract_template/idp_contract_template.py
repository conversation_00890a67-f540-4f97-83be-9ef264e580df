# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document


class IDPContractTemplate(Document):
	def validate(self):
		self.validate_milestones_and_deliverables()
		self.validate_milestone_percentage()

	def validate_milestone_percentage(self):
		total_percentage = 0
		for milestone in self.milestones:
			total_percentage += milestone.fee_
			if milestone.fee_ > 100:
				frappe.throw(f"Row #{frappe.bold(milestone.idx)} Percentage for milestone {frappe.bold(milestone.milestone)} cannot be greater than 100%")

		if total_percentage > 100:
			frappe.throw(f"Total percentage for milestones should not be greater than 100%")

	def validate_milestones_and_deliverables(self):
		milestones = []
		for milestone in self.milestones:
			milestones.append(milestone.milestone)

		deliverables = []
		for deliverable in self.deliverables:
			deliverables.append(deliverable.deliverable)
			if deliverable.milestone not in milestones:
				frappe.throw(f"Row #{frappe.bold(deliverable.idx)} Deliverable: {frappe.bold(deliverable.deliverable)} has invalid Milestone: {frappe.bold(deliverable.milestone)}")

		for task in self.tasks:
			if task.deliverable not in deliverables:
				frappe.throw(f"Row #{frappe.bold(task.idx)} Task: {frappe.bold(task.task)} has invalid Deliverable: {frappe.bold(task.deliverable)}")