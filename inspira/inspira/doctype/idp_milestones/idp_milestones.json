{"actions": [], "allow_rename": 1, "creation": "2025-02-12 15:01:32.915279", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["information_and_progress_section", "retainer", "milestone", "idp_task", "fee_", "fee_amount", "completion_", "profitability_", "column_break_gwfr", "payment_status", "contract_end_date", "planned_end_date", "actual_end_date", "client_approval_date", "invoice_date", "payment_received", "payment_pending", "payment_date", "cost_incurred"], "fields": [{"fieldname": "milestone", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Milestone", "options": "IDP Payment Milestone", "reqd": 1}, {"fieldname": "completion_", "fieldtype": "Percent", "in_list_view": 1, "in_standard_filter": 1, "label": "Completion %", "no_copy": 1, "read_only": 1}, {"fieldname": "information_and_progress_section", "fieldtype": "Section Break", "label": "Information and Progress"}, {"fieldname": "column_break_gwfr", "fieldtype": "Column Break"}, {"fieldname": "fee_", "fieldtype": "Percent", "label": "Fee %", "non_negative": 1, "reqd": 1}, {"fieldname": "fee_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> Amount", "non_negative": 1, "reqd": 1}, {"depends_on": "eval:parent.contract_type=='Retainer'", "fieldname": "retainer", "fieldtype": "Link", "in_standard_filter": 1, "label": "Retainer", "mandatory_depends_on": "eval:parent.contract_type=='Retainer'", "options": "IDP Retainer"}, {"fieldname": "payment_status", "fieldtype": "Link", "label": "Payment Status", "link_filters": "[[\"IDP Status Master\",\"form\",\"=\",\"MIlestone Payment\"]]", "options": "IDP Status Master"}, {"fieldname": "profitability_", "fieldtype": "Percent", "in_list_view": 1, "in_standard_filter": 1, "label": "Profitability %", "no_copy": 1, "read_only": 1}, {"fieldname": "contract_end_date", "fieldtype": "Date", "label": "Contract End Date"}, {"fieldname": "actual_end_date", "fieldtype": "Date", "label": "Actual End Date", "read_only": 1}, {"fieldname": "client_approval_date", "fieldtype": "Date", "label": "C<PERSON> Approval Date"}, {"fieldname": "invoice_date", "fieldtype": "Date", "label": "Invoice Date", "read_only": 1}, {"fieldname": "payment_received", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Payment Received", "read_only": 1}, {"fieldname": "payment_pending", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Payment Pending", "read_only": 1}, {"fieldname": "payment_date", "fieldtype": "Date", "label": "Payment Date", "read_only": 1}, {"fieldname": "cost_incurred", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Cost Incurred"}, {"fieldname": "planned_end_date", "fieldtype": "Date", "label": "Planned End Date", "read_only": 1}, {"fieldname": "idp_task", "fieldtype": "Data", "label": "IDP Task", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-06-11 21:14:40.342069", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Milestones", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "milestone"}