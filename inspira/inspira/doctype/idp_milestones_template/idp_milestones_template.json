{"actions": [], "allow_rename": 1, "creation": "2025-04-24 12:59:34.204275", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["retainer", "milestone", "column_break_qwjv", "fee_"], "fields": [{"depends_on": "eval:parent.contract_type=='Retainer'", "fieldname": "retainer", "fieldtype": "Link", "label": "Retainer", "mandatory_depends_on": "eval:parent.contract_type=='Retainer'", "options": "IDP Retainer"}, {"fieldname": "milestone", "fieldtype": "Link", "in_list_view": 1, "label": "Milestone", "options": "IDP Payment Milestone", "reqd": 1}, {"fieldname": "column_break_qwjv", "fieldtype": "Column Break"}, {"fieldname": "fee_", "fieldtype": "Percent", "in_list_view": 1, "label": "Fee %", "reqd": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-04-24 13:06:58.766088", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Milestones Template", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}