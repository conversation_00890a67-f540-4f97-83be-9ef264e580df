# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

# import frappe
from frappe.model.document import Document


class IDPContractFulfilmentChecklist(Document):
	# begin: auto-generated types
	# This code is auto-generated. Do not modify anything in this block.

	from typing import TYPE_CHECKING

	if TYPE_CHECKING:
		from frappe.types import DF

		amended_from: DF.Link | None
		fulfilled: DF.Check
		notes: DF.Text | None
		parent: DF.Data
		parentfield: DF.Data
		parenttype: DF.Data
		requirement: DF.Data | None
	# end: auto-generated types

	pass
