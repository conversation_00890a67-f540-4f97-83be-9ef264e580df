{"actions": [], "allow_rename": 1, "autoname": "field:sub_classification", "creation": "2025-04-23 20:25:31.334302", "doctype": "DocType", "engine": "InnoDB", "field_order": ["classification", "column_break_xwqx", "sub_classification"], "fields": [{"fieldname": "classification", "fieldtype": "Select", "in_list_view": 1, "label": "Classification", "options": "\nResidential\nCommercial", "reqd": 1}, {"fieldname": "sub_classification", "fieldtype": "Data", "in_list_view": 1, "label": "Sub Classification", "reqd": 1, "unique": 1}, {"fieldname": "column_break_xwqx", "fieldtype": "Column Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-23 20:26:20.952046", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Sub Classification", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}