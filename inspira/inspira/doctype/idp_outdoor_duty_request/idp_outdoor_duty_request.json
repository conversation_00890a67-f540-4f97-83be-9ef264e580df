{"actions": [], "allow_rename": 1, "autoname": "HR-ODRQ-.YY.-.MM.-.#####", "creation": "2025-05-30 10:54:24.847285", "doctype": "DocType", "engine": "InnoDB", "field_order": ["requested_on", "employee", "employee_name", "date", "duty_type", "project", "notify", "attendance_request", "column_break_hvq4", "in_time", "out_time", "shift", "reason", "amended_from", "approver", "approver_name", "approved_on"], "fields": [{"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "IDP Outdoor Duty Request", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "date", "fieldtype": "Date", "label": "Date"}, {"default": "Today", "fieldname": "requested_on", "fieldtype": "Date", "label": "Requested On", "read_only": 1}, {"fieldname": "duty_type", "fieldtype": "Link", "label": "Duty Type", "options": "IDP Duty Type"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "IDP Project"}, {"fieldname": "in_time", "fieldtype": "Time", "label": "In Time"}, {"fieldname": "out_time", "fieldtype": "Time", "label": "Out Time"}, {"fieldname": "reason", "fieldtype": "Long Text", "label": "Reason"}, {"fieldname": "notify", "fieldtype": "Table MultiSelect", "label": "Notify", "options": "IDP Notify User"}, {"fieldname": "column_break_hvq4", "fieldtype": "Column Break"}, {"fieldname": "employee", "fieldtype": "Link", "label": "Employee", "options": "Employee"}, {"fetch_from": "employee.default_shift", "fieldname": "shift", "fieldtype": "Link", "label": "Shift", "options": "Shift Type"}, {"fieldname": "attendance_request", "fieldtype": "Data", "label": "Attendance Request", "read_only": 1}, {"fetch_from": "employee.employee_name", "fieldname": "employee_name", "fieldtype": "Data", "label": "Employee Name", "read_only": 1}, {"fieldname": "approver", "fieldtype": "Link", "label": "Approver", "options": "User", "read_only": 1}, {"fieldname": "approved_on", "fieldtype": "Date", "label": "Approved on", "read_only": 1}, {"fetch_from": "approver.full_name", "fetch_if_empty": 1, "fieldname": "approver_name", "fieldtype": "Data", "label": "Approver Name", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-06-09 10:55:31.557084", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Outdoor Duty Request", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Employee", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}