# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from datetime import datetime,time, timedelta
from frappe.utils import getdate


class IDPOutdoorDutyRequest(Document):
	def before_submit(self, method=None):
		
		if self.in_time:
			if isinstance(self.in_time, timedelta):
				in_time_obj = (datetime.min + self.in_time).time()
			elif isinstance(self.in_time, str):
				in_time_obj = datetime.strptime(self.in_time, "%H:%M:%S").time()
			else:
				in_time_obj = self.in_time  # assume already a time object

			in_datetime = datetime.combine(getdate(self.date), in_time_obj)

			if not frappe.db.exists("Employee Checkin", {
				"employee": self.employee,
				"log_type": "IN",
				"time": in_datetime
			}):
				checkin = frappe.new_doc("Employee Checkin")
				checkin.employee = self.employee
				checkin.log_type = 'IN'
				checkin.shift = self.shift
				checkin.time = in_datetime
				checkin.skip_auto_attendance = 1
				checkin.insert()

		if self.out_time:
			if isinstance(self.out_time, timedelta):
				out_time_obj = (datetime.min + self.out_time).time()
			elif isinstance(self.out_time, str):
				out_time_obj = datetime.strptime(self.out_time, "%H:%M:%S").time()
			else:
				out_time_obj = self.out_time

			out_datetime = datetime.combine(getdate(self.date), out_time_obj)

			if not frappe.db.exists("Employee Checkin", {
				"employee": self.employee,
				"log_type": "OUT",
				"time": out_datetime
			}):
				checkin = frappe.new_doc("Employee Checkin")
				checkin.employee = self.employee
				checkin.log_type = 'OUT'
				checkin.shift = self.shift
				checkin.time = out_datetime
				checkin.skip_auto_attendance = 1
				checkin.insert()

		try:
			if not frappe.db.exists("Attendance Request", {
				"employee": self.employee,
				"from_date": getdate(self.date),
				"to_date": getdate(self.date),
				"docstatus": ["!=", 2]
			}):
				request = frappe.new_doc("Attendance Request")
				request.employee = self.employee
				request.from_date = self.date
				request.to_date = self.date
				request.shift = self.shift
				request.reason = "On Duty"
				request.explanation = self.reason
				for user in self.notify:
					request.append("notify_users", {
						"user": user.user
					})
				request.duty_type = self.duty_type
				request.project = self.project
				if self.in_time and self.in_time:
					request.half_day = 1
					request.half_day_date = self.date
				request.insert()
				request.submit()
				self.attendance_request = request.name
			else:
				exists = frappe.db.exists("Attendance Request", {
					"employee": self.employee,
					"from_date": getdate(self.date),
					"to_date": getdate(self.date),
					"docstatus": ["!=", 2]
				})
				self.attendance_request = exists
		except Exception as e:
			frappe.log_error(f"Error while creating attendance request from {self.name}", f"{e}")
			frappe.msgprint(f"Error while creating attendance request from {self.name} : {e}")
		
		self.approver = frappe.session.user
		self.approved_on = getdate()
