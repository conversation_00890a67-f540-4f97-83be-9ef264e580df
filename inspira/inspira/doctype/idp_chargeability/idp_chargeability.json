{"actions": [], "allow_rename": 1, "creation": "2025-07-03 13:51:41.385413", "doctype": "DocType", "engine": "InnoDB", "field_order": ["employee", "start_date", "fixed_cost", "variable_percentage", "column_break_rumk", "project", "end_date", "seat_cost", "total_cost"], "fields": [{"fieldname": "employee", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Employee", "options": "Employee", "reqd": 1}, {"fieldname": "start_date", "fieldtype": "Date", "in_list_view": 1, "label": "Start Date", "reqd": 1}, {"fieldname": "fixed_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Fixed Cost", "non_negative": 1, "reqd": 1}, {"fieldname": "column_break_rumk", "fieldtype": "Column Break"}, {"fieldname": "project", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Project", "options": "IDP Project", "reqd": 1}, {"fieldname": "end_date", "fieldtype": "Date", "in_list_view": 1, "label": "End Date"}, {"fieldname": "seat_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> Cost", "non_negative": 1, "reqd": 1}, {"fieldname": "total_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Cost", "non_negative": 1}, {"fieldname": "variable_percentage", "fieldtype": "Percent", "label": "Variable %", "non_negative": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-09 14:23:50.009429", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Chargeability", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}