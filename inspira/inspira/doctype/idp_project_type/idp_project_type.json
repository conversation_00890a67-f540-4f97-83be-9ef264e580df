{"actions": [], "allow_rename": 1, "autoname": "field:project_type", "creation": "2025-01-25 14:08:56.776540", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["project_type", "description"], "fields": [{"fieldname": "project_type", "fieldtype": "Data", "in_list_view": 1, "label": "Project Type", "reqd": 1, "unique": 1}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}], "links": [], "modified": "2025-01-25 14:08:56.776540", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Project Type", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Projects Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Projects User", "share": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}