// Copyright (c) 2025, Agkiya Labs and contributors
// For license information, please see license.txt

frappe.ui.form.on("IDP Site Management", {
  setup(frm) {
    frm.set_query("project_id", function () {
      return {
        query:
          "inspira.inspira.doctype.idp_site_management.idp_site_management.get_users",
        filters: {
          user: frappe.session.user,
        },
      };
    });
  },

  project_id: function (frm) {
    // Check if project_id is valid (not empty or undefined)
    if (frm.doc.project_id) {
      frappe.model.with_doc("IDP Project", frm.doc.project_id, function () {
        var project_doc = frappe.model.get_doc(
          "IDP Project",
          frm.doc.project_id
        );
        var area_list = [];

        $.each(project_doc.project_area || [], function (i, d) {
          if (d.area) {
            // area_list.push(d.area);
            area_list.push({
              value: d.area,
              label: d.area,
            });
            frm.fields_dict.select_area.set_data(area_list);
          }
        });
      });
    } else {
      frm.set_df_property("select_area", "options", []);
    }
  },
});
