{"actions": [], "allow_rename": 1, "autoname": "format:{doc_name}{########}", "creation": "2025-02-12 15:03:56.393533", "doctype": "DocType", "engine": "InnoDB", "field_order": ["main_tab", "project_section", "project_id", "project_name", "report_generated_by", "column_break_cgcp", "select_area", "date", "daily_number_series", "weekly_number_series", "civil_section", "column_break_amob", "image_1", "column_break_iegw", "image_2", "column_break_wiob", "image_3", "column_break_rsrs", "image_d", "column_break_ptov", "elevation_e", "column_break_tahe", "elevation_f", "section_break_lwrv", "disciplian_updates", "section_break_jufm", "select_activty", "status", "remarks", "doc_name", "civil_tab", "carpentry_tab", "electrical_tab", "hvac_tab", "pop_tab", "fire_fighting_tab", "windows_tab", "painting_and_polish_tab", "decor_tab", "amended_from"], "fields": [{"fieldname": "main_tab", "fieldtype": "Tab Break", "label": "Main"}, {"fieldname": "project_section", "fieldtype": "Section Break", "label": "Project"}, {"fieldname": "column_break_cgcp", "fieldtype": "Column Break"}, {"fieldname": "select_area", "fieldtype": "Autocomplete", "in_list_view": 1, "label": "Area"}, {"default": "Today", "fieldname": "date", "fieldtype": "Date", "label": "Date", "read_only": 1}, {"fieldname": "civil_section", "fieldtype": "Section Break", "label": "Image"}, {"fieldname": "column_break_amob", "fieldtype": "Column Break"}, {"fieldname": "image_1", "fieldtype": "Attach Image", "label": "Elevation A"}, {"fieldname": "column_break_iegw", "fieldtype": "Column Break"}, {"fieldname": "image_2", "fieldtype": "Attach Image", "label": "Elevation B"}, {"fieldname": "column_break_wiob", "fieldtype": "Column Break"}, {"fieldname": "image_3", "fieldtype": "Attach Image", "label": "Elevation C"}, {"fieldname": "column_break_rsrs", "fieldtype": "Column Break"}, {"fieldname": "image_d", "fieldtype": "Attach", "label": "Elevation D"}, {"fieldname": "section_break_lwrv", "fieldtype": "Section Break"}, {"fieldname": "disciplian_updates", "fieldtype": "Table", "label": "Work Updates and Future Plan", "options": "IDP Work Updates and Future Plan"}, {"fieldname": "section_break_jufm", "fieldtype": "Section Break"}, {"fieldname": "select_activty", "fieldtype": "Data", "hidden": 1, "label": "Select Activty"}, {"fieldname": "status", "fieldtype": "Data", "hidden": 1, "label": "Status"}, {"fieldname": "remarks", "fieldtype": "Small Text", "hidden": 1, "label": "Remarks"}, {"fieldname": "doc_name", "fieldtype": "Data", "hidden": 1, "label": "Doc Name"}, {"fieldname": "civil_tab", "fieldtype": "Tab Break", "label": "Civil"}, {"fieldname": "carpentry_tab", "fieldtype": "Tab Break", "label": "Carpentry"}, {"fieldname": "electrical_tab", "fieldtype": "Tab Break", "label": "Electrical"}, {"fieldname": "hvac_tab", "fieldtype": "Tab Break", "label": "HVAC"}, {"fieldname": "pop_tab", "fieldtype": "Tab Break", "label": "POP"}, {"fieldname": "fire_fighting_tab", "fieldtype": "Tab Break", "label": "Fire Fighting"}, {"fieldname": "windows_tab", "fieldtype": "Tab Break", "label": "Windows"}, {"fieldname": "painting_and_polish_tab", "fieldtype": "Tab Break", "label": "Painting and Polish"}, {"fieldname": "decor_tab", "fieldtype": "Tab Break", "label": "Decor"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "IDP Site Management", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "project_id", "fieldtype": "Link", "in_list_view": 1, "label": "Project ID", "options": "IDP Project"}, {"fetch_from": "project_id.project_name", "fieldname": "project_name", "fieldtype": "Data", "in_list_view": 1, "label": "Project Name"}, {"fieldname": "report_generated_by", "fieldtype": "Data", "in_filter": 1, "in_standard_filter": 1, "label": "Report Generated By"}, {"fieldname": "daily_number_series", "fieldtype": "Int", "label": "Daily Number Series", "read_only": 1}, {"fieldname": "weekly_number_series", "fieldtype": "Int", "label": "Weekly Number Series", "read_only": 1}, {"fieldname": "column_break_ptov", "fieldtype": "Column Break"}, {"fieldname": "elevation_e", "fieldtype": "Attach", "label": "Elevation E"}, {"fieldname": "column_break_tahe", "fieldtype": "Column Break"}, {"fieldname": "elevation_f", "fieldtype": "Attach", "label": "Elevation F"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-15 12:11:34.280053", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Site Management", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}