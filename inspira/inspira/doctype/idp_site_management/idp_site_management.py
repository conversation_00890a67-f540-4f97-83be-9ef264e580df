# Copyright (c) 2025, Agkiya Labs and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document

SITE_MGMT_DOCTYPE = "IDP Site Management"
class IDPSiteManagement(Document):
	def before_insert(self):
		if not self.project_id:
			frappe.throw("Project Name is required to generate a document name.")

			# Trim project_id to the first five characters
		project_id_trimmed = self.project_id[:4].upper()

		# Populate the custom_naming field
		self.doc_name = f"SA-{project_id_trimmed}-abbr-"

		user_fullname = frappe.db.get_value("User", frappe.session.user, "full_name")
		if not self.report_generated_by:
			self.report_generated_by = user_fullname

		self.set_number_series()

	def set_number_series(self):
		if not self.project_id:
			return

		daily_max = frappe.get_all(
			"IDP Site Management",
			filters=[[SITE_MGMT_DOCTYPE, "project_id", "=", self.project_id]],
			fields=["max(daily_number_series) as max_daily"],
			as_list=False
		)

		self.daily_number_series = (daily_max[0].max_daily or 0) if daily_max else 0

		weekly_max = frappe.get_all(
			"IDP Site Management",
			filters=[[SITE_MGMT_DOCTYPE, "project_id", "=", self.project_id]],
			fields=["max(weekly_number_series) as max_weekly"],
			as_list=False
		)
		self.weekly_number_series = (weekly_max[0].max_weekly or 0) if weekly_max else 0

	def validate(self):
		previous = self.get_doc_before_save()
		if previous and previous.workflow_state != self.workflow_state:
			self.date = frappe.utils.getdate()


@frappe.whitelist()
@frappe.validate_and_sanitize_search_inputs
def get_users(doctype, txt, searchfield, start, page_len, filters):
	sql_query = """
		SELECT
			proj.name AS value
		FROM
			`tabIDP Project` AS proj
		JOIN
			`tabIDP Project User` AS proj_user
		ON
			proj_user.parent = proj.name
		WHERE
			proj_user.user LIKE %(txt)s
			AND proj_user.user =  %(user_ref)s
		LIMIT %(page_len)s OFFSET %(start)s
	"""
	params = {
		"txt": f"%{txt}%",
		"user_ref": filters["user"],
		"start": start,
		"page_len": page_len,
	}
	return frappe.db.sql(sql_query, params)
