{"actions": [], "creation": "2025-01-25 12:36:27.895144", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["user", "email", "image", "view_contract", "column_break_2", "designation", "full_name", "view_attachments", "variable_pay_section", "is_variable_pay", "variable_", "column_break_nbjx", "variable_amount", "variable_received", "section_break_5", "project_status", "report_scheduler_section", "send_right_away", "send_daily", "daily_time", "column_break_uvly", "send_weekly", "weekly_time"], "fields": [{"fieldname": "user", "fieldtype": "Link", "in_list_view": 1, "label": "User", "options": "User", "reqd": 1, "search_index": 1}, {"fetch_from": "user.email", "fieldname": "email", "fieldtype": "Read Only", "label": "Email"}, {"fetch_from": "user.user_image", "fieldname": "image", "fieldtype": "Read Only", "hidden": 1, "in_global_search": 1, "label": "Image"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "user.full_name", "fieldname": "full_name", "fieldtype": "Read Only", "in_list_view": 1, "label": "Full Name"}, {"columns": 2, "default": "0", "fieldname": "view_attachments", "fieldtype": "Check", "in_list_view": 1, "label": "View attachments"}, {"fieldname": "section_break_5", "fieldtype": "Section Break"}, {"depends_on": "eval:parent.doctype == 'Project Update'", "fieldname": "project_status", "fieldtype": "Text", "label": "Project Status"}, {"fieldname": "designation", "fieldtype": "Link", "label": "Designation", "options": "Designation"}, {"fieldname": "report_scheduler_section", "fieldtype": "Section Break", "label": "Report Scheduler"}, {"default": "0", "fieldname": "send_right_away", "fieldtype": "Check", "label": "Send Right Away"}, {"default": "0", "fieldname": "send_daily", "fieldtype": "Check", "label": "Send Daily"}, {"depends_on": "eval:doc.send_daily", "fieldname": "daily_time", "fieldtype": "Select", "label": "Daily Time", "mandatory_depends_on": "eval:doc.send_daily", "options": "8 AM\n8 PM"}, {"fieldname": "column_break_uvly", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "send_weekly", "fieldtype": "Check", "label": "Send Weekly"}, {"depends_on": "eval:doc.send_weekly", "fieldname": "weekly_time", "fieldtype": "Select", "label": "Weekly Time", "mandatory_depends_on": "eval:doc.send_weekly", "options": "Monday - 8 AM\nSaturday - 8 PM"}, {"fieldname": "variable_pay_section", "fieldtype": "Section Break", "label": "Variable Pay"}, {"default": "0", "fieldname": "is_variable_pay", "fieldtype": "Check", "label": "Is Variable Pay"}, {"depends_on": "eval:doc.is_variable_pay", "fieldname": "variable_", "fieldtype": "Percent", "label": "Variable %", "mandatory_depends_on": "eval:doc.is_variable_pay"}, {"fieldname": "column_break_nbjx", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.is_variable_pay", "fieldname": "variable_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Variable Amount", "mandatory_depends_on": "eval:doc.is_variable_pay", "non_negative": 1}, {"depends_on": "eval:doc.is_variable_pay", "fieldname": "variable_received", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Variable Received", "read_only": 1}, {"default": "0", "fieldname": "view_contract", "fieldtype": "Check", "label": "View Contract"}], "grid_page_length": 50, "istable": 1, "links": [], "modified": "2025-06-09 17:33:36.949904", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Project User", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}