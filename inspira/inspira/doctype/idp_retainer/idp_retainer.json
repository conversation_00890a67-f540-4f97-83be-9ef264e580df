{"actions": [], "allow_rename": 1, "autoname": "field:retainer_name", "creation": "2025-04-24 12:56:58.729177", "doctype": "DocType", "engine": "InnoDB", "field_order": ["retainer_name"], "fields": [{"fieldname": "retainer_name", "fieldtype": "Data", "label": "Retainer Name", "unique": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-24 12:57:20.032396", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Retainer", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}