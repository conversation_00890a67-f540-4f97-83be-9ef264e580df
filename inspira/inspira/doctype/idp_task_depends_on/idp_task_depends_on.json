{"actions": [], "creation": "2025-01-25 12:39:33.233007", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["task", "column_break_2", "subject", "project"], "fields": [{"fieldname": "task", "fieldtype": "Link", "in_list_view": 1, "label": "Task", "options": "IDP Task"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fetch_from": "task.subject", "fetch_if_empty": 1, "fieldname": "subject", "fieldtype": "Text", "in_list_view": 1, "label": "Subject", "read_only": 1}, {"fieldname": "project", "fieldtype": "Text", "label": "Project", "read_only": 1}], "istable": 1, "links": [], "modified": "2025-03-06 21:01:57.394760", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Task Depends On", "owner": "Administrator", "permissions": [], "sort_field": "modified", "sort_order": "DESC", "states": []}