{"actions": [], "allow_rename": 1, "creation": "2025-06-04 12:04:06.016779", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["user_id", "milestone", "hourly_seat_cost", "column_break_vxsf", "employee_id", "timesheet_hrs", "total_amount"], "fields": [{"fieldname": "milestone", "fieldtype": "Link", "in_list_view": 1, "label": "Milestone", "options": "IDP Task"}, {"fieldname": "hourly_seat_cost", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "<PERSON>ly Seat Cost"}, {"fieldname": "column_break_vxsf", "fieldtype": "Column Break"}, {"fieldname": "employee_id", "fieldtype": "Link", "in_list_view": 1, "label": "Employee Id", "options": "Employee"}, {"fieldname": "timesheet_hrs", "fieldtype": "Float", "in_list_view": 1, "label": "Timesheet Hrs"}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Total Amount"}, {"fieldname": "user_id", "fieldtype": "Link", "in_list_view": 1, "label": "User Id", "options": "User"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-06-04 12:37:03.717395", "modified_by": "Administrator", "module": "Inspira", "name": "IDP Employee Timesheet Costing", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}