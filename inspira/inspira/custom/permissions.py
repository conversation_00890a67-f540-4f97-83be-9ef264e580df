import frappe


def idp_project_has_permission(doc, user):
	if user == "Administrator" or "System Manager" in frappe.get_roles(user):
		return True
	if isinstance(doc.users, list):
		for u in doc.users:
			if u.user == user:
				return True
	return False


def idp_project_permission_query_conditions(user):
	if user == "Administrator" or "System Manager" in frappe.get_roles(user):
		return ""
	else:
		conditions = []

		conditions.append(
			f"""
        EXISTS (
            SELECT
                1
            FROM `tabIDP Project User` ipu
            WHERE
                ipu.parent = `tabIDP Project`.name
                AND ipu.user = '{user}'
        )
        """
		)

		return "({})".format(" OR ".join(conditions))
