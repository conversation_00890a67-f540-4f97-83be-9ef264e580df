import frappe
from datetime import datetime

def attendance_before_submit(doc,method=None):
    shift = frappe.db.get_value("Employee",doc.employee,"default_shift")
    if not doc.shift :
        # doc.shift = shift
        frappe.db.set_value("Attendance",doc.name,"shift",shift)
    if not doc.working_hours:
        time = frappe.db.get_value("Shift Type",shift,['start_time','end_time'])
        
        if time and time[0] and time[1]:
            start_time = datetime.strptime(str(time[0]), "%H:%M:%S")
            end_time = datetime.strptime(str(time[1]), "%H:%M:%S")

            # Handle overnight shifts (e.g. start at 22:00, end at 06:00 next day)
            if end_time < start_time:
                end_time = end_time.replace(day=start_time.day + 1)

            duration = (end_time - start_time).seconds / 3600  # in hours
            # frappe.msgprint(f"Shift duration is {duration} hours.")
            # doc.working_hours = duration
            frappe.db.set_value("Attendance",doc.name,"working_hours",duration)

        
def attendance_request_before_submit(doc,method=None):
    doc.approver = frappe.session.user
    doc.approved_on = frappe.utils.getdate()