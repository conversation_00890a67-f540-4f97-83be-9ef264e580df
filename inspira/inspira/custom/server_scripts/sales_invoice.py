import frappe

from inspira.inspira.doctype.idp_contract.idp_contract import update_milestone_payment_status



def si_before_submit(doc, method=None):
	# Check item amount should not be greater than milestone amount
	for item in doc.items:
		if not item.milestone:
			continue

		fee_amount, payment_received = frappe.db.get_value("IDP Milestones", item.milestone, ["fee_amount", "payment_received"])
		remaining_amount = fee_amount - (payment_received or 0)
		if item.base_net_amount > remaining_amount:
			frappe.throw(f"Row {item.idx}: Item amount {frappe.format(item.base_net_amount, 'Currency')} is greater than remaining milestone amount {frappe.format(remaining_amount, 'Currency')}")

def si_contract_milestone_update(doc, method=None):
	if not doc.contract:
		return

	for item in doc.items:
		if not item.milestone:
			continue

		# Fetch all posting dates directly in one query
		invoice_dates = frappe.get_all(
			"Sales Invoice",
			filters={
				"name": ["in", frappe.get_all(
					"Sales Invoice Item",
					filters={"milestone": item.milestone, "docstatus": 1},
					pluck="parent"
				)]
			},
			fields=["posting_date"]
		)

		# Get latest posting date if any
		max_date = max([d.posting_date for d in invoice_dates], default=None)
		frappe.db.set_value("IDP Milestones", item.milestone, "invoice_date", max_date)
		update_milestone_payment_status(item.milestone)
