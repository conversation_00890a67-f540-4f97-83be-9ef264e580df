import frappe


@frappe.whitelist()
@frappe.validate_and_sanitize_search_inputs
def milestone_query(doctype, txt, searchfield, start, page_len, filters):
	query = f"""
		SELECT
			name,
			milestone
		FROM
			`tabIDP Milestones`
		WHERE
			parent = '{filters['parent']}'
	"""

	if txt:
		query += f" AND {searchfield} like '{txt}' "

	query += f" LIMIT {page_len} offset {start} "
	return frappe.db.sql(query)