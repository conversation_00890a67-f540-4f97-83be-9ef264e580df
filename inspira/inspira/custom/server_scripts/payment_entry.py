import frappe
from frappe.utils import flt, getdate

from inspira.inspira.doctype.idp_contract.idp_contract import update_milestone_payment_status


def pe_contract_milestone_update(doc, method=None):
    if doc.payment_type == "Receive" and doc.party_type == "Customer":
        if not doc.references:
            return
            
        for ref in doc.references:
            if ref.reference_doctype != "Sales Invoice":
                continue
                
            # Get the sales invoice items with milestones
            si_items = frappe.get_all(
                "Sales Invoice Item",
                filters={"parent": ref.reference_name, "milestone": ["is", "set"]},
                fields=["milestone", "base_net_amount", "parent"],
                order_by="idx"
            )
            
            if not si_items:
                continue
                
            # Calculate the payment amount for this reference
            payment_amount = flt(ref.allocated_amount)
            remaining_payment = payment_amount
            
            # Distribute payment to milestones using FIFO
            for item in si_items:
                if remaining_payment <= 0:
                    break
                    
                current_payment, fee_amount = frappe.get_value("IDP Milestones", item.milestone, ["payment_received", "fee_amount"])
                current_payment = flt(current_payment) or 0
                fee_amount = flt(fee_amount) or 0
                
                # Calculate how much can be allocated to this milestone
                # Cannot exceed the remaining unpaid amount of fee_amount
                max_allowable = fee_amount - current_payment
                allocatable_amount = min(remaining_payment, item.base_net_amount, max_allowable)
                
                if allocatable_amount <= 0:
                    continue
                
                # Calculate new values
                new_payment_received = current_payment + allocatable_amount
                new_payment_pending = fee_amount - new_payment_received
                
                # Get the latest payment date for this milestone
                latest_payment_date = get_latest_payment_date(item.milestone, doc.posting_date)
                
                # Update milestone payment received, pending and payment date
                frappe.db.set_value(
                    "IDP Milestones",
                    item.milestone,
                    {
                        "payment_received": new_payment_received,
                        "payment_pending": new_payment_pending,
                        "payment_date": latest_payment_date
                    },
                    update_modified=False
                )
                update_milestone_payment_status(item.milestone)
                
                remaining_payment -= allocatable_amount

def pe_contract_milestone_update_on_cancel(doc, method=None):
    if doc.payment_type == "Receive" and doc.party_type == "Customer":
        if not doc.references:
            return
            
        for ref in doc.references:
            if ref.reference_doctype != "Sales Invoice":
                continue
                
            # Get the sales invoice items with milestones
            si_items = frappe.get_all(
                "Sales Invoice Item",
                filters={"parent": ref.reference_name, "milestone": ["is", "set"]},
                fields=["milestone", "base_net_amount", "parent"],
                order_by="idx"
            )
            
            if not si_items:
                continue
                
            # Calculate the payment amount for this reference
            payment_amount = flt(ref.allocated_amount)
            remaining_payment = payment_amount
            
            # Reverse distribute payment from milestones using FIFO
            for item in si_items:
                if remaining_payment <= 0:
                    break
                    
                current_payment, fee_amount = frappe.get_value("IDP Milestones", item.milestone, ["payment_received", "fee_amount"])
                current_payment = flt(current_payment) or 0
                fee_amount = flt(fee_amount) or 0
                
                # Calculate how much was allocated to this milestone
                allocated_amount = min(remaining_payment, item.base_net_amount)
                
                # Ensure we don't subtract more than what was received
                allocated_amount = min(allocated_amount, current_payment)
                
                if allocated_amount <= 0:
                    continue
                
                # Calculate new values
                new_payment_received = current_payment - allocated_amount
                new_payment_pending = fee_amount - new_payment_received
                
                # Get the latest payment date after cancellation
                latest_payment_date = get_latest_payment_date(item.milestone)
                
                # Update milestone payment received, pending and payment date
                frappe.db.set_value(
                    "IDP Milestones",
                    item.milestone,
                    {
                        "payment_received": new_payment_received,
                        "payment_pending": new_payment_pending,
                        "payment_date": latest_payment_date
                    },
                    update_modified=False
                )
                update_milestone_payment_status(item.milestone)
                
                remaining_payment -= allocated_amount

def get_latest_payment_date(milestone_id, current_payment_date=None):
    """
    Get the latest payment date for a milestone by checking all related Payment Entries
    Args:
        milestone_id: ID of the milestone
        current_payment_date: Date of the current payment being processed (optional)
    Returns:
        Date: Latest payment date
    """
    # Get all payment entries for this milestone
    payment_dates = frappe.get_all(
        "Payment Entry Reference",
        filters={
            "docstatus": 1,
            "reference_doctype": "Sales Invoice",
            "reference_name": ["in", frappe.get_all(
                "Sales Invoice Item",
                filters={"milestone": milestone_id, "docstatus": 1},
                pluck="parent"
            )]
        },
        fields=["parent"],
        pluck="parent"
    )
    
    if payment_dates:
        # Get posting dates of all related payment entries
        dates = frappe.get_all(
            "Payment Entry",
            filters={
                "name": ["in", payment_dates],
                "docstatus": 1
            },
            fields=["posting_date"],
            pluck="posting_date"
        )
        
        # Add current payment date to the list if provided
        if current_payment_date:
            dates.append(getdate(current_payment_date))
            
        # Return the latest date if dates exist
        if dates:
            return max(dates)
    
    # Return current payment date if provided and no other dates found
    return current_payment_date if current_payment_date else None

@frappe.whitelist()
def create_variable_invoices(references):
    references = frappe.parse_json(references)
    allocation_messages = []

    for ref in references:
        if ref.get("reference_doctype") != "Sales Invoice":
            continue

        allocated_amount = ref.get("allocated_amount") or 0
        if allocated_amount <= 0:
            continue

        sales_invoice = ref.get("reference_name")
        contract = frappe.get_value("Sales Invoice", sales_invoice, "contract")
        project = frappe.get_value("IDP Project", {"contract": contract}, "name")

        users = frappe.get_all(
            "IDP Project User",
            filters={"parent": project},
            fields=["name", "user", "variable_", "variable_amount", "variable_received"],
            order_by="idx"
        )

        remaining_payment = allocated_amount
        for user in users:
            variable_amount = user.variable_amount or 0
            variable_received = user.variable_received or 0
            remaining_user_amount = variable_amount - variable_received

            if remaining_user_amount <= 0 or remaining_payment <= 0:
                continue

            amount_to_allocate = min(remaining_user_amount, remaining_payment)
            if amount_to_allocate <= 0:
                continue

            # Create Purchase Invoice
            pi = frappe.new_doc("Purchase Invoice")
            pi.supplier = "Supp 1"  # Replace with dynamic supplier if needed
            pi.contract = contract
            pi.variable_for = user.user
            pi.project_user_name = user.name
            pi.append("items", {
                "item_code": "Service Item",
                "qty": 1,
                "rate": amount_to_allocate
            })
            pi.save()
            pi.submit()

            # Update received amount
            new_received = variable_received + amount_to_allocate
            frappe.db.set_value("IDP Project User", user.name, "variable_received", new_received)

            # Update remaining payment
            remaining_payment -= amount_to_allocate

            # Add message
            allocation_messages.append(
                f"User: {user.user}, Allocated Amount: {amount_to_allocate}, "
                f"Purchase Invoice: {frappe.utils.get_link_to_form('Purchase Invoice', pi.name)}"
            )

    # Show allocation result
    if allocation_messages:
        frappe.msgprint("<br>".join(allocation_messages))
    else:
        frappe.msgprint("No allocation to be done.")
