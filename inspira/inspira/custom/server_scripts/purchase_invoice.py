import frappe

def pi_on_cancel(doc, method=None):
    # Updating the received amount in IDP Project User
    if not doc.project_user_name:
        return
    
    variable_received = frappe.db.get_value("IDP Project User", doc.project_user_name, "variable_received")
    if variable_received:
        new_received = variable_received - doc.grand_total
        frappe.db.set_value("IDP Project User", doc.project_user_name, "variable_received", new_received)
    