from datetime import datetime

import frappe
from frappe.utils import flt, fmt_money


def get_hrs_from_timedelta(timedelta_obj):
	return timedelta_obj.total_seconds() / 3600


def time_to_seconds(time: str) -> int:
    """Convert time string of format HH:MM:SS into seconds"""
    if type(time) != str:
        time = str(time)
    date_time = datetime.strptime(time.split(".")[0], "%H:%M:%S")
    return date_time - datetime(1900, 1, 1)


def get_label_from_fieldname(doctype: str, fieldname: str) -> str:
    """
    Return the label for a given fieldname on the specified doctype.
    """
    meta = frappe.get_meta(doctype)

    df = meta.get_field(fieldname)
    if not df:
        frappe.throw(f"Field {fieldname} not found on {doctype}")

    # Return its label
    return df.label


def get_status_name_from_id(status_id):
    return frappe.get_cached_value("IDP Status Master", status_id, "status")

def get_status_id_from_status_name(form, status):
    return frappe.get_cached_value("IDP Status Master", {"Form": form, "status": status}, "name")


def get_fmt_money(value, currency="INR"):
    ZERO_MONEY = fmt_money(0, currency="INR")
    return fmt_money(flt(value), currency=currency) if value else ZERO_MONEY


def get_fmt_percent(value):
    if not value:
         value = 0
    return frappe.format(value, 'Percent')