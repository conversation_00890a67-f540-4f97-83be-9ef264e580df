frappe.ui.form.on("Sales Invoice", {
	refresh(frm) {
		frm.set_query("contract", function () {
			return {
				filters: {
					party_name: frm.doc.customer,
				},
			};
		});

		frm.set_query("milestone", "items", function () {
			return {
				query: "inspira.inspira.custom.server_scripts.queries.milestone_query",
				filters: {
					"parent": frm.doc.contract
				}
			};
		});
	},

	customer(frm) {
		if (!frm._original_customer) {
			frm._original_customer = frm.doc.customer;
		}

		if (frm._original_customer && frm._original_customer !== frm.doc.customer) {
			frm.doc.contract = null;
			frm.refresh_field("contract");

			frm.doc?.items.forEach(item => {
				item.milestone = null; 
			 });
			 frm.refresh_field("items")
		}
		
		frm._original_customer = frm.doc.company;
	},

	contract(frm) {
		if (!frm._original_contract) {
			frm._original_contract = frm.doc.contract;
		}

		if (frm._original_contract && frm._original_contract !== frm.doc.contract) {
			frm.doc?.items.forEach(item => {
				item.milestone = null; 
			 });
			 frm.refresh_field("items")
		}
		
		frm._original_contract = frm.doc.company;
	}
});