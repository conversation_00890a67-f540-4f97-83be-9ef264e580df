
frappe.ui.form.on("Payment Entry", {
    refresh(frm) {
        if (frm.doc.docstatus == 1) {
            frm.trigger("add_buttons");
        }
    },

    add_buttons(frm) {
        frm.add_custom_button(
            __("Create Variable Invoices"),
            () => {
                frappe.call({
                    method: "inspira.inspira.custom.server_scripts.payment_entry.create_variable_invoices",
                    args: {
                        references: JSON.stringify(frm.doc.references)
                    },
                    callback: function (r) {
                        if (r && r.message) {
                            frm.refresh();
                        }
                    },
                });
            },
            __("Create")
        );
    }
});