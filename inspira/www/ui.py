# import frappe
# from frappe.utils import cint, get_system_timezone

# no_cache = 1


# def get_context():
# 	frappe.db.commit()
# 	context = frappe._dict()
# 	context.boot = get_boot()

# 	return context


# @frappe.whitelist(methods=["POST"], allow_guest=True)
# def get_context_for_dev():
# 	if not frappe.conf.developer_mode:
# 		frappe.throw("This method is only meant for developer mode")
# 	return get_boot()


# def get_boot():
# 	emp_id, designation = frappe.db.get_value(
# 		"Employee", {"user_id": frappe.session.user}, ["name", "designation"]
# 	)
# 	return frappe._dict(
# 		{
# 			"frappe_version": frappe.__version__,
# 			"csrf_token": frappe.sessions.get_csrf_token(),
# 			"setup_complete": cint(frappe.get_system_settings("setup_complete")),
# 			"sysdefaults": frappe.defaults.get_defaults(),
# 			"emp_id": emp_id,
# 			"designation": designation,
# 			"timezone": {
# 				"system": get_system_timezone(),
# 				"user": frappe.db.get_value("User", frappe.session.user, "time_zone")
# 				or get_system_timezone(),
# 			},
# 		}
# 	)


import frappe
from frappe.utils import cint, get_system_timezone

from inspira.inspira.api.hrms.hr_utils import get_reportees

no_cache = 1

def get_context():
    frappe.db.commit()
    context = frappe._dict()
    context.boot = get_boot()
    return context


@frappe.whitelist(methods=["POST"], allow_guest=True)
def get_context_for_dev():
    if not frappe.conf.developer_mode:
        frappe.throw("This method is only meant for developer mode")
    return get_boot()


def get_boot():
    result = frappe.db.get_value("Employee", {"user_id": frappe.session.user}, ["name", "designation"])
    emp_id, designation = result if result else (None, None)
    user_name = frappe.db.get_value("User",frappe.session.user,"full_name")
    reportees = get_reportees(frappe.session.user)
    return frappe._dict(
        {
            "frappe_version": frappe.__version__,
            "csrf_token": frappe.sessions.get_csrf_token(),
            "setup_complete": cint(frappe.get_system_settings("setup_complete")),
            "sysdefaults": frappe.defaults.get_defaults(),
            "emp_id": emp_id,
            "site_url": frappe.utils.get_url(),
            "designation": designation,
            "timezone": {
                "system": get_system_timezone(),
                "user": frappe.db.get_value("User", frappe.session.user, "time_zone") or get_system_timezone(),
            },
            "user_name": user_name,
            "is_manager": len(reportees) > 0
        }
    )
